/**
 * @file database_loader.h
 * @brief Database loading functionality for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 * @version 1.0
 * 
 * @section overview Overview
 * This header provides database loading functionality for the OMOP ETL pipeline,
 * including bulk loading, transaction management, and database-specific optimizations.
 * 
 * @section design Design Principles
 * - Performance: Bulk loading and buffering
 * - Reliability: Transaction management
 * - Flexibility: Database-specific optimizations
 * - Thread Safety: Concurrent loading support
 * - Error Handling: Comprehensive error tracking
 * 
 * @section components Components
 * - DatabaseLoaderOptions: Loading configuration
 * - BulkInsertBuffer: Record buffering
 * - DatabaseLoader: Core loading functionality
 * - ParallelDatabaseLoader: Concurrent loading
 * 
 * @section usage Usage
 * To use the database loader:
 * 1. Configure loader options
 * 2. Initialize with database connection
 * 3. Load records or batches
 * 4. Commit or rollback changes
 * 
 * @section example Example
 * @code
 * auto loader = DatabaseLoader::create("postgresql", connection);
 * loader->initialize(config);
 * loader->load_batch(batch, context);
 * loader->commit(context);
 * @endcode
 */

#pragma once

#include "core/interfaces.h"
#include "extract/database_connector.h"
#include "cdm/omop_tables.h"
#include <queue>
#include <atomic>
#include <thread>
#include <condition_variable>
#include <shared_mutex>
#include <unordered_set>
#include <functional>

namespace omop::load {

/**
 * @brief Database loader configuration options
 * 
 * @section description Description
 * Configures database loading behavior, including batch sizes,
 * transaction settings, and database-specific optimizations.
 * 
 * @section fields Fields
 * - batch_size: Records per batch
 * - commit_interval: Records per commit
 * - use_bulk_insert: Bulk insert flag
 * - truncate_before_load: Truncate flag
 * - disable_constraints: Constraint flag
 * - create_indexes_after_load: Index flag
 * - lock_timeout: Lock timeout
 * - temp_table_prefix: Temp table prefix
 * - use_copy_command: Copy command flag
 * - null_string: NULL representation
 * - delimiter: Field delimiter
 */
struct DatabaseLoaderOptions {
    size_t batch_size{1000};  ///< Records per batch
    size_t commit_interval{5000};  ///< Records per commit
    bool use_bulk_insert{true};  ///< Bulk insert flag
    bool truncate_before_load{false};  ///< Truncate flag
    bool disable_constraints{false};  ///< Constraint flag
    bool create_indexes_after_load{false};  ///< Index flag
    std::chrono::seconds lock_timeout{30};  ///< Lock timeout
    std::string temp_table_prefix{"tmp_"};  ///< Temp table prefix
    bool use_copy_command{true};  ///< Copy command flag
    std::string null_string{"\\N"};  ///< NULL representation
    char delimiter{'\t'};  ///< Field delimiter
};

/**
 * @brief Bulk insert buffer
 * 
 * @section description Description
 * Manages buffering of records for efficient bulk insertion,
 * providing capacity management and record access.
 * 
 * @section features Features
 * - Record buffering
 * - Capacity management
 * - Table association
 * - Thread safety
 */
class BulkInsertBuffer {
public:
    /**
     * @brief Constructor
     * @param table_name Target table name
     * @param capacity Buffer capacity
     * 
     * @section details Implementation Details
     * - Sets table name
     * - Sets capacity
     * - Reserves space
     * - Thread-safe
     */
    BulkInsertBuffer(const std::string& table_name, size_t capacity)
        : table_name_(table_name), capacity_(capacity) {
        records_.reserve(capacity);
    }

    /**
     * @brief Add record to buffer
     * @param record Record to add
     * @return bool True if buffer is full
     * 
     * @section details Implementation Details
     * - Adds record
     * - Checks capacity
     * - Thread-safe
     */
    bool add(const core::Record& record) {
        records_.push_back(record);
        return records_.size() >= capacity_;
    }

    /**
     * @brief Get buffered records
     * @return const std::vector<core::Record>& Records
     * 
     * @section details Implementation Details
     * - Returns records
     * - Thread-safe
     */
    [[nodiscard]] const std::vector<core::Record>& records() const {
        return records_;
    }

    /**
     * @brief Get buffer size
     * @return size_t Number of records
     * 
     * @section details Implementation Details
     * - Returns size
     * - Thread-safe
     */
    [[nodiscard]] size_t size() const { return records_.size(); }

    /**
     * @brief Check if buffer is empty
     * @return bool True if empty
     * 
     * @section details Implementation Details
     * - Checks emptiness
     * - Thread-safe
     */
    [[nodiscard]] bool empty() const { return records_.empty(); }

    /**
     * @brief Clear buffer
     * 
     * @section details Implementation Details
     * - Clears records
     * - Thread-safe
     */
    void clear() { records_.clear(); }

    /**
     * @brief Get table name
     * @return const std::string& Table name
     * 
     * @section details Implementation Details
     * - Returns name
     * - Thread-safe
     */
    [[nodiscard]] const std::string& table_name() const { return table_name_; }

private:
    std::string table_name_;  ///< Target table name
    size_t capacity_;  ///< Buffer capacity
    std::vector<core::Record> records_;  ///< Buffered records
};

/**
 * @brief Database loader base class
 * 
 * @section description Description
 * Provides common functionality for loading data into databases,
 * including bulk loading, transaction management, and optimizations.
 * 
 * @section features Features
 * - Bulk loading
 * - Transaction management
 * - Buffer management
 * - Constraint handling
 * - Index management
 * - Thread safety
 */
class DatabaseLoader : public core::ILoader {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     * @param options Loader options
     * 
     * @section details Implementation Details
     * - Stores connection
     * - Sets options
     * - Thread-safe
     */
    DatabaseLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                   DatabaseLoaderOptions options = {});

    /**
     * @brief Destructor
     * 
     * @section details Implementation Details
     * - Flushes buffers
     * - Thread-safe
     */
    ~DatabaseLoader() override;

    /**
     * @brief Initialize the loader
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Prepares tables
     * - Sets up buffers
     * - Thread-safe
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     * 
     * @section details Implementation Details
     * - Buffers record
     * - Handles errors
     * - Thread-safe
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     * 
     * @section details Implementation Details
     * - Loads records
     * - Handles errors
     * - Thread-safe
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Flushes buffers
     * - Commits transaction
     * - Thread-safe
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Clears buffers
     * - Rolls back transaction
     * - Thread-safe
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "database"; }

    /**
     * @brief Finalize loading
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Flushes buffers
     * - Creates indexes
     * - Thread-safe
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get loading statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     * 
     * @section details Implementation Details
     * - Returns stats
     * - Thread-safe
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Prepare table for loading
     * @param table_name Table name
     * 
     * @section details Implementation Details
     * - Truncates table
     * - Disables constraints
     * - Thread-safe
     */
    virtual void prepare_table(const std::string& table_name);

    /**
     * @brief Execute bulk insert
     * @param buffer Bulk insert buffer
     * @return size_t Number of inserted records
     * 
     * @section details Implementation Details
     * - Inserts records
     * - Handles errors
     * - Thread-safe
     */
    virtual size_t execute_bulk_insert(const BulkInsertBuffer& buffer);

    /**
     * @brief Execute single insert
     * @param table_name Table name
     * @param record Record to insert
     * @return bool True if successful
     * 
     * @section details Implementation Details
     * - Inserts record
     * - Handles errors
     * - Thread-safe
     */
    virtual bool execute_single_insert(const std::string& table_name,
                                      const core::Record& record);

    /**
     * @brief Build insert statement
     * @param table_name Table name
     * @param record Sample record
     * @return std::string INSERT statement
     * 
     * @section details Implementation Details
     * - Builds statement
     * - Thread-safe
     */
    virtual std::string build_insert_statement(const std::string& table_name,
                                             const core::Record& record);

    /**
     * @brief Flush all buffers
     * 
     * @section details Implementation Details
     * - Flushes buffers
     * - Thread-safe
     */
    void flush_all_buffers();

    /**
     * @brief Get or create buffer for table
     * @param table_name Table name
     * @return BulkInsertBuffer& Buffer reference
     * 
     * @section details Implementation Details
     * - Gets buffer
     * - Creates if needed
     * - Thread-safe
     */
    BulkInsertBuffer& get_buffer(const std::string& table_name);

    /**
     * @brief Index definition
     * 
     * @section description Description
     * Defines a database index with name and columns.
     * 
     * @section fields Fields
     * - name: Index name
     * - columns: Index columns
     */
    struct IndexDefinition {
        std::string name;  ///< Index name
        std::string columns;  ///< Index columns
    };

    /**
     * @brief Get OMOP standard indexes for table
     * @param table_name Table name
     * @return std::vector<IndexDefinition> Index definitions
     * 
     * @section details Implementation Details
     * - Returns indexes
     * - Thread-safe
     */
    std::vector<IndexDefinition> get_omop_indexes(const std::string& table_name);

    /**
     * @brief Disable constraints for table
     * @param table_name Table name
     * 
     * @section details Implementation Details
     * - Disables constraints
     * - Thread-safe
     */
    void disable_constraints(const std::string& table_name);

    /**
     * @brief Enable constraints for table
     * @param table_name Table name
     * 
     * @section details Implementation Details
     * - Enables constraints
     * - Thread-safe
     */
    void enable_constraints(const std::string& table_name);

    /**
     * @brief Create deferred indexes for table
     * @param table_name Table name
     * 
     * @section details Implementation Details
     * - Creates indexes
     * - Thread-safe
     */
    void create_deferred_indexes(const std::string& table_name);

protected:
    std::unique_ptr<extract::IDatabaseConnection> connection_;  ///< Database connection
    DatabaseLoaderOptions options_;  ///< Loader options
    std::unordered_map<std::string, BulkInsertBuffer> buffers_;  ///< Table buffers
    mutable std::shared_mutex buffer_mutex_;  ///< Buffer mutex
    std::atomic<size_t> total_loaded_{0};  ///< Total loaded records
    std::atomic<size_t> total_failed_{0};  ///< Total failed records
    std::atomic<size_t> total_committed_{0};  ///< Total committed records
    std::atomic<size_t> total_rolled_back_{0};  ///< Total rolled back records
};

/**
 * @brief PostgreSQL-specific loader
 *
 * Optimized loader for PostgreSQL with COPY command support.
 */
class PostgreSQLLoader : public DatabaseLoader {
public:
    /**
     * @brief Constructor
     * @param connection PostgreSQL connection
     * @param options Loader options
     */
    PostgreSQLLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                     DatabaseLoaderOptions options = {})
        : DatabaseLoader(std::move(connection), options) {}

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "postgresql"; }

protected:
    /**
     * @brief Execute bulk insert using COPY command
     * @param buffer Bulk insert buffer
     * @return size_t Number of inserted records
     */
    size_t execute_bulk_insert(const BulkInsertBuffer& buffer) override;

    /**
     * @brief Prepare COPY data
     * @param records Records to format
     * @return std::string COPY-formatted data
     */
    std::string prepare_copy_data(const std::vector<core::Record>& records);
};

/**
 * @brief MySQL-specific loader
 *
 * Optimized loader for MySQL with LOAD DATA support.
 */
class MySQLLoader : public DatabaseLoader {
public:
    /**
     * @brief Constructor
     * @param connection MySQL connection
     * @param options Loader options
     */
    MySQLLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                DatabaseLoaderOptions options = {})
        : DatabaseLoader(std::move(connection), options) {}

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "mysql"; }

protected:
    /**
     * @brief Execute bulk insert using LOAD DATA
     * @param buffer Bulk insert buffer
     * @return size_t Number of inserted records
     */
    size_t execute_bulk_insert(const BulkInsertBuffer& buffer) override;
};

/**
 * @brief OMOP-specific database loader
 *
 * Specialized loader that understands OMOP CDM structure and handles
 * table-specific loading requirements.
 */
class OmopDatabaseLoader : public DatabaseLoader {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     * @param options Loader options
     */
    OmopDatabaseLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                       DatabaseLoaderOptions options = {});

    /**
     * @brief Initialize with OMOP table
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "omop_database"; }

protected:
    /**
     * @brief Convert record to OMOP table object
     * @param record Generic record
     * @param table_name OMOP table name
     * @return std::unique_ptr<cdm::OmopTable> OMOP table object
     */
    std::unique_ptr<cdm::OmopTable> convert_to_omop_table(
        const core::Record& record,
        const std::string& table_name);

    /**
     * @brief Validate OMOP constraints
     * @param table OMOP table object
     * @return bool True if valid
     */
    bool validate_omop_constraints(const cdm::OmopTable& table);

    /**
     * @brief Handle foreign key constraints
     * @param table_name Table name
     * @param enable Whether to enable constraints
     */
    void handle_foreign_key_constraints(const std::string& table_name, bool enable);

    /**
     * @brief Create indexes for table
     * @param table_name Table name
     */
    void create_table_indexes(const std::string& table_name);

private:
    std::string current_omop_table_;
    bool validate_foreign_keys_{true};
    bool create_missing_tables_{false};

    // Cache for foreign key validation
    std::unordered_set<int64_t> person_id_cache_;
    std::unordered_set<int64_t> visit_id_cache_;
    std::unordered_set<int32_t> concept_id_cache_;
    mutable std::shared_mutex cache_mutex_;
};

/**
 * @brief Parallel database loader
 * 
 * @section description Description
 * Extends database loader with parallel loading capabilities,
 * using multiple worker threads for improved performance.
 * 
 * @section features Features
 * - Parallel loading
 * - Worker management
 * - Load balancing
 * - Thread safety
 */
class ParallelDatabaseLoader : public DatabaseLoader {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     * @param options Loader options
     * @param num_workers Number of worker threads
     * 
     * @section details Implementation Details
     * - Sets workers
     * - Thread-safe
     */
    ParallelDatabaseLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                          DatabaseLoaderOptions options = {},
                          size_t num_workers = 4);

    /**
     * @brief Destructor
     * 
     * @section details Implementation Details
     * - Stops workers
     * - Thread-safe
     */
    ~ParallelDatabaseLoader() override;

    /**
     * @brief Initialize the loader
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Starts workers
     * - Thread-safe
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     * 
     * @section details Implementation Details
     * - Queues record
     * - Thread-safe
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     * 
     * @section details Implementation Details
     * - Queues batch
     * - Thread-safe
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Waits for workers
     * - Commits changes
     * - Thread-safe
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Stops workers
     * - Rolls back changes
     * - Thread-safe
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Finalize loading
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Stops workers
     * - Finalizes loading
     * - Thread-safe
     */
    void finalize(core::ProcessingContext& context) override;

protected:
    /**
     * @brief Worker thread function
     * @param worker_id Worker identifier
     * 
     * @section details Implementation Details
     * - Processes records
     * - Thread-safe
     */
    void worker_thread(size_t worker_id);

private:
    size_t num_workers_;  ///< Number of workers
    std::vector<std::thread> workers_;  ///< Worker threads
    std::queue<core::Record> record_queue_;  ///< Record queue
    std::condition_variable queue_cv_;  ///< Queue condition
    std::mutex queue_mutex_;  ///< Queue mutex
    std::atomic<bool> stop_workers_{false};  ///< Stop flag
};

/**
 * @brief Create database loader
 * @param type Database type
 * @param connection Database connection
 * @param options Loader options
 * @return std::unique_ptr<DatabaseLoader> Loader instance
 * 
 * @section details Implementation Details
 * - Creates loader
 * - Thread-safe
 */
std::unique_ptr<DatabaseLoader> create_database_loader(
    const std::string& type,
    std::unique_ptr<extract::IDatabaseConnection> connection,
    const DatabaseLoaderOptions& options = {});

/**
 * @brief Register database loaders
 * 
 * @section details Implementation Details
 * - Registers loaders
 * - Thread-safe
 */
void register_database_loaders();

} // namespace omop::load