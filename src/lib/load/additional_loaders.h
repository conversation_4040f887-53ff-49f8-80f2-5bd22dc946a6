/**
 * @file additional_loaders.h
 * @brief Additional loader implementations for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 * @version 1.0
 * 
 * @section overview Overview
 * This header provides specialized loader implementations for various output formats
 * and destinations, including JSON, HTTP/REST APIs, and cloud storage.
 * 
 * @section design Design Principles
 * - Flexibility: Multiple output formats
 * - Reliability: Retry mechanisms and error handling
 * - Performance: Efficient data transfer
 * - Extensibility: Easy to add new formats
 * - Thread Safety: Concurrent operation support
 * 
 * @section components Components
 * - JsonBatchLoader: JSON file output
 * - HttpLoader: HTTP/REST API output
 * - MultiFormatLoader: Multiple format support
 * - S3Loader: AWS S3 storage
 * 
 * @section usage Usage
 * To use additional loaders:
 * 1. Configure loader-specific options
 * 2. Initialize with desired settings
 * 3. Load records or batches
 * 4. Handle output in target format
 * 
 * @section example Example
 * @code
 * auto json_loader = JsonBatchLoader(options, json_options);
 * json_loader->initialize(config);
 * json_loader->load_batch(batch, context);
 * @endcode
 */

#pragma once

#include "load/batch_loader.h"
#include "load/loader_base.h"
#include <nlohmann/json.hpp>

namespace omop::load {

/**
 * @brief JSON batch loader
 * 
 * @section description Description
 * Specialized batch loader for JSON file output with support for
 * nested structures and pretty printing.
 * 
 * @section features Features
 * - JSON formatting
 * - Pretty printing
 * - Metadata support
 * - Array/NDJSON output
 * - Date formatting
 */
class JsonBatchLoader : public BatchLoader {
public:
    /**
     * @brief JSON output configuration
     * 
     * @section description Description
     * Configures JSON output formatting and structure.
     * 
     * @section fields Fields
     * - pretty_print: Pretty printing flag
     * - indent_size: Indentation size
     * - include_metadata: Metadata flag
     * - array_output: Array/NDJSON flag
     * - date_format: Date format string
     */
    struct JsonOptions {
        bool pretty_print{true};  ///< Enable pretty printing
        int indent_size{2};  ///< Indentation size
        bool include_metadata{true};  ///< Include metadata
        bool array_output{true};  ///< Array/NDJSON mode
        std::string date_format{"%Y-%m-%d %H:%M:%S"};  ///< Date format
    };

    /**
     * @brief Constructor
     * @param options Batch loader options
     * @param json_options JSON-specific options
     * 
     * @section details Implementation Details
     * - Sets options
     * - Thread-safe
     */
    JsonBatchLoader(BatchLoaderOptions options,
                    JsonOptions json_options);

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "json_batch"; }

protected:
    /**
     * @brief Process a batch by writing to JSON
     * @param batch Batch to process
     * @param context Processing context
     * @return size_t Number of successfully processed records
     * 
     * @section details Implementation Details
     * - Converts records
     * - Writes JSON
     * - Thread-safe
     */
    size_t process_batch(std::unique_ptr<EnhancedBatch> batch,
                        core::ProcessingContext& context) override;

    /**
     * @brief Initialize JSON batch loader
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Opens file
     * - Sets up JSON
     * - Thread-safe
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Finalize JSON output
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Closes array
     * - Closes file
     * - Thread-safe
     */
    void do_finalize(core::ProcessingContext& context) override;

private:
    /**
     * @brief Convert record to JSON object
     * @param record Record to convert
     * @return nlohmann::json JSON object
     * 
     * @section details Implementation Details
     * - Converts fields
     * - Thread-safe
     */
    nlohmann::json record_to_json(const core::Record& record);

    /**
     * @brief Convert std::any value to JSON
     * @param value Value to convert
     * @return nlohmann::json JSON value
     * 
     * @section details Implementation Details
     * - Converts value
     * - Thread-safe
     */
    nlohmann::json any_to_json(const std::any& value);

    JsonOptions json_options_;  ///< JSON options
    std::string output_file_;  ///< Output file path
    std::ofstream output_stream_;  ///< Output stream
    std::mutex output_mutex_;  ///< Output mutex
    bool first_batch_{true};  ///< First batch flag
    nlohmann::json json_array_;  ///< JSON array
};

/**
 * @brief HTTP/REST API loader
 * 
 * @section description Description
 * Network-based loader that sends data to HTTP endpoints with
 * support for authentication, retries, and compression.
 * 
 * @section features Features
 * - HTTP methods
 * - Authentication
 * - Retry mechanism
 * - Compression
 * - Batch sending
 */
class HttpLoader : public NetworkLoaderBase {
public:
    /**
     * @brief HTTP configuration
     * 
     * @section description Description
     * Configures HTTP request behavior and connection settings.
     * 
     * @section fields Fields
     * - method: HTTP method
     * - headers: Request headers
     * - content_type: Content type
     * - timeout_seconds: Timeout
     * - retry_count: Retry attempts
     * - retry_delay_ms: Retry delay
     * - use_compression: Compression flag
     * - auth_type: Auth type
     * - auth_credentials: Auth data
     */
    struct HttpOptions {
        std::string method{"POST"};  ///< HTTP method
        std::unordered_map<std::string, std::string> headers;  ///< Request headers
        std::string content_type{"application/json"};  ///< Content type
        size_t timeout_seconds{30};  ///< Timeout
        size_t retry_count{3};  ///< Retry attempts
        size_t retry_delay_ms{1000};  ///< Retry delay
        bool use_compression{true};  ///< Compression flag
        std::string auth_type;  ///< Auth type
        std::string auth_credentials;  ///< Auth data
    };

    /**
     * @brief Constructor
     * @param options HTTP options
     * 
     * @section details Implementation Details
     * - Sets options
     * - Thread-safe
     */
    explicit HttpLoader(HttpOptions options);

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     * 
     * @section details Implementation Details
     * - Buffers record
     * - Sends if threshold
     * - Thread-safe
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     * 
     * @section details Implementation Details
     * - Formats batch
     * - Sends request
     * - Thread-safe
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Sends buffer
     * - Thread-safe
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Clears buffer
     * - Thread-safe
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "http"; }

    /**
     * @brief Finalize the loader
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Sends buffer
     * - Thread-safe
     */
    void finalize(core::ProcessingContext& context) override;

protected:
    /**
     * @brief Connect to HTTP endpoint
     * @param endpoint Endpoint URL
     * @param timeout Connection timeout
     * 
     * @section details Implementation Details
     * - Connects
     * - Thread-safe
     */
    void connect(const std::string& endpoint,
                std::chrono::seconds timeout) override;

    /**
     * @brief Disconnect from endpoint
     * 
     * @section details Implementation Details
     * - Disconnects
     * - Thread-safe
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     * 
     * @section details Implementation Details
     * - Returns status
     * - Thread-safe
     */
    bool is_connected() const override;

    /**
     * @brief Send data over HTTP
     * @param data Data to send
     * @param timeout Send timeout
     * @return bool True if successful
     * 
     * @section details Implementation Details
     * - Sends data
     * - Thread-safe
     */
    bool send_data(const std::string& data,
                  std::chrono::seconds timeout) override;

private:
    /**
     * @brief Format batch as JSON payload
     * @param batch Batch to format
     * @return std::string JSON payload
     * 
     * @section details Implementation Details
     * - Formats batch
     * - Thread-safe
     */
    std::string format_batch_payload(const core::RecordBatch& batch);

    /**
     * @brief Send HTTP request with retries
     * @param payload Request payload
     * @return bool True if successful
     * 
     * @section details Implementation Details
     * - Sends request
     * - Handles retries
     * - Thread-safe
     */
    bool send_with_retry(const std::string& payload);

    HttpOptions http_options_;  ///< HTTP options
    std::atomic<bool> connected_{false};  ///< Connection status
    std::vector<core::Record> pending_records_;  ///< Pending records
    std::mutex pending_mutex_;  ///< Pending mutex
    size_t batch_threshold_{100};  ///< Batch threshold
};

/**
 * @brief Multi-format loader
 * 
 * @section description Description
 * Loader that can output to multiple formats simultaneously,
 * with support for weighted load distribution.
 * 
 * @section features Features
 * - Multiple formats
 * - Load distribution
 * - Statistics tracking
 * - Error handling
 */
class MultiFormatLoader : public LoaderBase {
public:
    /**
     * @brief Constructor
     * @param name Loader name
     * 
     * @section details Implementation Details
     * - Sets name
     * - Thread-safe
     */
    explicit MultiFormatLoader(const std::string& name = "multi_format");

    /**
     * @brief Add a sub-loader
     * @param loader Loader to add
     * @param weight Relative weight for load distribution
     * 
     * @section details Implementation Details
     * - Adds loader
     * - Thread-safe
     */
    void add_loader(std::unique_ptr<core::ILoader> loader, double weight = 1.0);

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded by all loaders
     * 
     * @section details Implementation Details
     * - Loads record
     * - Updates stats
     * - Thread-safe
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     * 
     * @section details Implementation Details
     * - Loads batch
     * - Updates stats
     * - Thread-safe
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Commits changes
     * - Thread-safe
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Rolls back changes
     * - Thread-safe
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "multi_format"; }

    /**
     * @brief Get number of sub-loaders
     * @return size_t Number of loaders
     * 
     * @section details Implementation Details
     * - Returns count
     * - Thread-safe
     */
    size_t loader_count() const { return loaders_.size(); }

protected:
    /**
     * @brief Initialize multi-format loader
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Initializes loaders
     * - Thread-safe
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Finalize multi-format loader
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Finalizes loaders
     * - Thread-safe
     */
    void do_finalize(core::ProcessingContext& context) override;

private:
    /**
     * @brief Loader information
     * 
     * @section description Description
     * Tracks loader state and statistics.
     * 
     * @section fields Fields
     * - loader: Loader instance
     * - weight: Load weight
     * - success_count: Success count
     * - failure_count: Failure count
     */
    struct LoaderInfo {
        std::unique_ptr<core::ILoader> loader;  ///< Loader instance
        double weight;  ///< Load weight
        size_t success_count{0};  ///< Success count
        size_t failure_count{0};  ///< Failure count
    };

    std::vector<LoaderInfo> loaders_;  ///< Sub-loaders
    std::mutex loaders_mutex_;  ///< Loaders mutex
};

/**
 * @brief AWS S3 loader
 * 
 * @section description Description
 * Loader that writes data to AWS S3 storage with support for
 * multipart uploads and compression.
 * 
 * @section features Features
 * - S3 integration
 * - Multipart uploads
 * - Compression
 * - Retry mechanism
 */
class S3Loader : public NetworkLoaderBase {
public:
    /**
     * @brief S3 configuration
     * 
     * @section description Description
     * Configures S3 connection and upload behavior.
     * 
     * @section fields Fields
     * - region: AWS region
     * - bucket_name: S3 bucket
     * - key_prefix: Key prefix
     * - use_compression: Compression flag
     * - part_size: Part size
     * - max_retries: Retry attempts
     */
    struct S3Options {
        std::string region;  ///< AWS region
        std::string bucket_name;  ///< S3 bucket
        std::string key_prefix;  ///< Key prefix
        bool use_compression{true};  ///< Compression flag
        size_t part_size{5 * 1024 * 1024};  ///< Part size
        size_t max_retries{3};  ///< Retry attempts
    };

    /**
     * @brief Constructor
     * @param options S3 options
     * 
     * @section details Implementation Details
     * - Sets options
     * - Thread-safe
     */
    explicit S3Loader(S3Options options);

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     * 
     * @section details Implementation Details
     * - Buffers record
     * - Uploads if threshold
     * - Thread-safe
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     * 
     * @section details Implementation Details
     * - Formats batch
     * - Uploads to S3
     * - Thread-safe
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Uploads buffer
     * - Thread-safe
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Clears buffer
     * - Thread-safe
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "s3"; }

    /**
     * @brief Finalize the loader
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Completes upload
     * - Thread-safe
     */
    void finalize(core::ProcessingContext& context) override;

protected:
    /**
     * @brief Connect to S3
     * @param endpoint Endpoint URL
     * @param timeout Connection timeout
     * 
     * @section details Implementation Details
     * - Connects
     * - Thread-safe
     */
    void connect(const std::string& endpoint,
                std::chrono::seconds timeout) override;

    /**
     * @brief Disconnect from S3
     * 
     * @section details Implementation Details
     * - Disconnects
     * - Thread-safe
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     * 
     * @section details Implementation Details
     * - Returns status
     * - Thread-safe
     */
    bool is_connected() const override;

    /**
     * @brief Send data to S3
     * @param data Data to send
     * @param timeout Send timeout
     * @return bool True if successful
     * 
     * @section details Implementation Details
     * - Uploads data
     * - Thread-safe
     */
    bool send_data(const std::string& data,
                  std::chrono::seconds timeout) override;

private:
    /**
     * @brief Generate S3 object key
     * @param suffix Key suffix
     * @return std::string Full object key
     * 
     * @section details Implementation Details
     * - Generates key
     * - Thread-safe
     */
    std::string generate_object_key(const std::string& suffix = "");

    /**
     * @brief Upload buffer to S3
     * @param key Object key
     * @param data Data to upload
     * @return bool True if successful
     * 
     * @section details Implementation Details
     * - Uploads data
     * - Thread-safe
     */
    bool upload_to_s3(const std::string& key, const std::string& data);

    /**
     * @brief Start multipart upload
     * @param key Object key
     * @return std::string Upload ID
     * 
     * @section details Implementation Details
     * - Starts upload
     * - Thread-safe
     */
    std::string start_multipart_upload(const std::string& key);

    /**
     * @brief Upload part
     * @param key Object key
     * @param upload_id Upload ID
     * @param part_number Part number
     * @param data Part data
     * @return std::string ETag
     * 
     * @section details Implementation Details
     * - Uploads part
     * - Thread-safe
     */
    std::string upload_part(const std::string& key,
                           const std::string& upload_id,
                           int part_number,
                           const std::string& data);

    /**
     * @brief Complete multipart upload
     * @param key Object key
     * @param upload_id Upload ID
     * @param parts Part ETags
     * @return bool True if successful
     * 
     * @section details Implementation Details
     * - Completes upload
     * - Thread-safe
     */
    bool complete_multipart_upload(const std::string& key,
                                  const std::string& upload_id,
                                  const std::vector<std::pair<int, std::string>>& parts);

    S3Options s3_options_;  ///< S3 options
    std::string current_key_;  ///< Current key
    std::string current_upload_id_;  ///< Current upload ID
    std::vector<std::pair<int, std::string>> uploaded_parts_;  ///< Uploaded parts
    std::ostringstream buffer_;  ///< Data buffer
    size_t buffer_size_{0};  ///< Buffer size
    int next_part_number_{1};  ///< Next part number
};

} // namespace omop::load