/**
 * @file loader_base.h
 * @brief Base classes for data loaders in OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header provides base classes for implementing data loaders in the OMOP ETL pipeline,
 * including common functionality for file-based and network-based loaders.
 * 
 * @section design Design Principles
 * - Inheritance: Base class hierarchy
 * - Thread Safety: Atomic counters and mutexes
 * - Error Handling: Comprehensive error tracking
 * - Statistics: Performance and progress tracking
 * - Configuration: Type-safe configuration management
 * 
 * @section components Components
 * - LoaderBase: Abstract base class for all loaders
 * - FileLoaderBase: Base class for file-based loaders
 * - NetworkLoaderBase: Base class for network-based loaders
 * 
 * @section usage Usage
 * To implement a new loader:
 * 1. Inherit from appropriate base class
 * 2. Implement required virtual methods
 * 3. Use protected helper methods
 * 4. Track statistics and errors
 * 
 * @section example Example
 * @code
 * class MyLoader : public LoaderBase {
 * protected:
 *     void do_initialize(const Config& config) override {
 *         // Initialize loader
 *     }
 * };
 * @endcode
 */

#pragma once

#include "core/interfaces.h"
#include "common/exceptions.h"
#include <chrono>
#include <atomic>
#include <unordered_map>
#include <any>
#include <fstream>
#include <mutex>
#include <vector>

namespace omop::load {

/**
 * @brief Base class for all loaders
 * 
 * @section description Description
 * Provides common functionality and statistics tracking for data loaders,
 * implementing the ILoader interface and providing shared functionality
 * for derived loader classes.
 * 
 * @section features Features
 * - Initialization management
 * - Statistics tracking
 * - Error handling
 * - Progress monitoring
 * - Configuration management
 * - Thread safety
 */
class LoaderBase : public core::ILoader {
public:
    /**
     * @brief Constructor
     * @param name Loader name for identification
     * 
     * @section details Implementation Details
     * - Sets name
     * - Initializes counters
     * - Thread-safe
     */
    explicit LoaderBase(const std::string& name);

    /**
     * @brief Virtual destructor
     * 
     * @section details Implementation Details
     * - Default implementation
     * - Thread-safe
     */
    virtual ~LoaderBase() = default;

    /**
     * @brief Initialize the loader
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Validates config
     * - Calls do_initialize
     * - Sets start time
     * - Thread-safe
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Get loader statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     * 
     * @section details Implementation Details
     * - Combines base stats
     * - Adds derived stats
     * - Thread-safe
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

    /**
     * @brief Finalize the loader
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Calls do_finalize
     * - Sets end time
     * - Thread-safe
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get loader name
     * @return const std::string& Loader name
     * 
     * @section details Implementation Details
     * - Returns name
     * - Thread-safe
     */
    const std::string& get_name() const { return name_; }

    /**
     * @brief Check if loader is initialized
     * @return bool True if initialized
     * 
     * @section details Implementation Details
     * - Returns state
     * - Thread-safe
     */
    bool is_initialized() const { return initialized_; }

protected:
    /**
     * @brief Perform loader-specific initialization
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Pure virtual
     * - Must be implemented
     * - Thread-safe
     */
    virtual void do_initialize(const std::unordered_map<std::string, std::any>& config,
                              core::ProcessingContext& context) = 0;

    /**
     * @brief Perform loader-specific finalization
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Optional override
     * - Thread-safe
     */
    virtual void do_finalize(core::ProcessingContext& context) {}

    /**
     * @brief Get loader-specific statistics
     * @return std::unordered_map<std::string, std::any> Additional statistics
     * 
     * @section details Implementation Details
     * - Optional override
     * - Thread-safe
     */
    virtual std::unordered_map<std::string, std::any> get_additional_statistics() const {
        return {};
    }

    /**
     * @brief Update loading progress
     * @param loaded Number of successfully loaded records
     * @param failed Number of failed records
     * 
     * @section details Implementation Details
     * - Updates counters
     * - Thread-safe
     */
    void update_progress(size_t loaded, size_t failed = 0);

    /**
     * @brief Record an error
     * @param error_message Error message
     * @param record_info Optional record information
     * 
     * @section details Implementation Details
     * - Adds error
     * - Thread-safe
     */
    void record_error(const std::string& error_message, 
                     const std::string& record_info = "");

    /**
     * @brief Get elapsed time since initialization
     * @return std::chrono::duration<double> Elapsed time in seconds
     * 
     * @section details Implementation Details
     * - Calculates duration
     * - Thread-safe
     */
    std::chrono::duration<double> get_elapsed_time() const;

    /**
     * @brief Check if configuration contains a key
     * @param config Configuration map
     * @param key Key to check
     * @return bool True if key exists
     * 
     * @section details Implementation Details
     * - Checks existence
     * - Thread-safe
     */
    bool has_config_key(const std::unordered_map<std::string, std::any>& config,
                       const std::string& key) const;

    /**
     * @brief Get configuration value with type checking
     * @tparam T Expected value type
     * @param config Configuration map
     * @param key Configuration key
     * @param default_value Default value if key not found
     * @return T Configuration value
     * 
     * @section details Implementation Details
     * - Type checks
     * - Returns default
     * - Thread-safe
     */
    template<typename T>
    T get_config_value(const std::unordered_map<std::string, std::any>& config,
                      const std::string& key,
                      const T& default_value) const {
        auto it = config.find(key);
        if (it != config.end()) {
            try {
                return std::any_cast<T>(it->second);
            } catch (const std::bad_any_cast& e) {
                throw common::ConfigurationException(
                    std::format("Invalid type for configuration key '{}': {}", key, e.what()));
            }
        }
        return default_value;
    }

private:
    std::string name_;  ///< Loader name
    bool initialized_{false};  ///< Initialization state
    
    // Statistics tracking
    std::atomic<size_t> total_loaded_{0};  ///< Total loaded records
    std::atomic<size_t> total_failed_{0};  ///< Total failed records
    std::atomic<size_t> total_processed_{0};  ///< Total processed records
    std::chrono::steady_clock::time_point start_time_;  ///< Start time
    std::chrono::steady_clock::time_point end_time_;  ///< End time
    
    // Error tracking
    mutable std::mutex error_mutex_;  ///< Error mutex
    std::vector<std::pair<std::string, std::chrono::steady_clock::time_point>> errors_;  ///< Error list
    static constexpr size_t MAX_ERRORS_TO_TRACK = 100;  ///< Max errors to track
};

/**
 * @brief File-based loader base class
 * 
 * @section description Description
 * Base class for loaders that write to files (CSV, JSON, etc.),
 * providing file handling and buffering functionality.
 * 
 * @section features Features
 * - File management
 * - Buffered writing
 * - File statistics
 * - Thread safety
 */
class FileLoaderBase : public LoaderBase {
public:
    /**
     * @brief Constructor
     * @param name Loader name
     * @param file_extension Default file extension
     * 
     * @section details Implementation Details
     * - Sets name
     * - Sets extension
     * - Thread-safe
     */
    FileLoaderBase(const std::string& name, const std::string& file_extension);

    /**
     * @brief Destructor
     * 
     * @section details Implementation Details
     * - Closes file
     * - Thread-safe
     */
    ~FileLoaderBase() override;

protected:
    /**
     * @brief Perform file loader initialization
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Opens file
     * - Thread-safe
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Perform file loader finalization
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Closes file
     * - Thread-safe
     */
    void do_finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get additional statistics for file loader
     * @return std::unordered_map<std::string, std::any> File-specific statistics
     * 
     * @section details Implementation Details
     * - Returns stats
     * - Thread-safe
     */
    std::unordered_map<std::string, std::any> get_additional_statistics() const override;

    /**
     * @brief Open output file
     * @param filename File path
     * @param append Whether to append to existing file
     * 
     * @section details Implementation Details
     * - Opens file
     * - Thread-safe
     */
    virtual void open_file(const std::string& filename, bool append = false);

    /**
     * @brief Close output file
     * 
     * @section details Implementation Details
     * - Closes file
     * - Thread-safe
     */
    virtual void close_file();

    /**
     * @brief Write data to file
     * @param data Data to write
     * 
     * @section details Implementation Details
     * - Writes data
     * - Thread-safe
     */
    virtual void write_to_file(const std::string& data);

    /**
     * @brief Flush file buffer
     * 
     * @section details Implementation Details
     * - Flushes buffer
     * - Thread-safe
     */
    virtual void flush_file();

    /**
     * @brief Get output file path
     * @return const std::string& File path
     * 
     * @section details Implementation Details
     * - Returns path
     * - Thread-safe
     */
    const std::string& get_file_path() const { return file_path_; }

    /**
     * @brief Check if file is open
     * @return bool True if file is open
     * 
     * @section details Implementation Details
     * - Returns state
     * - Thread-safe
     */
    bool is_file_open() const { return file_stream_.is_open(); }

    /**
     * @brief Get file extension
     * @return const std::string& File extension
     * 
     * @section details Implementation Details
     * - Returns extension
     * - Thread-safe
     */
    const std::string& get_file_extension() const { return file_extension_; }

private:
    std::string file_extension_;  ///< File extension
    std::string file_path_;  ///< File path
    std::ofstream file_stream_;  ///< File stream
    std::atomic<size_t> bytes_written_{0};  ///< Bytes written
    mutable std::mutex file_mutex_;  ///< File mutex
};

/**
 * @brief Network-based loader base class
 * 
 * @section description Description
 * Base class for loaders that send data over network protocols,
 * providing connection management and network statistics.
 * 
 * @section features Features
 * - Connection management
 * - Network statistics
 * - Timeout handling
 * - Thread safety
 */
class NetworkLoaderBase : public LoaderBase {
public:
    /**
     * @brief Constructor
     * @param name Loader name
     * @param protocol Network protocol
     * 
     * @section details Implementation Details
     * - Sets name
     * - Sets protocol
     * - Thread-safe
     */
    NetworkLoaderBase(const std::string& name, const std::string& protocol);

    /**
     * @brief Destructor
     * 
     * @section details Implementation Details
     * - Closes connection
     * - Thread-safe
     */
    ~NetworkLoaderBase() override;

protected:
    /**
     * @brief Perform network loader initialization
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Connects to endpoint
     * - Thread-safe
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Connect to network endpoint
     * @param endpoint Network endpoint
     * @param timeout Connection timeout
     * 
     * @section details Implementation Details
     * - Pure virtual
     * - Must be implemented
     * - Thread-safe
     */
    virtual void connect(const std::string& endpoint,
                        std::chrono::seconds timeout = std::chrono::seconds(30)) = 0;

    /**
     * @brief Send data over network
     * @param data Data to send
     * @param timeout Send timeout
     * @return bool True if successful
     * 
     * @section details Implementation Details
     * - Pure virtual
     * - Must be implemented
     * - Thread-safe
     */
    virtual bool send_data(const std::string& data,
                          std::chrono::seconds timeout = std::chrono::seconds(30)) = 0;

    /**
     * @brief Update network statistics
     * @param bytes_sent Bytes sent
     * @param success Success flag
     * 
     * @section details Implementation Details
     * - Updates stats
     * - Thread-safe
     */
    void update_network_stats(size_t bytes_sent, bool success);

    /**
     * @brief Get network endpoint
     * @return const std::string& Endpoint
     * 
     * @section details Implementation Details
     * - Returns endpoint
     * - Thread-safe
     */
    const std::string& get_endpoint() const { return endpoint_; }

    /**
     * @brief Get network protocol
     * @return const std::string& Protocol
     * 
     * @section details Implementation Details
     * - Returns protocol
     * - Thread-safe
     */
    const std::string& get_protocol() const { return protocol_; }

private:
    std::string protocol_;  ///< Network protocol
    std::string endpoint_;  ///< Network endpoint
    std::atomic<size_t> bytes_sent_{0};  ///< Bytes sent
    std::atomic<size_t> send_errors_{0};  ///< Send errors
    mutable std::mutex network_mutex_;  ///< Network mutex
};

} // namespace omop::load