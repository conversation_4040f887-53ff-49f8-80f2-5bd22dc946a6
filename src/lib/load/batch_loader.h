/**
 * @file batch_loader.h
 * @brief Batch loading functionality for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 * @version 1.0
 * 
 * @section overview Overview
 * This header provides batch loading functionality for the OMOP ETL pipeline,
 * including memory management, compression, and parallel processing capabilities.
 * 
 * @section design Design Principles
 * - Performance: Batch processing and parallel execution
 * - Memory Efficiency: Controlled memory usage and compression
 * - Reliability: Batch-level transaction management
 * - Flexibility: Configurable batch operations
 * - Thread Safety: Concurrent batch processing
 * 
 * @section components Components
 * - BatchLoaderOptions: Loading configuration
 * - BatchStatistics: Batch metrics
 * - EnhancedBatch: Batch container with metadata
 * - BatchLoader: Core batch loading functionality
 * 
 * @section usage Usage
 * To use the batch loader:
 * 1. Configure batch options
 * 2. Initialize with desired settings
 * 3. Load records or batches
 * 4. Process batches in parallel
 * 
 * @section example Example
 * @code
 * auto loader = BatchLoader::create("file", options);
 * loader->initialize(config);
 * loader->load_batch(batch, context);
 * loader->commit(context);
 * @endcode
 */

#pragma once

#include "load/loader_base.h"
#include "core/record.h"
#include <queue>
#include <condition_variable>
#include <functional>

namespace omop::load {

/**
 * @brief Batch loading configuration options
 * 
 * @section description Description
 * Configures batch loading behavior, including batch sizes,
 * memory limits, compression settings, and parallel processing.
 * 
 * @section fields Fields
 * - batch_size: Records per batch
 * - max_batches_in_memory: Memory limit
 * - flush_interval_ms: Auto-flush timing
 * - enable_compression: Compression flag
 * - parallel_processing: Parallel flag
 * - worker_threads: Thread count
 * - compression_type: Algorithm
 * - deduplicate: Deduplication flag
 * - sort_batch: Sorting flag
 * - sort_key: Sort field
 */
struct BatchLoaderOptions {
    size_t batch_size{1000};              ///< Number of records per batch
    size_t max_batches_in_memory{10};     ///< Maximum batches to keep in memory
    size_t flush_interval_ms{5000};       ///< Auto-flush interval in milliseconds
    bool enable_compression{false};        ///< Enable batch compression
    bool parallel_processing{true};        ///< Enable parallel batch processing
    size_t worker_threads{4};             ///< Number of worker threads
    std::string compression_type{"gzip"}; ///< Compression algorithm
    bool deduplicate{false};              ///< Remove duplicate records
    bool sort_batch{false};               ///< Sort records within batch
    std::string sort_key;                 ///< Field to sort by
};

/**
 * @brief Batch processing statistics
 * 
 * @section description Description
 * Tracks metrics and timing information for batch processing,
 * including record counts, sizes, and processing times.
 * 
 * @section fields Fields
 * - records_in_batch: Record count
 * - batch_size_bytes: Uncompressed size
 * - compressed_size_bytes: Compressed size
 * - creation_time: Batch creation
 * - processing_start_time: Processing start
 * - processing_end_time: Processing end
 * - processed: Processing flag
 * - success: Success flag
 * - error_message: Error details
 */
struct BatchStatistics {
    size_t records_in_batch{0};  ///< Number of records in batch
    size_t batch_size_bytes{0};  ///< Uncompressed size in bytes
    size_t compressed_size_bytes{0};  ///< Compressed size in bytes
    std::chrono::steady_clock::time_point creation_time;  ///< Batch creation time
    std::chrono::steady_clock::time_point processing_start_time;  ///< Processing start time
    std::chrono::steady_clock::time_point processing_end_time;  ///< Processing end time
    bool processed{false};  ///< Processing completion flag
    bool success{false};  ///< Processing success flag
    std::string error_message;  ///< Error message if failed
};

/**
 * @brief Enhanced batch container
 * 
 * @section description Description
 * Extends basic batch functionality with metadata tracking,
 * compression, sorting, and deduplication capabilities.
 * 
 * @section features Features
 * - Record management
 * - Statistics tracking
 * - Compression support
 * - Sorting capabilities
 * - Deduplication
 * - Memory estimation
 */
class EnhancedBatch {
public:
    /**
     * @brief Constructor
     * @param batch_id Unique batch identifier
     * @param capacity Initial capacity
     * 
     * @section details Implementation Details
     * - Sets batch ID
     * - Sets capacity
     * - Initializes statistics
     * - Thread-safe
     */
    EnhancedBatch(size_t batch_id, size_t capacity);

    /**
     * @brief Add record to batch
     * @param record Record to add
     * @return bool True if batch is full after adding
     * 
     * @section details Implementation Details
     * - Adds record
     * - Updates statistics
     * - Checks capacity
     * - Thread-safe
     */
    bool add_record(const core::Record& record);

    /**
     * @brief Get batch records
     * @return const core::RecordBatch& Records in batch
     * 
     * @section details Implementation Details
     * - Returns records
     * - Thread-safe
     */
    const core::RecordBatch& get_records() const { return records_; }

    /**
     * @brief Get mutable batch records
     * @return core::RecordBatch& Mutable records
     * 
     * @section details Implementation Details
     * - Returns mutable records
     * - Thread-safe
     */
    core::RecordBatch& get_mutable_records() { return records_; }

    /**
     * @brief Get batch ID
     * @return size_t Batch identifier
     * 
     * @section details Implementation Details
     * - Returns ID
     * - Thread-safe
     */
    size_t get_batch_id() const { return batch_id_; }

    /**
     * @brief Get batch statistics
     * @return const BatchStatistics& Statistics
     * 
     * @section details Implementation Details
     * - Returns statistics
     * - Thread-safe
     */
    const BatchStatistics& get_statistics() const { return statistics_; }

    /**
     * @brief Update batch statistics
     * @return BatchStatistics& Mutable statistics
     * 
     * @section details Implementation Details
     * - Returns mutable statistics
     * - Thread-safe
     */
    BatchStatistics& get_mutable_statistics() { return statistics_; }

    /**
     * @brief Check if batch is full
     * @return bool True if full
     * 
     * @section details Implementation Details
     * - Checks capacity
     * - Thread-safe
     */
    bool is_full() const { return records_.size() >= capacity_; }

    /**
     * @brief Get batch size
     * @return size_t Number of records
     * 
     * @section details Implementation Details
     * - Returns size
     * - Thread-safe
     */
    size_t size() const { return records_.size(); }

    /**
     * @brief Clear batch
     * 
     * @section details Implementation Details
     * - Clears records
     * - Resets statistics
     * - Thread-safe
     */
    void clear();

    /**
     * @brief Sort batch records
     * @param key_extractor Function to extract sort key
     * 
     * @section details Implementation Details
     * - Sorts records
     * - Updates statistics
     * - Thread-safe
     */
    void sort(std::function<std::any(const core::Record&)> key_extractor);

    /**
     * @brief Remove duplicate records
     * @param key_extractor Function to extract deduplication key
     * @return size_t Number of duplicates removed
     * 
     * @section details Implementation Details
     * - Removes duplicates
     * - Updates statistics
     * - Thread-safe
     */
    size_t deduplicate(std::function<std::string(const core::Record&)> key_extractor);

    /**
     * @brief Compress batch data
     * @param compression_type Compression algorithm
     * @return std::vector<uint8_t> Compressed data
     * 
     * @section details Implementation Details
     * - Compresses data
     * - Updates statistics
     * - Thread-safe
     */
    std::vector<uint8_t> compress(const std::string& compression_type);

    /**
     * @brief Estimate memory usage
     * @return size_t Estimated bytes
     * 
     * @section details Implementation Details
     * - Estimates usage
     * - Thread-safe
     */
    size_t estimate_memory_usage() const;

private:
    size_t batch_id_;  ///< Unique batch identifier
    size_t capacity_;  ///< Maximum records
    core::RecordBatch records_;  ///< Batch records
    BatchStatistics statistics_;  ///< Batch statistics
};

/**
 * @brief Batch loader base class
 * 
 * @section description Description
 * Provides efficient batch-based loading with memory management,
 * compression, and parallel processing capabilities.
 * 
 * @section features Features
 * - Batch management
 * - Memory control
 * - Parallel processing
 * - Compression support
 * - Statistics tracking
 * - Thread safety
 */
class BatchLoader : public LoaderBase {
public:
    /**
     * @brief Constructor
     * @param name Loader name
     * @param options Batch loader options
     * 
     * @section details Implementation Details
     * - Sets name
     * - Sets options
     * - Thread-safe
     */
    BatchLoader(const std::string& name, BatchLoaderOptions options = {});

    /**
     * @brief Destructor
     * 
     * @section details Implementation Details
     * - Stops workers
     * - Flushes batches
     * - Thread-safe
     */
    ~BatchLoader() override;

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     * 
     * @section details Implementation Details
     * - Adds to batch
     * - Handles errors
     * - Thread-safe
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     * 
     * @section details Implementation Details
     * - Processes batch
     * - Handles errors
     * - Thread-safe
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Flushes batches
     * - Commits changes
     * - Thread-safe
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Clears batches
     * - Rolls back changes
     * - Thread-safe
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "batch"; }

protected:
    /**
     * @brief Process a complete batch
     * @param batch Batch to process
     * @param context Processing context
     * @return size_t Number of successfully processed records
     * 
     * @section details Implementation Details
     * - Processes batch
     * - Updates statistics
     * - Thread-safe
     */
    virtual size_t process_batch(std::unique_ptr<EnhancedBatch> batch,
                                core::ProcessingContext& context) = 0;

    /**
     * @brief Perform batch loader initialization
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Initializes loader
     * - Starts workers
     * - Thread-safe
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Perform batch loader finalization
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Stops workers
     * - Flushes batches
     * - Thread-safe
     */
    void do_finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get additional statistics
     * @return std::unordered_map<std::string, std::any> Batch-specific statistics
     * 
     * @section details Implementation Details
     * - Returns statistics
     * - Thread-safe
     */
    std::unordered_map<std::string, std::any> get_additional_statistics() const override;

    /**
     * @brief Get batch loader options
     * @return const BatchLoaderOptions& Options
     * 
     * @section details Implementation Details
     * - Returns options
     * - Thread-safe
     */
    const BatchLoaderOptions& get_options() const { return options_; }

private:
    /**
     * @brief Worker thread function
     * @param worker_id Worker identifier
     * 
     * @section details Implementation Details
     * - Processes batches
     * - Thread-safe
     */
    void worker_thread(size_t worker_id);

    /**
     * @brief Flush thread function
     * 
     * @section details Implementation Details
     * - Flushes batches
     * - Thread-safe
     */
    void flush_thread();

    /**
     * @brief Flush all pending batches
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Flushes batches
     * - Thread-safe
     */
    void flush_all_batches(core::ProcessingContext& context);

    /**
     * @brief Submit batch for processing
     * @param batch Batch to submit
     * 
     * @section details Implementation Details
     * - Submits batch
     * - Thread-safe
     */
    void submit_batch(std::unique_ptr<EnhancedBatch> batch);

    BatchLoaderOptions options_;  ///< Loader options
    std::queue<std::unique_ptr<EnhancedBatch>> batch_queue_;  ///< Batch queue
    std::condition_variable queue_cv_;  ///< Queue condition
    std::mutex queue_mutex_;  ///< Queue mutex
    std::vector<std::thread> workers_;  ///< Worker threads
    std::thread flush_thread_;  ///< Flush thread
    std::atomic<bool> stop_workers_{false};  ///< Stop flag
    std::atomic<size_t> total_processed_{0};  ///< Total processed records
    std::atomic<size_t> total_failed_{0};  ///< Total failed records
};

/**
 * @brief Create batch loader
 * @param type Loader type
 * @param options Loader options
 * @return std::unique_ptr<BatchLoader> Loader instance
 * 
 * @section details Implementation Details
 * - Creates loader
 * - Thread-safe
 */
std::unique_ptr<BatchLoader> create_batch_loader(
    const std::string& type,
    const BatchLoaderOptions& options = {});

/**
 * @brief Register batch loaders
 * 
 * @section details Implementation Details
 * - Registers loaders
 * - Thread-safe
 */
void register_batch_loaders();

/**
 * @brief CSV batch loader
 *
 * Specialized batch loader for CSV file output.
 */
class CsvBatchLoader : public BatchLoader {
public:
    /**
     * @brief Constructor
     * @param options Batch loader options
     * @param delimiter CSV delimiter
     * @param quote_char Quote character
     */
    CsvBatchLoader(BatchLoaderOptions options = {},
                   char delimiter = ',',
                   char quote_char = '"');

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "csv_batch"; }

protected:
    /**
     * @brief Process a batch by writing to CSV
     * @param batch Batch to process
     * @param context Processing context
     * @return size_t Number of successfully processed records
     */
    size_t process_batch(std::unique_ptr<EnhancedBatch> batch,
                        core::ProcessingContext& context) override;

    /**
     * @brief Initialize CSV batch loader
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

private:
    /**
     * @brief Format record as CSV line
     * @param record Record to format
     * @return std::string CSV line
     */
    std::string format_csv_line(const core::Record& record);

    /**
     * @brief Escape CSV value
     * @param value Value to escape
     * @return std::string Escaped value
     */
    std::string escape_csv_value(const std::string& value);

    char delimiter_;
    char quote_char_;
    std::string output_file_;
    std::ofstream output_stream_;
    std::mutex output_mutex_;
    bool header_written_{false};
    std::vector<std::string> column_order_;
};

/**
 * @brief Memory-mapped file batch loader
 *
 * High-performance batch loader using memory-mapped files.
 */
class MmapBatchLoader : public BatchLoader {
public:
    /**
     * @brief Constructor
     * @param options Batch loader options
     * @param file_size_hint Expected file size hint
     */
    MmapBatchLoader(BatchLoaderOptions options = {},
                    size_t file_size_hint = 0);

    /**
     * @brief Destructor
     */
    ~MmapBatchLoader() override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "mmap_batch"; }

protected:
    /**
     * @brief Process batch using memory-mapped file
     * @param batch Batch to process
     * @param context Processing context
     * @return size_t Number of successfully processed records
     */
    size_t process_batch(std::unique_ptr<EnhancedBatch> batch,
                        core::ProcessingContext& context) override;

    /**
     * @brief Initialize memory-mapped file
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Finalize and unmap file
     * @param context Processing context
     */
    void do_finalize(core::ProcessingContext& context) override;

private:
    /**
     * @brief Map file into memory
     * @param file_path File path
     * @param size Initial size
     */
    void map_file(const std::string& file_path, size_t size);

    /**
     * @brief Unmap file from memory
     */
    void unmap_file();

    /**
     * @brief Extend mapped file size
     * @param new_size New file size
     */
    void extend_file(size_t new_size);

    size_t file_size_hint_;
    std::string mapped_file_path_;
    void* mapped_memory_{nullptr};
    size_t mapped_size_{0};
    size_t current_offset_{0};
    std::mutex mmap_mutex_;
    
#ifdef _WIN32
    void* file_handle_{nullptr};
    void* mapping_handle_{nullptr};
#else
    int file_descriptor_{-1};
#endif
};

} // namespace omop::load