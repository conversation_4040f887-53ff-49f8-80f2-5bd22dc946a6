/**
 * @file job_manager.cpp
 * @brief Implementation of job management system for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "job_manager.h"
#include "common/utilities.h"
#include <algorithm>
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <sstream>

namespace omop::core {

// Job implementation
Job::Job(const JobConfig& config, std::unique_ptr<ETLPipeline> pipeline)
    : config_(config),
      pipeline_(std::move(pipeline)),
      creation_time_(std::chrono::system_clock::now()) {

    if (config_.job_id.empty()) {
        config_.job_id = omop::utils::CryptoUtils::generate_uuid();
    }

    checkpoint_path_ = "/tmp/omop-etl/checkpoints/" + config_.job_id + ".checkpoint";
}

void Job::setStatus(JobStatus status) {
    JobStatus old_status = status_.exchange(status);

    if (status == JobStatus::Running && old_status != JobStatus::Running) {
        start_time_ = std::chrono::system_clock::now();
    } else if ((status == JobStatus::Completed || status == JobStatus::Failed ||
                status == JobStatus::Cancelled) &&
               (old_status == JobStatus::Running || old_status == JobStatus::Paused)) {
        end_time_ = std::chrono::system_clock::now();
    }
}

JobStatistics Job::getStatistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return statistics_;
}

void Job::updateStatistics(const JobStatistics& stats) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    statistics_ = stats;

    // Calculate elapsed time
    if (start_time_.time_since_epoch().count() > 0) {
        auto now = (end_time_.time_since_epoch().count() > 0) ? end_time_ : std::chrono::system_clock::now();
        statistics_.elapsed_time = now - start_time_;

        // Calculate processing rate
        if (statistics_.elapsed_time.count() > 0) {
            statistics_.processing_rate = static_cast<double>(statistics_.total_records_processed) /
                                        statistics_.elapsed_time.count();
        }
    }
}

bool Job::canRetry() const {
    return retry_count_ < config_.max_retries &&
           (status_ == JobStatus::Failed || status_ == JobStatus::Cancelled);
}

bool Job::saveCheckpoint() {
    if (!config_.enable_checkpointing) {
        return true;
    }

    try {
        // Create checkpoint directory if it doesn't exist
        std::filesystem::create_directories(std::filesystem::path(checkpoint_path_).parent_path());

        // Create checkpoint data
        nlohmann::json checkpoint;
        checkpoint["job_id"] = config_.job_id;
        checkpoint["status"] = static_cast<int>(status_.load());
        checkpoint["retry_count"] = retry_count_;
        checkpoint["statistics"] = {
            {"total_records_processed", statistics_.total_records_processed},
            {"successful_records", statistics_.successful_records},
            {"failed_records", statistics_.failed_records},
            {"skipped_records", statistics_.skipped_records}
        };

        // Add pipeline state if available
        if (pipeline_) {
            auto job_info = pipeline_->get_job_info();
            checkpoint["pipeline_state"] = {
                {"processed_records", job_info.processed_records},
                {"error_records", job_info.error_records},
                {"total_records", job_info.total_records}
            };
        }

        // Write checkpoint to file
        std::ofstream file(checkpoint_path_);
        if (!file.is_open()) {
            return false;
        }

        file << checkpoint.dump(4);
        file.close();

        return true;
    } catch (const std::exception& e) {
        // Log error but don't fail the job
        return false;
    }
}

bool Job::loadCheckpoint() {
    if (!config_.enable_checkpointing || !std::filesystem::exists(checkpoint_path_)) {
        return false;
    }

    try {
        std::ifstream file(checkpoint_path_);
        if (!file.is_open()) {
            return false;
        }

        nlohmann::json checkpoint;
        file >> checkpoint;
        file.close();

        // Verify job ID matches
        if (checkpoint["job_id"] != config_.job_id) {
            return false;
        }

        // Restore state
        status_ = static_cast<JobStatus>(checkpoint["status"].get<int>());
        retry_count_ = checkpoint["retry_count"];

        // Restore statistics
        statistics_.total_records_processed = checkpoint["statistics"]["total_records_processed"];
        statistics_.successful_records = checkpoint["statistics"]["successful_records"];
        statistics_.failed_records = checkpoint["statistics"]["failed_records"];
        statistics_.skipped_records = checkpoint["statistics"]["skipped_records"];

        return true;
    } catch (const std::exception& e) {
        return false;
    }
}

// JobManager implementation
JobManager::JobManager(std::shared_ptr<common::ConfigurationManager> config,
                       std::shared_ptr<common::Logger> logger)
    : config_(config),
      logger_(logger),
      job_queue_(JobPriorityComparator{}) {

    // Load configuration
    max_concurrent_jobs_ = config_->get_value_or<size_t>("job_manager.max_concurrent_jobs", 4);
}

JobManager::~JobManager() {
    stop();
}

bool JobManager::start() {
    if (running_.exchange(true)) {
        return false; // Already running
    }

    logger_->info("Starting job manager with {} worker threads", max_concurrent_jobs_);

    // Start worker threads
    for (size_t i = 0; i < max_concurrent_jobs_; ++i) {
        worker_threads_.emplace_back(&JobManager::workerThread, this);
    }

    return true;
}

void JobManager::stop() {
    if (!running_.exchange(false)) {
        return; // Already stopped
    }

    logger_->info("Stopping job manager");

    // Wake up all worker threads
    queue_cv_.notify_all();

    // Wait for worker threads to finish
    for (auto& thread : worker_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }

    worker_threads_.clear();
}

std::string JobManager::submitJob(const JobConfig& config) {
    // Create pipeline from configuration
    auto pipeline = createPipeline(config.pipeline_config_path);
    if (!pipeline) {
        throw std::runtime_error("Failed to create pipeline from configuration");
    }

    // Create job
    auto job = std::make_shared<Job>(config, std::move(pipeline));

    // Add to job registry
    {
        std::lock_guard<std::mutex> lock(jobs_mutex_);
        jobs_[job->getId()] = job;
    }

    // Add to queue
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        job->setStatus(JobStatus::Created);
        job_queue_.push(job);
    }

    // Notify worker threads
    queue_cv_.notify_one();

    logger_->info("Submitted job {} with priority {}", job->getId(),
                 static_cast<int>(job->getConfig().priority));

    return job->getId();
}

std::shared_ptr<Job> JobManager::getJob(const std::string& job_id) const {
    std::lock_guard<std::mutex> lock(jobs_mutex_);
    auto it = jobs_.find(job_id);
    return (it != jobs_.end()) ? it->second : nullptr;
}

std::vector<std::shared_ptr<Job>> JobManager::getAllJobs() const {
    std::lock_guard<std::mutex> lock(jobs_mutex_);
    std::vector<std::shared_ptr<Job>> result;
    result.reserve(jobs_.size());

    for (const auto& [id, job] : jobs_) {
        result.push_back(job);
    }

    return result;
}

std::vector<std::shared_ptr<Job>> JobManager::getJobsByStatus(JobStatus status) const {
    std::lock_guard<std::mutex> lock(jobs_mutex_);
    std::vector<std::shared_ptr<Job>> result;

    for (const auto& [id, job] : jobs_) {
        if (job->getStatus() == status) {
            result.push_back(job);
        }
    }

    return result;
}

bool JobManager::cancelJob(const std::string& job_id) {
    auto job = getJob(job_id);
    if (!job) {
        return false;
    }

    JobStatus current_status = job->getStatus();
    if (current_status == JobStatus::Completed ||
        current_status == JobStatus::Failed ||
        current_status == JobStatus::Cancelled) {
        return false; // Job already finished
    }

    job->setStatus(JobStatus::Cancelled);

    // If job is running, signal the pipeline to stop
    if (current_status == JobStatus::Running && job->getPipeline()) {
        job->getPipeline()->stop();
    }

    logger_->info("Cancelled job {}", job_id);
    notifyJobStatusChange(job_id, current_status, JobStatus::Cancelled);

    return true;
}

bool JobManager::pauseJob(const std::string& job_id) {
    auto job = getJob(job_id);
    if (!job || job->getStatus() != JobStatus::Running) {
        return false;
    }

    job->setStatus(JobStatus::Paused);

    if (job->getPipeline()) {
        job->getPipeline()->pause();
    }

    logger_->info("Paused job {}", job_id);
    notifyJobStatusChange(job_id, JobStatus::Running, JobStatus::Paused);

    return true;
}

bool JobManager::resumeJob(const std::string& job_id) {
    auto job = getJob(job_id);
    if (!job || job->getStatus() != JobStatus::Paused) {
        return false;
    }

    // Re-queue the job
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        job->setStatus(JobStatus::Created); // Reset to Created for re-queueing
        job_queue_.push(job);
    }

    queue_cv_.notify_one();

    logger_->info("Resumed job {}", job_id);
    notifyJobStatusChange(job_id, JobStatus::Paused, JobStatus::Running);

    return true;
}

bool JobManager::retryJob(const std::string& job_id) {
    auto job = getJob(job_id);
    if (!job || !job->canRetry()) {
        return false;
    }

    job->incrementRetryCount();
    job->setStatus(JobStatus::Created); // Reset to Created for re-queueing

    // Create new pipeline instance for retry
    auto pipeline = createPipeline(job->getConfig().pipeline_config_path);
    if (!pipeline) {
        return false;
    }
    job->pipeline_ = std::move(pipeline);

    // Re-queue the job
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        job_queue_.push(job);
    }

    queue_cv_.notify_one();

    logger_->info("Retrying job {} (attempt {})", job_id, job->getRetryCount() + 1);
    notifyJobStatusChange(job_id, JobStatus::Failed, JobStatus::Created);

    return true;
}

size_t JobManager::getActiveJobCount() const {
    return active_jobs_.load();
}

size_t JobManager::getQueuedJobCount() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(queue_mutex_));
    return job_queue_.size();
}

void JobManager::setMaxConcurrentJobs(size_t max_jobs) {
    max_concurrent_jobs_ = max_jobs;
    // Note: This doesn't affect already running threads
}

void JobManager::registerJobEventCallback(
    std::function<void(const std::string&, JobStatus, JobStatus)> callback) {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    event_callbacks_.push_back(callback);
}

size_t JobManager::cleanupOldJobs(std::chrono::hours age) {
    std::lock_guard<std::mutex> lock(jobs_mutex_);
    auto now = std::chrono::system_clock::now();
    size_t cleaned = 0;

    for (auto it = jobs_.begin(); it != jobs_.end(); ) {
        auto job = it->second;
        if ((job->getStatus() == JobStatus::Completed ||
             job->getStatus() == JobStatus::Failed ||
             job->getStatus() == JobStatus::Cancelled) &&
            (now - job->getEndTime()) > age) {
            it = jobs_.erase(it);
            ++cleaned;
        } else {
            ++it;
        }
    }

    logger_->info("Cleaned up {} old jobs", cleaned);
    return cleaned;
}

void JobManager::workerThread() {
    logger_->debug("Worker thread started");

    while (running_) {
        std::shared_ptr<Job> job;

        // Get next job from queue
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            queue_cv_.wait(lock, [this] { return !job_queue_.empty() || !running_; });

            if (!running_) {
                break;
            }

            if (!job_queue_.empty()) {
                job = job_queue_.top();
                job_queue_.pop();
            }
        }

        if (job) {
            // Execute the job
            active_jobs_++;
            JobExecutionContext context{job, logger_, nullptr, false};
            executeJob(context);
            active_jobs_--;
        }
    }

    logger_->debug("Worker thread stopped");
}

void JobManager::executeJob(JobExecutionContext& context) {
    auto job = context.job;
    auto old_status = job->getStatus();

    try {
        logger_->info("Starting execution of job {}", job->getId());
        job->setStatus(JobStatus::Initializing);
        notifyJobStatusChange(job->getId(), old_status, JobStatus::Initializing);

        // Load checkpoint if available
        if (job->getConfig().enable_checkpointing) {
            job->loadCheckpoint();
        }

        // Initialize pipeline
        auto pipeline = job->getPipeline();
        if (!pipeline) {
            throw std::runtime_error("Pipeline is null");
        }

        // Run the pipeline
        job->setStatus(JobStatus::Running);
        notifyJobStatusChange(job->getId(), JobStatus::Initializing, JobStatus::Running);

        auto future = pipeline->start(job->getId());
        auto job_info = future.get(); // Wait for completion

        // Update job statistics
        JobStatistics stats;
        stats.total_records_processed = job_info.total_records;
        stats.successful_records = job_info.processed_records;
        stats.failed_records = job_info.error_records;
        job->updateStatistics(stats);

        if (job_info.status == JobStatus::Completed) {
            handleJobCompletion(job);
        } else {
            if (!job_info.error_messages.empty()) {
                job->setErrorMessage(job_info.error_messages[0]);
            }
            handleJobFailure(job);
        }

    } catch (const std::exception& e) {
        logger_->error("Job {} failed with exception: {}", job->getId(), e.what());
        job->setErrorMessage(e.what());
        handleJobFailure(job);
    }
}

std::unique_ptr<ETLPipeline> JobManager::createPipeline(const std::string& config_path) {
    try {
        if (config_path.empty()) {
            // Create default pipeline
            return std::make_unique<ETLPipeline>();
        }

        // TODO: Load configuration from file and create pipeline with config
        // For now, create a default pipeline
        logger_->warn("Pipeline configuration loading from file not yet implemented, using default pipeline");
        return std::make_unique<ETLPipeline>();

    } catch (const std::exception& e) {
        logger_->error("Failed to create pipeline from config {}: {}", config_path, e.what());
        return nullptr;
    }
}

void JobManager::handleJobCompletion(std::shared_ptr<Job> job) {
    job->setStatus(JobStatus::Completed);

    // Save final checkpoint
    job->saveCheckpoint();

    logger_->info("Job {} completed successfully. Stats: {} records processed, {} succeeded, {} failed",
                 job->getId(),
                 job->getStatistics().total_records_processed,
                 job->getStatistics().successful_records,
                 job->getStatistics().failed_records);

    notifyJobStatusChange(job->getId(), JobStatus::Running, JobStatus::Completed);
}

void JobManager::handleJobFailure(std::shared_ptr<Job> job) {
    job->setStatus(JobStatus::Failed);

    // Save checkpoint for potential retry
    job->saveCheckpoint();

    logger_->error("Job {} failed: {}", job->getId(), job->getErrorMessage());

    notifyJobStatusChange(job->getId(), JobStatus::Running, JobStatus::Failed);

    // Auto-retry if configured
    if (job->canRetry() && job->getConfig().max_retries > 0) {
        logger_->info("Scheduling automatic retry for job {} after {} seconds",
                     job->getId(), job->getConfig().retry_delay.count());

        // Schedule retry after delay
        // For simplicity, we'll retry immediately
        // In a production system, you'd want to implement a delayed retry mechanism
        std::this_thread::sleep_for(job->getConfig().retry_delay);
        retryJob(job->getId());
    }
}

void JobManager::notifyJobStatusChange(const std::string& job_id,
                                      JobStatus old_status,
                                      JobStatus new_status) {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    for (const auto& callback : event_callbacks_) {
        try {
            callback(job_id, old_status, new_status);
        } catch (const std::exception& e) {
            logger_->error("Error in job event callback: {}", e.what());
        }
    }
}

} // namespace omop::core