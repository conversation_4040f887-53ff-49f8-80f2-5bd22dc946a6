/**
 * @file interfaces.cpp
 * @brief Implementation of interface components for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "interfaces.h"
#include "common/logging.h"
#include <format>

namespace omop::core {

// ProcessingContext implementation

void ProcessingContext::set_data(const std::string& key, std::any value) {
    std::lock_guard<std::mutex> lock(context_mutex_);
    context_data_[key] = std::move(value);
}

std::optional<std::any> ProcessingContext::get_data(const std::string& key) const {
    std::lock_guard<std::mutex> lock(context_mutex_);
    auto it = context_data_.find(key);
    if (it != context_data_.end()) {
        return it->second;
    }
    return std::nullopt;
}

void ProcessingContext::log(const std::string& level, const std::string& message) {
    auto logger = common::Logger::get("omop-etl-context");
    
    // Format message with context information
    std::string formatted_message = std::format(
        "[Job: {}] [Stage: {}] {}", 
        job_id_,
        static_cast<int>(current_stage_),
        message
    );
    
    // Log based on level
    if (level == "debug") {
        logger->debug(formatted_message);
    } else if (level == "info") {
        logger->info(formatted_message);
    } else if (level == "warn" || level == "warning") {
        logger->warn(formatted_message);
    } else if (level == "error") {
        logger->error(formatted_message);
    } else if (level == "critical") {
        logger->critical(formatted_message);
    } else {
        // Default to info for unknown levels
        logger->info(formatted_message);
    }
}

} // namespace omop::core