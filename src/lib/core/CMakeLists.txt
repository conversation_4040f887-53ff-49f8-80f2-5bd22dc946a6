# Core library CMakeLists.txt
set(CORE_SOURCES
    component_factory.cpp
    interfaces.cpp
    job_manager.cpp
    job_scheduler.cpp
    pipeline.cpp
    record.cpp
)

set(CORE_HEADERS
    interfaces.h
    job_manager.h
    job_scheduler.h
    pipeline.h
    record.h
)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}
                    ${CMAKE_SOURCE_DIR}/src/lib/common
)

# Create core library
add_library(omop_core STATIC ${CORE_SOURCES} ${CORE_HEADERS})

# Set include directories
# Set include directories for the target
target_include_directories(omop_core
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/..>
        $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/src/lib>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_SOURCE_DIR}/src/lib/common
        ${YAML_CPP_INCLUDE_DIRS}
        ${spdlog_SOURCE_DIR}/include
)

# Link dependencies
target_link_libraries(omop_core
    PUBLIC
        omop_common
        spdlog::spdlog
        $<$<TARGET_EXISTS:yaml-cpp::yaml-cpp>:yaml-cpp::yaml-cpp>
        $<$<TARGET_EXISTS:yaml-cpp>:yaml-cpp>
    PRIVATE
        nlohmann_json::nlohmann_json
        Threads::Threads
)


# Set compile features and flags
target_compile_features(omop_core
    PUBLIC
        cxx_std_20
)

# Platform-specific compile options
if(MSVC)
    target_compile_options(omop_core PRIVATE /W4 /WX)
else()
    target_compile_options(omop_core PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Enable position independent code
set_target_properties(omop_core PROPERTIES POSITION_INDEPENDENT_CODE ON)

# Install rules
install(TARGETS omop_core
    EXPORT omop-etl-targets
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${CORE_HEADERS}
    DESTINATION include/omop/core
)
