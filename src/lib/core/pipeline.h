/**
 * @file pipeline.h
 * @brief ETL Pipeline implementation for OMOP CDM data processing
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header file defines the core ETL pipeline components for the OMOP CDM
 * data processing system. It provides a robust framework for orchestrating
 * data extraction, transformation, and loading operations with support for
 * parallel processing, error handling, and checkpointing.
 * 
 * @section design Design Principles
 * - Modularity: Clear separation of ETL stages
 * - Parallelism: Multi-threaded processing
 * - Fault Tolerance: Error handling and recovery
 * - Monitoring: Progress tracking and statistics
 * - Checkpointing: State persistence for recovery
 * - Thread Safety: Concurrent operation support
 * 
 * @section components Components
 * - ETLPipeline: Main pipeline orchestrator
 * - PipelineManager: Job scheduling and management
 * - JobInfo: Job status and statistics
 * - PipelineConfig: Pipeline configuration
 * 
 * @section usage Usage
 * To use the ETL pipeline:
 * 1. Configure pipeline components (extractor, transformer, loader)
 * 2. Set up pipeline configuration
 * 3. Start pipeline execution
 * 4. Monitor progress and handle results
 * 
 * @section example Example
 * @code
 * PipelineConfig config{...};
 * ETLPipeline pipeline(config);
 * pipeline.set_extractor(std::make_unique<MyExtractor>());
 * pipeline.set_transformer(std::make_unique<MyTransformer>());
 * pipeline.set_loader(std::make_unique<MyLoader>());
 * auto future = pipeline.start("job_123");
 * @endcode
 */

#pragma once

#include "interfaces.h"
#include "common/configuration.h"
#include "common/exceptions.h"
#include <memory>
#include <vector>
#include <thread>
#include <atomic>
#include <future>
#include <queue>
#include <condition_variable>
#include <chrono>
#include <functional>
#include <string>
#include <unordered_map>
#include <any>
#include <nlohmann/json.hpp>

namespace omop::core {

/**
 * @brief ETL job status enumeration
 * 
 * @section description Description
 * Defines the possible states of an ETL job throughout its lifecycle,
 * from creation to completion or failure.
 * 
 * @section states States
 * - Created: Initial state when job is created
 * - Initializing: Job is being set up
 * - Running: Job is actively processing
 * - Paused: Job execution is temporarily suspended
 * - Completed: Job finished successfully
 * - Failed: Job encountered an error
 * - Cancelled: Job was manually cancelled
 * 
 * @section thread_safety Thread Safety
 * Status transitions are thread-safe and atomic.
 */
enum class JobStatus {
    Created,      ///< Initial state
    Initializing, ///< Setup phase
    Running,      ///< Active processing
    Paused,       ///< Temporarily suspended
    Completed,    ///< Successfully finished
    Failed,       ///< Error occurred
    Cancelled     ///< Manually cancelled
};

/**
 * @brief ETL job information and statistics
 * 
 * @section description Description
 * Contains comprehensive information about an ETL job's execution,
 * including status, timing, progress metrics, and error tracking.
 * 
 * @section metrics Metrics
 * - Timing: Start/end times and duration
 * - Progress: Record counts and percentages
 * - Errors: Error counts and messages
 * - Metadata: Custom job information
 * 
 * @section thread_safety Thread Safety
 * All metric updates are thread-safe and atomic.
 */
class JobInfo {
public:
    std::string job_id;      ///< Unique job identifier
    std::string job_name;    ///< Human-readable job name
    JobStatus status{JobStatus::Created}; ///< Current job status
    std::chrono::system_clock::time_point start_time; ///< Job start time
    std::chrono::system_clock::time_point end_time;   ///< Job end time
    size_t total_records{0};     ///< Total records to process
    size_t processed_records{0}; ///< Records processed so far
    size_t error_records{0};     ///< Records with errors
    std::vector<std::string> error_messages; ///< Error messages
    std::unordered_map<std::string, std::any> metadata; ///< Custom metadata

    /**
     * @brief Calculate job duration
     * @return Duration in seconds
     * 
     * @section details Implementation Details
     * - For running jobs: current time - start time
     * - For completed jobs: end time - start time
     * - Thread-safe operation
     */
    [[nodiscard]] std::chrono::duration<double> duration() const {
        if (status == JobStatus::Running) {
            return std::chrono::system_clock::now() - start_time;
        }
        return end_time - start_time;
    }

    /**
     * @brief Calculate progress percentage
     * @return Progress as percentage (0-100)
     * 
     * @section details Implementation Details
     * - Returns 0 if no records to process
     * - Otherwise: (processed / total) * 100
     * - Thread-safe operation
     */
    [[nodiscard]] double progress() const {
        if (total_records == 0) return 0.0;
        return (static_cast<double>(processed_records) / total_records) * 100.0;
    }

    /**
     * @brief Calculate error rate
     * @return Error rate as ratio (0-1)
     * 
     * @section details Implementation Details
     * - Returns 0 if no records processed
     * - Otherwise: errors / processed
     * - Thread-safe operation
     */
    [[nodiscard]] double error_rate() const {
        if (processed_records == 0) return 0.0;
        return static_cast<double>(error_records) / processed_records;
    }
};

/**
 * @brief ETL pipeline configuration
 * 
 * @section description Description
 * Contains configuration parameters for the ETL pipeline, controlling
 * batch processing, parallelism, error handling, and checkpointing.
 * 
 * @section parameters Parameters
 * - Batch size and parallelism
 * - Queue capacity
 * - Error thresholds
 * - Checkpoint settings
 * - Validation options
 * 
 * @section thread_safety Thread Safety
 * Configuration is immutable after pipeline creation.
 */
struct PipelineConfig {
    size_t batch_size{1000};           ///< Records per batch
    size_t max_parallel_batches{4};    ///< Maximum parallel batches
    size_t queue_size{10000};          ///< Maximum queue size
    size_t commit_interval{10000};     ///< Records between commits
    double error_threshold{0.01};      ///< Maximum error rate
    bool stop_on_error{true};          ///< Stop on first error
    bool validate_records{true};       ///< Enable record validation
    std::chrono::seconds checkpoint_interval{300}; ///< Checkpoint interval
    std::string checkpoint_dir;        ///< Checkpoint directory
};

/**
 * @brief ETL pipeline orchestrator
 * 
 * @section description Description
 * Coordinates the entire ETL process, managing data extraction,
 * transformation, and loading operations. Provides thread-safe
 * operations and comprehensive error handling.
 * 
 * @section features Features
 * - Multi-threaded processing
 * - Progress monitoring
 * - Error handling
 * - Checkpointing
 * - Pre/post processing hooks
 * - Resource management
 * 
 * @section thread_safety Thread Safety
 * All public methods are thread-safe, using appropriate synchronization
 * mechanisms for concurrent access.
 * 
 * @section memory Memory Management
 * - RAII resource management
 * - Smart pointer ownership
 * - Automatic cleanup
 */
class ETLPipeline {
public:
    /**
     * @brief Constructor
     * @param config Pipeline configuration
     * 
     * @section details Implementation Details
     * - Initializes pipeline with provided configuration
     * - Sets up processing queues and threads
     * - Prepares error handling
     * - Thread-safe initialization
     */
    explicit ETLPipeline(PipelineConfig config = {});

    /**
     * @brief Destructor
     * 
     * @section details Implementation Details
     * - Stops all processing threads
     * - Cleans up resources
     * - Saves final state
     * - Thread-safe cleanup
     */
    ~ETLPipeline();

    /**
     * @brief Set data extractor
     * @param extractor Data extractor instance
     * 
     * @section details Implementation Details
     * - Sets the extractor component
     * - Takes ownership of extractor
     * - Thread-safe operation
     */
    void set_extractor(std::unique_ptr<IExtractor> extractor);

    /**
     * @brief Set data transformer
     * @param transformer Data transformer instance
     * 
     * @section details Implementation Details
     * - Sets the transformer component
     * - Takes ownership of transformer
     * - Thread-safe operation
     */
    void set_transformer(std::unique_ptr<ITransformer> transformer);

    /**
     * @brief Set data loader
     * @param loader Data loader instance
     * 
     * @section details Implementation Details
     * - Sets the loader component
     * - Takes ownership of loader
     * - Thread-safe operation
     */
    void set_loader(std::unique_ptr<ILoader> loader);

    /**
     * @brief Add pre-processing hook
     * @param processor Pre-processing function
     * 
     * @section details Implementation Details
     * - Adds pre-processing function
     * - Called before transformation
     * - Thread-safe operation
     */
    void add_pre_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Add post-processing hook
     * @param processor Post-processing function
     * 
     * @section details Implementation Details
     * - Adds post-processing function
     * - Called after transformation
     * - Thread-safe operation
     */
    void add_post_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Start pipeline execution
     * @param job_id Job identifier
     * @return Future containing job result
     * 
     * @section details Implementation Details
     * - Starts processing threads
     * - Returns future for result
     * - Thread-safe operation
     */
    std::future<JobInfo> start(const std::string& job_id);

    /**
     * @brief Stop pipeline execution
     * 
     * @section details Implementation Details
     * - Stops all threads
     * - Saves checkpoint
     * - Thread-safe operation
     */
    void stop();

    /**
     * @brief Pause pipeline execution
     * 
     * @section details Implementation Details
     * - Pauses processing
     * - Saves state
     * - Thread-safe operation
     */
    void pause();

    /**
     * @brief Resume pipeline execution
     * 
     * @section details Implementation Details
     * - Resumes processing
     * - Restores state
     * - Thread-safe operation
     */
    void resume();

    /**
     * @brief Get current job status
     * @return Current job status
     * 
     * @section details Implementation Details
     * - Returns current status
     * - Thread-safe operation
     */
    [[nodiscard]] JobStatus get_status() const;

    /**
     * @brief Get current job information
     * @return Current job information
     * 
     * @section details Implementation Details
     * - Returns current job info
     * - Thread-safe operation
     */
    [[nodiscard]] JobInfo get_job_info() const;

    /**
     * @brief Set progress callback
     * @param callback Progress callback function
     * 
     * @section details Implementation Details
     * - Sets progress notification callback
     * - Thread-safe operation
     */
    void set_progress_callback(
        std::function<void(const JobInfo&)> callback);

    /**
     * @brief Set error callback
     * @param callback Error callback function
     * 
     * @section details Implementation Details
     * - Sets error notification callback
     * - Thread-safe operation
     */
    void set_error_callback(
        std::function<void(const std::string&, const std::exception&)> callback);

private:
    /**
     * @brief Main pipeline execution loop
     * 
     * @section details Implementation Details
     * - Coordinates processing stages
     * - Handles errors
     * - Updates progress
     */
    void run_pipeline();

    /**
     * @brief Extraction worker thread
     * 
     * @section details Implementation Details
     * - Extracts data in batches
     * - Handles errors
     * - Updates progress
     */
    void extraction_worker();

    /**
     * @brief Transformation worker thread
     * 
     * @section details Implementation Details
     * - Transforms data in batches
     * - Handles errors
     * - Updates progress
     */
    void transformation_worker();

    /**
     * @brief Loading worker thread
     * 
     * @section details Implementation Details
     * - Loads data in batches
     * - Handles errors
     * - Updates progress
     */
    void loading_worker();

    /**
     * @brief Handle processing error
     * @param stage Processing stage
     * @param error Error message
     * 
     * @section details Implementation Details
     * - Logs error
     * - Updates statistics
     * - Notifies callbacks
     */
    void handle_error(ProcessingContext::Stage stage,
                     const std::string& error);

    /**
     * @brief Save checkpoint
     * 
     * @section details Implementation Details
     * - Saves current state
     * - Updates metadata
     * - Thread-safe operation
     */
    void save_checkpoint();

    /**
     * @brief Load checkpoint
     * @return bool True if checkpoint loaded
     * 
     * @section details Implementation Details
     * - Loads saved state
     * - Restores metadata
     * - Thread-safe operation
     */
    bool load_checkpoint();

    PipelineConfig config_;  ///< Pipeline configuration
    std::unique_ptr<IExtractor> extractor_;  ///< Data extractor
    std::unique_ptr<ITransformer> transformer_;  ///< Data transformer
    std::unique_ptr<ILoader> loader_;  ///< Data loader
    std::vector<std::function<void(RecordBatch&, ProcessingContext&)>> pre_processors_;  ///< Pre-processing hooks
    std::vector<std::function<void(RecordBatch&, ProcessingContext&)>> post_processors_;  ///< Post-processing hooks
    std::function<void(const JobInfo&)> progress_callback_;  ///< Progress callback
    std::function<void(const std::string&, const std::exception&)> error_callback_;  ///< Error callback
    std::atomic<JobStatus> status_{JobStatus::Created};  ///< Current status
    JobInfo job_info_;  ///< Job information
    std::mutex pipeline_mutex_;  ///< Pipeline mutex
    std::condition_variable pipeline_cv_;  ///< Pipeline condition variable
    std::queue<RecordBatch> extract_queue_;  ///< Extraction queue
    std::queue<RecordBatch> transform_queue_;  ///< Transformation queue
    std::queue<RecordBatch> load_queue_;  ///< Loading queue
    std::vector<std::thread> worker_threads_;  ///< Worker threads
    std::chrono::system_clock::time_point last_checkpoint_;  ///< Last checkpoint time
};

/**
 * @brief Pipeline job manager
 * 
 * @section description Description
 * Manages multiple ETL pipeline jobs, providing scheduling,
 * monitoring, and control capabilities.
 * 
 * @section features Features
 * - Job scheduling
 * - Resource management
 * - Status monitoring
 * - Job control
 * 
 * @section thread_safety Thread Safety
 * All operations are thread-safe with appropriate synchronization.
 */
class PipelineManager {
public:
    /**
     * @brief Constructor
     * @param max_concurrent_jobs Maximum concurrent jobs
     * 
     * @section details Implementation Details
     * - Initializes manager
     * - Sets up scheduler
     * - Thread-safe initialization
     */
    explicit PipelineManager(size_t max_concurrent_jobs = 4);

    /**
     * @brief Submit new job
     * @param pipeline Pipeline instance
     * @param job_id Job identifier
     * @return bool True if job submitted
     * 
     * @section details Implementation Details
     * - Submits job to scheduler
     * - Takes ownership of pipeline
     * - Thread-safe operation
     */
    bool submit_job(std::unique_ptr<ETLPipeline> pipeline,
                   const std::string& job_id);

    /**
     * @brief Cancel job
     * @param job_id Job identifier
     * @return bool True if job cancelled
     * 
     * @section details Implementation Details
     * - Cancels running job
     * - Cleans up resources
     * - Thread-safe operation
     */
    bool cancel_job(const std::string& job_id);

    /**
     * @brief Pause job
     * @param job_id Job identifier
     * @return bool True if job paused
     * 
     * @section details Implementation Details
     * - Pauses running job
     * - Saves state
     * - Thread-safe operation
     */
    bool pause_job(const std::string& job_id);

    /**
     * @brief Resume job
     * @param job_id Job identifier
     * @return bool True if job resumed
     * 
     * @section details Implementation Details
     * - Resumes paused job
     * - Restores state
     * - Thread-safe operation
     */
    bool resume_job(const std::string& job_id);

    /**
     * @brief Wait for job completion
     * @param job_id Job identifier
     * @param timeout_ms Timeout in milliseconds
     * @return bool True if job completed
     * 
     * @section details Implementation Details
     * - Waits for completion
     * - Handles timeout
     * - Thread-safe operation
     */
    bool wait_for_job(const std::string& job_id, int timeout_ms = -1);

    /**
     * @brief Shutdown manager
     * @param wait_for_jobs Whether to wait for jobs
     * 
     * @section details Implementation Details
     * - Stops scheduler
     * - Cleans up resources
     * - Thread-safe operation
     */
    void shutdown(bool wait_for_jobs = true);

private:
    /**
     * @brief Job entry structure
     * 
     * @section description Description
     * Contains information about a managed job, including
     * pipeline instance, execution future, and status.
     */
    struct JobEntry {
        std::string job_id;  ///< Job identifier
        std::unique_ptr<ETLPipeline> pipeline;  ///< Pipeline instance
        std::future<JobInfo> future;  ///< Execution future
        JobInfo info;  ///< Job information
    };

    /**
     * @brief Scheduler worker thread
     * 
     * @section details Implementation Details
     * - Manages job execution
     * - Handles scheduling
     * - Updates status
     */
    void scheduler_worker();

    /**
     * @brief Generate unique job ID
     * @return std::string Job identifier
     * 
     * @section details Implementation Details
     * - Generates unique ID
     * - Thread-safe operation
     */
    std::string generate_job_id();

    size_t max_concurrent_jobs_;  ///< Maximum concurrent jobs
    std::unordered_map<std::string, JobEntry> jobs_;  ///< Managed jobs
    std::mutex manager_mutex_;  ///< Manager mutex
    std::condition_variable manager_cv_;  ///< Manager condition variable
    std::thread scheduler_thread_;  ///< Scheduler thread
    std::atomic<bool> running_{true};  ///< Manager running flag
};

} // namespace omop::core