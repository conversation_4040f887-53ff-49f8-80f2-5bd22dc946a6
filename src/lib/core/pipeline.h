/**
 * @file pipeline.h
 * @brief ETL Pipeline implementation for OMOP CDM data processing
 * <AUTHOR> ETL Team
 * @date 2024
 * @version 1.0
 * 
 * @section overview Overview
 * This header file defines the core ETL pipeline components for the OMOP CDM
 * data processing system. It provides a robust framework for orchestrating
 * data extraction, transformation, and loading operations with support for
 * parallel processing, error handling, and checkpointing.
 * 
 * @section design Design Principles
 * - Modularity: Clear separation of ETL stages
 * - Parallelism: Multi-threaded processing
 * - Fault Tolerance: Error handling and recovery
 * - Monitoring: Progress tracking and statistics
 * - Checkpointing: State persistence for recovery
 * 
 * @section components Components
 * - ETLPipeline: Main pipeline orchestrator
 * - PipelineManager: Job scheduling and management
 * - JobInfo: Job status and statistics
 * - PipelineConfig: Pipeline configuration
 * 
 * @section usage Usage
 * To use the ETL pipeline:
 * 1. Configure pipeline components (extractor, transformer, loader)
 * 2. Set up pipeline configuration
 * 3. Start pipeline execution
 * 4. Monitor progress and handle results
 * 
 * @section example Example
 * @code
 * PipelineConfig config{...};
 * ETLPipeline pipeline(config);
 * pipeline.set_extractor(std::make_unique<MyExtractor>());
 * pipeline.set_transformer(std::make_unique<MyTransformer>());
 * pipeline.set_loader(std::make_unique<MyLoader>());
 * auto future = pipeline.start("job_123");
 * @endcode
 */

#pragma once

#include "interfaces.h"
#include "common/configuration.h"
#include "common/exceptions.h"
#include <memory>
#include <vector>
#include <thread>
#include <atomic>
#include <future>
#include <queue>
#include <condition_variable>
#include <chrono>
#include <functional>
#include <string>
#include <unordered_map>
#include <any>
#include <nlohmann/json.hpp>

namespace omop::core {

/**
 * @brief ETL job status enumeration
 * 
 * @section description Description
 * Defines the possible states of an ETL job throughout its lifecycle,
 * from creation to completion or failure.
 * 
 * @section states States
 * - Created: Initial state when job is created
 * - Initializing: Job is being set up
 * - Running: Job is actively processing
 * - Paused: Job execution is temporarily suspended
 * - Completed: Job finished successfully
 * - Failed: Job encountered an error
 * - Cancelled: Job was manually cancelled
 */
enum class JobStatus {
    Created,      ///< Initial state
    Initializing, ///< Setup phase
    Running,      ///< Active processing
    Paused,       ///< Temporarily suspended
    Completed,    ///< Successfully finished
    Failed,       ///< Error occurred
    Cancelled     ///< Manually cancelled
};

/**
 * @brief ETL job information and statistics
 * 
 * @section description Description
 * Contains comprehensive information about an ETL job's execution,
 * including status, timing, progress metrics, and error tracking.
 * 
 * @section metrics Metrics
 * - Timing: Start/end times and duration
 * - Progress: Record counts and percentages
 * - Errors: Error counts and messages
 * - Metadata: Custom job information
 */
class JobInfo {
public:
    std::string job_id;      ///< Unique job identifier
    std::string job_name;    ///< Human-readable job name
    JobStatus status{JobStatus::Created}; ///< Current job status
    std::chrono::system_clock::time_point start_time; ///< Job start time
    std::chrono::system_clock::time_point end_time;   ///< Job end time
    size_t total_records{0};     ///< Total records to process
    size_t processed_records{0}; ///< Records processed so far
    size_t error_records{0};     ///< Records with errors
    std::vector<std::string> error_messages; ///< Error messages
    std::unordered_map<std::string, std::any> metadata; ///< Custom metadata

    /**
     * @brief Calculate job duration
     * @return Duration in seconds
     * 
     * @section details Implementation Details
     * - For running jobs: current time - start time
     * - For completed jobs: end time - start time
     */
    [[nodiscard]] std::chrono::duration<double> duration() const {
        if (status == JobStatus::Running) {
            return std::chrono::system_clock::now() - start_time;
        }
        return end_time - start_time;
    }

    /**
     * @brief Calculate progress percentage
     * @return Progress as percentage (0-100)
     * 
     * @section details Implementation Details
     * - Returns 0 if no records to process
     * - Otherwise: (processed / total) * 100
     */
    [[nodiscard]] double progress() const {
        if (total_records == 0) return 0.0;
        return (static_cast<double>(processed_records) / total_records) * 100.0;
    }

    /**
     * @brief Calculate error rate
     * @return Error rate as ratio (0-1)
     * 
     * @section details Implementation Details
     * - Returns 0 if no records processed
     * - Otherwise: errors / processed
     */
    [[nodiscard]] double error_rate() const {
        if (processed_records == 0) return 0.0;
        return static_cast<double>(error_records) / processed_records;
    }
};

/**
 * @brief ETL pipeline configuration
 * 
 * @section description Description
 * Contains configuration parameters for the ETL pipeline, controlling
 * batch processing, parallelism, error handling, and checkpointing.
 * 
 * @section parameters Parameters
 * - Batch size and parallelism
 * - Queue capacity
 * - Error thresholds
 * - Checkpoint settings
 * - Validation options
 */
struct PipelineConfig {
    size_t batch_size{1000};           ///< Records per batch
    size_t max_parallel_batches{4};    ///< Maximum parallel batches
    size_t queue_size{10000};          ///< Maximum queue size
    size_t commit_interval{10000};     ///< Records between commits
    double error_threshold{0.01};      ///< Maximum error rate
    bool stop_on_error{true};          ///< Stop on first error
    bool validate_records{true};       ///< Enable record validation
    std::chrono::seconds checkpoint_interval{300}; ///< Checkpoint interval
    std::string checkpoint_dir;        ///< Checkpoint directory
};

/**
 * @brief ETL pipeline orchestrator
 * 
 * @section description Description
 * Coordinates the entire ETL process, managing data extraction,
 * transformation, and loading operations. Provides thread-safe
 * operations and comprehensive error handling.
 * 
 * @section features Features
 * - Multi-threaded processing
 * - Progress monitoring
 * - Error handling
 * - Checkpointing
 * - Pre/post processing hooks
 * 
 * @section thread_safety Thread Safety
 * All public methods are thread-safe, using appropriate synchronization
 * mechanisms for concurrent access.
 */
class ETLPipeline {
public:
    /**
     * @brief Constructor
     * @param config Pipeline configuration
     * 
     * @section details Implementation Details
     * - Initializes pipeline with provided configuration
     * - Sets up processing queues and threads
     * - Prepares error handling
     */
    explicit ETLPipeline(PipelineConfig config = {});

    /**
     * @brief Destructor
     * 
     * @section details Implementation Details
     * - Stops all processing threads
     * - Cleans up resources
     * - Saves final state
     */
    ~ETLPipeline();

    /**
     * @brief Set data extractor
     * @param extractor Data extractor instance
     * 
     * @section details Implementation Details
     * - Sets the extractor component
     * - Thread-safe operation
     */
    void set_extractor(std::unique_ptr<IExtractor> extractor);

    /**
     * @brief Set data transformer
     * @param transformer Data transformer instance
     * 
     * @section details Implementation Details
     * - Sets the transformer component
     * - Thread-safe operation
     */
    void set_transformer(std::unique_ptr<ITransformer> transformer);

    /**
     * @brief Set data loader
     * @param loader Data loader instance
     * 
     * @section details Implementation Details
     * - Sets the loader component
     * - Thread-safe operation
     */
    void set_loader(std::unique_ptr<ILoader> loader);

    /**
     * @brief Add pre-processing function
     * @param processor Pre-processing function
     * 
     * @section details Implementation Details
     * - Adds function to pre-processing pipeline
     * - Thread-safe operation
     */
    void add_pre_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Add post-processing function
     * @param processor Post-processing function
     * 
     * @section details Implementation Details
     * - Adds function to post-processing pipeline
     * - Thread-safe operation
     */
    void add_post_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Start pipeline execution
     * @param job_id Job identifier
     * @return Future containing job results
     * 
     * @section details Implementation Details
     * - Initializes job state
     * - Starts processing threads
     * - Returns future for results
     */
    std::future<JobInfo> start(const std::string& job_id);

    /**
     * @brief Stop pipeline gracefully
     * 
     * @section details Implementation Details
     * - Signals threads to stop
     * - Waits for completion
     * - Saves final state
     */
    void stop();

    /**
     * @brief Pause pipeline execution
     * 
     * @section details Implementation Details
     * - Suspends processing threads
     * - Preserves current state
     */
    void pause();

    /**
     * @brief Resume pipeline execution
     * 
     * @section details Implementation Details
     * - Resumes processing threads
     * - Restores previous state
     */
    void resume();

    /**
     * @brief Get current job status
     * @return Current job status
     * 
     * @section details Implementation Details
     * - Returns current status
     * - Thread-safe operation
     */
    [[nodiscard]] JobStatus get_status() const;

    /**
     * @brief Get current job information
     * @return Current job information
     * 
     * @section details Implementation Details
     * - Returns current job info
     * - Thread-safe operation
     */
    [[nodiscard]] JobInfo get_job_info() const;

    /**
     * @brief Set progress callback
     * @param callback Progress callback function
     * 
     * @section details Implementation Details
     * - Sets progress notification callback
     * - Thread-safe operation
     */
    void set_progress_callback(
        std::function<void(const JobInfo&)> callback);

    /**
     * @brief Set error callback
     * @param callback Error callback function
     * 
     * @section details Implementation Details
     * - Sets error notification callback
     * - Thread-safe operation
     */
    void set_error_callback(
        std::function<void(const std::string&, const std::exception&)> callback);

protected:
    /**
     * @brief Main pipeline execution method
     * 
     * @section details Implementation Details
     * - Coordinates ETL stages
     * - Manages processing flow
     * - Handles errors
     */
    void run_pipeline();

    /**
     * @brief Data extraction worker
     * 
     * @section details Implementation Details
     * - Runs in separate thread
     * - Extracts data from source
     * - Queues for transformation
     */
    void extraction_worker();

    /**
     * @brief Data transformation worker
     * 
     * @section details Implementation Details
     * - Runs in separate thread
     * - Transforms data records
     * - Queues for loading
     */
    void transformation_worker();

    /**
     * @brief Data loading worker
     * 
     * @section details Implementation Details
     * - Runs in separate thread
     * - Loads data to target
     * - Handles commits
     */
    void loading_worker();

    /**
     * @brief Handle pipeline error
     * @param stage Processing stage
     * @param error Error message
     * @param exception Optional exception
     * 
     * @section details Implementation Details
     * - Logs error details
     * - Updates job state
     * - Triggers callbacks
     */
    void handle_error(ProcessingContext::Stage stage,
                     const std::string& error,
                     const std::exception* exception = nullptr);

    /**
     * @brief Save checkpoint
     * 
     * @section details Implementation Details
     * - Saves current state
     * - Updates checkpoint file
     */
    void save_checkpoint();

    /**
     * @brief Load checkpoint
     * @return true if checkpoint loaded
     * 
     * @section details Implementation Details
     * - Loads previous state
     * - Restores processing
     */
    bool load_checkpoint();

private:
    // Configuration
    PipelineConfig config_; ///< Pipeline configuration

    // Components
    std::unique_ptr<IExtractor> extractor_;     ///< Data extractor
    std::unique_ptr<ITransformer> transformer_; ///< Data transformer
    std::unique_ptr<ILoader> loader_;           ///< Data loader

    // Pre/post processors
    std::vector<std::function<void(RecordBatch&, ProcessingContext&)>> pre_processors_;  ///< Pre-processing functions
    std::vector<std::function<void(RecordBatch&, ProcessingContext&)>> post_processors_; ///< Post-processing functions

    // Job information
    JobInfo job_info_;           ///< Current job information
    ProcessingContext context_;   ///< Processing context

    // Thread management
    std::vector<std::thread> workers_;
    std::atomic<JobStatus> status_{JobStatus::Created};
    std::atomic<bool> should_stop_{false};
    std::atomic<bool> is_paused_{false};

    // Queues
    std::queue<RecordBatch> extract_queue_;
    std::queue<RecordBatch> transform_queue_;
    std::mutex extract_mutex_;
    std::mutex transform_mutex_;
    std::condition_variable extract_cv_;
    std::condition_variable transform_cv_;
    std::condition_variable pause_cv_;

    // Callbacks
    std::function<void(const JobInfo&)> progress_callback_;
    std::function<void(const std::string&, const std::exception&)> error_callback_;

    // Statistics
    mutable std::mutex stats_mutex_;
    std::chrono::steady_clock::time_point last_checkpoint_;
};

/**
 * @brief Pipeline builder for fluent API
 *
 * Provides a builder pattern for constructing ETL pipelines.
 */
class PipelineBuilder {
public:
    /**
     * @brief Constructor
     */
    PipelineBuilder() : pipeline_(std::make_unique<ETLPipeline>()) {}

    /**
     * @brief Set configuration
     * @param config Pipeline configuration
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_config(const PipelineConfig& config);

    /**
     * @brief Set configuration from file
     * @param config_file Configuration file path
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_config_file(const std::string& config_file);

    /**
     * @brief Set extractor by type
     * @param type Extractor type
     * @param params Extractor parameters
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_extractor(const std::string& type,
                                   const std::unordered_map<std::string, std::any>& params);

    /**
     * @brief Set custom extractor
     * @param extractor Extractor instance
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_extractor(std::unique_ptr<IExtractor> extractor);

    /**
     * @brief Set transformer by type
     * @param type Transformer type
     * @param params Transformer parameters
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_transformer(const std::string& type,
                                     const std::unordered_map<std::string, std::any>& params);

    /**
     * @brief Set transformer for table
     * @param table_name OMOP table name
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_transformer_for_table(const std::string& table_name);

    /**
     * @brief Set custom transformer
     * @param transformer Transformer instance
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_transformer(std::unique_ptr<ITransformer> transformer);

    /**
     * @brief Set loader by type
     * @param type Loader type
     * @param params Loader parameters
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_loader(const std::string& type,
                                const std::unordered_map<std::string, std::any>& params);

    /**
     * @brief Set custom loader
     * @param loader Loader instance
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_loader(std::unique_ptr<ILoader> loader);

    /**
     * @brief Add pre-processor
     * @param processor Pre-processor function
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_pre_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Add post-processor
     * @param processor Post-processor function
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_post_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Set progress callback
     * @param callback Progress callback
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_progress_callback(
        std::function<void(const JobInfo&)> callback);

    /**
     * @brief Set error callback
     * @param callback Error callback
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_error_callback(
        std::function<void(const std::string&, const std::exception&)> callback);

    /**
     * @brief Build the pipeline
     * @return std::unique_ptr<ETLPipeline> Constructed pipeline
     */
    [[nodiscard]] std::unique_ptr<ETLPipeline> build();

private:
    std::unique_ptr<ETLPipeline> pipeline_;
};

/**
 * @brief Pipeline manager for managing multiple ETL jobs
 *
 * This class manages multiple ETL pipelines, providing job scheduling,
 * monitoring, and resource management.
 */
class PipelineManager {
public:
    /**
     * @brief Constructor
     * @param max_concurrent_jobs Maximum concurrent jobs
     */
    explicit PipelineManager(size_t max_concurrent_jobs = 4);

    /**
     * @brief Destructor
     */
    ~PipelineManager();

    /**
     * @brief Submit job
     * @param job_name Job name
     * @param pipeline Pipeline to execute
     * @return std::string Job ID
     */
    std::string submit_job(const std::string& job_name,
                          std::unique_ptr<ETLPipeline> pipeline);

    /**
     * @brief Get job status
     * @param job_id Job ID
     * @return std::optional<JobStatus> Job status if found
     */
    [[nodiscard]] std::optional<JobStatus> get_job_status(const std::string& job_id) const;

    /**
     * @brief Get job information
     * @param job_id Job ID
     * @return std::optional<JobInfo> Job information if found
     */
    [[nodiscard]] std::optional<JobInfo> get_job_info(const std::string& job_id) const;

    /**
     * @brief Get all jobs
     * @return std::vector<JobInfo> All job information
     */
    [[nodiscard]] std::vector<JobInfo> get_all_jobs() const;

    /**
     * @brief Cancel job
     * @param job_id Job ID
     * @return bool True if cancelled
     */
    bool cancel_job(const std::string& job_id);

    /**
     * @brief Pause job
     * @param job_id Job ID
     * @return bool True if paused
     */
    bool pause_job(const std::string& job_id);

    /**
     * @brief Resume job
     * @param job_id Job ID
     * @return bool True if resumed
     */
    bool resume_job(const std::string& job_id);

    /**
     * @brief Wait for job completion
     * @param job_id Job ID
     * @param timeout_ms Timeout in milliseconds (-1 for no timeout)
     * @return bool True if completed within timeout
     */
    bool wait_for_job(const std::string& job_id, int timeout_ms = -1);

    /**
     * @brief Shutdown manager
     * @param wait_for_jobs Whether to wait for running jobs
     */
    void shutdown(bool wait_for_jobs = true);

private:
    struct JobEntry {
        std::string job_id;
        std::unique_ptr<ETLPipeline> pipeline;
        std::future<JobInfo> future;
        JobInfo info;
    };

    size_t max_concurrent_jobs_;
    std::unordered_map<std::string, std::unique_ptr<JobEntry>> jobs_;
    std::queue<std::string> job_queue_;
    std::vector<std::thread> scheduler_threads_;

    mutable std::mutex jobs_mutex_;
    std::condition_variable job_cv_;
    std::atomic<bool> shutdown_{false};

    void scheduler_worker();
    std::string generate_job_id();
};

} // namespace omop::core