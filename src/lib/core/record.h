/**
 * @file record.h
 * @brief Data record representation for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header file defines the core data structures for representing and
 * manipulating records in the OMOP ETL pipeline. It provides a flexible
 * and type-safe way to handle data records as they flow through the
 * extraction, transformation, and loading stages.
 * 
 * @section design Design Principles
 * - Type Safety: Strong typing with std::any for field values
 * - Flexibility: Support for dynamic field addition/removal
 * - Metadata: Rich metadata for fields and records
 * - Performance: Efficient memory management and copying
 * - Serialization: JSON support for data exchange
 * - Thread Safety: Clear thread safety guarantees
 * 
 * @section components Components
 * - Record: Individual data record with fields and metadata
 * - RecordBatch: Collection of records for batch processing
 * - FieldMetadata: Field-level metadata and validation
 * - RecordMetadata: Record-level metadata and tracking
 * 
 * @section usage Usage
 * To use the record system:
 * 1. Create records with initial data
 * 2. Manipulate fields and metadata
 * 3. Process records individually or in batches
 * 4. Serialize/deserialize as needed
 * 
 * @section example Example
 * @code
 * Record record;
 * record.setField("person_id", 12345);
 * record.setField("birth_date", "1980-01-01");
 * RecordMetadata meta;
 * meta.source_table = "patients";
 * record.setMetadata(meta);
 * @endcode
 */

#pragma once

#include <string>
#include <unordered_map>
#include <any>
#include <vector>
#include <chrono>
#include <optional>
#include <memory>
#include <variant>

namespace omop::core {

/**
 * @brief Represents a single data record in the ETL pipeline
 * 
 * @section description Description
 * Encapsulates a data record as it flows through the pipeline, providing
 * a unified interface for accessing and manipulating field values regardless
 * of the source or target data format. Supports rich metadata and type-safe
 * field access.
 * 
 * @section features Features
 * - Dynamic field management
 * - Type-safe value access
 * - Rich metadata support
 * - JSON serialization
 * - Field selection and renaming
 * - Record merging
 * 
 * @section thread_safety Thread Safety
 * Individual Record instances are not thread-safe. For concurrent access,
 * use appropriate synchronization mechanisms.
 * 
 * @section memory Memory Management
 * - Efficient copying with move semantics
 * - Smart pointer support
 * - RAII resource management
 */
class Record {
public:
    /**
     * @brief Field metadata and validation information
     * 
     * @section description Description
     * Contains metadata and validation rules for individual fields,
     * including data type information, nullability, and source mapping.
     * 
     * @section fields Fields
     * - name: Field identifier
     * - data_type: Expected data type
     * - is_nullable: Null value support
     * - source_column: Original source mapping
     * - description: Field documentation
     * 
     * @section thread_safety Thread Safety
     * Field metadata is immutable after creation.
     */
    struct FieldMetadata {
        std::string name;           ///< Field name
        std::string data_type;      ///< Data type
        bool is_nullable{true};     ///< Whether field can be null
        std::string source_column;  ///< Original source column name
        std::string description;    ///< Field description
    };

    /**
     * @brief Record-level metadata and tracking information
     * 
     * @section description Description
     * Contains metadata about the record's origin, processing history,
     * and custom attributes. Used for tracking and debugging.
     * 
     * @section fields Fields
     * - source_table: Origin table/file
     * - target_table: Destination table
     * - source_row_number: Original row number
     * - extraction_time: When record was extracted
     * - record_id: Unique identifier
     * - custom: Additional metadata
     * 
     * @section thread_safety Thread Safety
     * Record metadata is mutable but not thread-safe.
     */
    struct RecordMetadata {
        std::string source_table;   ///< Source table/file name
        std::string target_table;   ///< Target table name
        size_t source_row_number{0}; ///< Row number in source
        std::chrono::system_clock::time_point extraction_time; ///< When record was extracted
        std::string record_id;      ///< Unique record identifier
        std::unordered_map<std::string, std::string> custom; ///< Custom metadata
    };

    /**
     * @brief Default constructor
     * 
     * @section details Implementation Details
     * - Creates empty record
     * - Initializes empty metadata
     * - No memory allocation
     */
    Record() = default;

    /**
     * @brief Constructor with initial data
     * @param data Initial field data
     * 
     * @section details Implementation Details
     * - Creates record with provided fields
     * - Initializes default metadata
     * - Deep copies field values
     */
    explicit Record(const std::unordered_map<std::string, std::any>& data);

    /**
     * @brief Constructor with metadata
     * @param data Initial field data
     * @param metadata Record metadata
     * 
     * @section details Implementation Details
     * - Creates record with provided fields
     * - Sets provided metadata
     * - Deep copies field values
     */
    Record(const std::unordered_map<std::string, std::any>& data,
           const RecordMetadata& metadata);

    /**
     * @brief Copy constructor
     * 
     * @section details Implementation Details
     * - Creates deep copy
     * - Copies all fields
     * - Copies metadata
     */
    Record(const Record& other) = default;

    /**
     * @brief Move constructor
     * 
     * @section details Implementation Details
     * - Moves ownership
     * - No copying
     * - Source becomes empty
     */
    Record(Record&& other) noexcept = default;

    /**
     * @brief Copy assignment operator
     * 
     * @section details Implementation Details
     * - Creates deep copy
     * - Copies all fields
     * - Copies metadata
     */
    Record& operator=(const Record& other) = default;

    /**
     * @brief Move assignment operator
     * 
     * @section details Implementation Details
     * - Moves ownership
     * - No copying
     * - Source becomes empty
     */
    Record& operator=(Record&& other) noexcept = default;

    /**
     * @brief Destructor
     * 
     * @section details Implementation Details
     * - Cleans up resources
     * - No explicit cleanup needed
     */
    virtual ~Record() = default;

    /**
     * @brief Set a field value
     * @param field_name Field name
     * @param value Field value
     * 
     * @section details Implementation Details
     * - Adds or updates field
     * - Preserves metadata
     * - Deep copies value
     */
    void setField(const std::string& field_name, const std::any& value);

    /**
     * @brief Get a field value
     * @param field_name Field name
     * @return Field value
     * @throws std::out_of_range if field not found
     * 
     * @section details Implementation Details
     * - Returns const reference
     * - No copying
     * - Throws if missing
     */
    const std::any& getField(const std::string& field_name) const;

    /**
     * @brief Get a field value with type conversion
     * @tparam T Target type
     * @param field_name Field name
     * @return Field value as type T
     * @throws std::bad_any_cast if type conversion fails
     * 
     * @section details Implementation Details
     * - Converts to type T
     * - Throws if invalid
     * - No copying if possible
     */
    template<typename T>
    T getFieldAs(const std::string& field_name) const {
        const auto& value = getField(field_name);
        return std::any_cast<T>(value);
    }

    /**
     * @brief Get optional field value
     * @param field_name Field name
     * @return Optional containing field value if exists
     * 
     * @section details Implementation Details
     * - Returns empty if missing
     * - No exceptions
     * - No copying
     */
    std::optional<std::any> getFieldOptional(const std::string& field_name) const;

    /**
     * @brief Check if field exists
     * @param field_name Field name
     * @return bool True if field exists
     * 
     * @section details Implementation Details
     * - O(1) lookup
     * - No exceptions
     * - Thread-safe
     */
    bool hasField(const std::string& field_name) const;

    /**
     * @brief Check if field is null
     * @param field_name Field name
     * @return bool True if field is null
     * @throws std::out_of_range if field not found
     * 
     * @section details Implementation Details
     * - Checks for null
     * - Throws if missing
     * - Thread-safe
     */
    bool isFieldNull(const std::string& field_name) const;

    /**
     * @brief Remove a field
     * @param field_name Field name
     * @return bool True if field was removed
     * 
     * @section details Implementation Details
     * - Removes field
     * - Preserves metadata
     * - Returns success
     */
    bool removeField(const std::string& field_name);

    /**
     * @brief Clear all fields
     * 
     * @section details Implementation Details
     * - Removes all fields
     * - Preserves metadata
     * - No exceptions
     */
    void clear();

    /**
     * @brief Get field count
     * @return size_t Number of fields
     *
     * @section details Implementation Details
     * - O(1) operation
     * - No exceptions
     * - Thread-safe
     */
    size_t getFieldCount() const { return fields_.size(); }

    /**
     * @brief Get all field names
     * @return Vector of field names
     *
     * @section details Implementation Details
     * - Returns all field names
     * - No exceptions
     * - Thread-safe
     */
    std::vector<std::string> getFieldNames() const {
        std::vector<std::string> names;
        names.reserve(fields_.size());
        for (const auto& [name, _] : fields_) {
            names.push_back(name);
        }
        return names;
    }

    /**
     * @brief Check if record is empty
     * @return bool True if no fields
     * 
     * @section details Implementation Details
     * - O(1) operation
     * - No exceptions
     * - Thread-safe
     */
    bool isEmpty() const { return fields_.empty(); }

    /**
     * @brief Get record metadata
     * @return const RecordMetadata& Record metadata
     * 
     * @section details Implementation Details
     * - Returns reference
     * - No copying
     * - Thread-safe
     */
    const RecordMetadata& getMetadata() const { return metadata_; }

    /**
     * @brief Set record metadata
     * @param metadata New metadata
     * 
     * @section details Implementation Details
     * - Copies metadata
     * - No exceptions
     * - Not thread-safe
     */
    void setMetadata(const RecordMetadata& metadata) { metadata_ = metadata; }

    /**
     * @brief Set field metadata
     * @param field_name Field name
     * @param metadata Field metadata
     * @throws std::out_of_range if field not found
     * 
     * @section details Implementation Details
     * - Updates metadata
     * - Throws if missing
     * - Not thread-safe
     */
    void setFieldMetadata(const std::string& field_name, const FieldMetadata& metadata);

    /**
     * @brief Merge another record
     * @param other Record to merge
     * @param overwrite Whether to overwrite existing fields
     * 
     * @section details Implementation Details
     * - Merges fields
     * - Updates metadata
     * - No exceptions
     */
    void merge(const Record& other, bool overwrite = true);

    /**
     * @brief Select specific fields
     * @param field_names Field names to select
     * @return Record with selected fields
     * 
     * @section details Implementation Details
     * - Creates new record
     * - Copies selected fields
     * - Preserves metadata
     */
    Record selectFields(const std::vector<std::string>& field_names) const;

    /**
     * @brief Rename a field
     * @param old_name Current field name
     * @param new_name New field name
     * @return bool True if renamed
     * 
     * @section details Implementation Details
     * - Renames field
     * - Preserves value
     * - Preserves metadata
     */
    bool renameField(const std::string& old_name, const std::string& new_name);

    /**
     * @brief Convert to JSON
     * @param pretty Whether to format JSON
     * @return std::string JSON representation
     * 
     * @section details Implementation Details
     * - Serializes fields
     * - Includes metadata
     * - Thread-safe
     */
    std::string toJson(bool pretty = false) const;

    /**
     * @brief Create from JSON
     * @param json JSON string
     * @return Record Parsed record
     * @throws std::runtime_error if JSON is invalid
     *
     * @section details Implementation Details
     * - Parses JSON string
     * - Creates record with fields
     * - Sets metadata if present
     * - Thread-safe
     */
    static Record fromJson(const std::string& json);

    /**
     * @brief Convert record to string representation
     * @return String representation
     */
    std::string toString() const;

    /**
     * @brief Equality operator
     * @param other Other record
     * @return true if records are equal
     */
    bool operator==(const Record& other) const;

    /**
     * @brief Inequality operator
     * @param other Other record
     * @return true if records are not equal
     */
    bool operator!=(const Record& other) const { return !(*this == other); }

private:
    std::unordered_map<std::string, std::any> fields_; ///< Field values
    std::unordered_map<std::string, FieldMetadata> field_metadata_; ///< Field metadata
    RecordMetadata metadata_; ///< Record metadata
};

/**
 * @brief Collection of records for batch processing
 * 
 * @section description Description
 * Manages a collection of records for efficient batch processing
 * in the ETL pipeline. Provides methods for adding, accessing,
 * and managing records in bulk.
 * 
 * @section features Features
 * - Fixed or dynamic capacity
 * - Efficient memory management
 * - Batch operations
 * - Const access to records
 * 
 * @section thread_safety Thread Safety
 * Individual RecordBatch instances are not thread-safe. For concurrent
 * access, use appropriate synchronization mechanisms.
 */
class RecordBatch {
public:
    /**
     * @brief Constructor with capacity
     * @param capacity Initial capacity
     * 
     * @section details Implementation Details
     * - Pre-allocates memory for records
     * - No exceptions thrown
     */
    explicit RecordBatch(size_t capacity);

    /**
     * @brief Add record by copy
     * @param record Record to add
     * 
     * @section details Implementation Details
     * - Makes a copy of the record
     * - No exceptions thrown
     */
    void addRecord(const Record& record);

    /**
     * @brief Add record by move
     * @param record Record to add
     * 
     * @section details Implementation Details
     * - Takes ownership of the record
     * - No exceptions thrown
     */
    void addRecord(Record&& record);

    /**
     * @brief Get record by index
     * @param index Record index
     * @return Const reference to record
     * @throws std::out_of_range if index invalid
     * 
     * @section details Implementation Details
     * - Returns const reference to record
     * - Throws if index out of range
     */
    const Record& getRecord(size_t index) const;

    /**
     * @brief Get mutable record by index
     * @param index Record index
     * @return Mutable reference to record
     * @throws std::out_of_range if index invalid
     *
     * @section details Implementation Details
     * - Returns mutable reference to record
     * - Throws if index out of range
     */
    Record& getRecordMutable(size_t index);

    /**
     * @brief Get all records
     * @return Const reference to record vector
     *
     * @section details Implementation Details
     * - Returns const reference to records
     * - No exceptions thrown
     */
    const std::vector<Record>& getRecords() const { return records_; }

    /**
     * @brief Get number of records
     * @return Record count
     * 
     * @section details Implementation Details
     * - Returns current record count
     * - No exceptions thrown
     */
    size_t size() const { return records_.size(); }

    /**
     * @brief Check if batch is empty
     * @return true if no records
     * 
     * @section details Implementation Details
     * - Returns true if no records
     * - No exceptions thrown
     */
    bool isEmpty() const { return records_.empty(); }

    /**
     * @brief Check if batch is empty
     * @return true if no records
     * 
     * @section details Implementation Details
     * - Alias for isEmpty()
     * - No exceptions thrown
     */
    bool empty() const { return records_.empty(); }

    /**
     * @brief Clear all records
     * 
     * @section details Implementation Details
     * - Removes all records
     * - No exceptions thrown
     */
    void clear() { records_.clear(); }

    /**
     * @brief Reserve capacity
     * @param capacity Desired capacity
     * 
     * @section details Implementation Details
     * - Pre-allocates memory
     * - No exceptions thrown
     */
    void reserve(size_t capacity) { records_.reserve(capacity); }

    /**
     * @brief Iterator support
     */
    auto begin() { return records_.begin(); }
    auto end() { return records_.end(); }
    auto begin() const { return records_.begin(); }
    auto end() const { return records_.end(); }

private:
    std::vector<Record> records_; ///< Record collection
};

} // namespace omop::core