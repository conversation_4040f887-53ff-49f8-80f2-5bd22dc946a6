/**
 * @file component_factory.cpp
 * @brief Implementation of component factory for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "interfaces.h"
#include "common/utilities.h"

#include <memory>
#include <mutex>
#include <format>

namespace omop::core {

// Global component factory instances
namespace {
    ComponentFactory<IExtractor> extractor_factory;
    ComponentFactory<ITransformer> transformer_factory;
    ComponentFactory<ILoader> loader_factory;
    std::once_flag factories_initialized;
}

/**
 * @brief Initialize built-in component factories
 */
void initialize_component_factories() {
    std::call_once(factories_initialized, []() {
        // TODO: Register built-in extractors when concrete implementations are available
        // extractor_factory.register_creator("csv", []() {
        //     return std::make_unique<CsvExtractor>();
        // });

        // extractor_factory.register_creator("json", []() {
        //     return std::make_unique<JsonExtractor>();
        // });

        // TODO: Register built-in transformers when concrete implementations are available
        // transformer_factory.register_creator("mapping", []() {
        //     return std::make_unique<MappingTransformer>();
        // });

        // transformer_factory.register_creator("validation", []() {
        //     return std::make_unique<ValidationTransformer>();
        // });

        // TODO: Register built-in loaders when concrete implementations are available
        // loader_factory.register_creator("database", []() {
        //     return std::make_unique<DatabaseLoader>();
        // });

        // loader_factory.register_creator("batch", []() {
        //     return std::make_unique<BatchLoader>();
        // });
    });
}

/**
 * @brief Get global extractor factory
 * @return Reference to extractor factory
 */
ComponentFactory<IExtractor>& get_extractor_factory() {
    initialize_component_factories();
    return extractor_factory;
}

/**
 * @brief Get global transformer factory
 * @return Reference to transformer factory
 */
auto get_transformer_factory() -> ComponentFactory<ITransformer>& {
    initialize_component_factories();
    return transformer_factory;
}

/**
 * @brief Get global loader factory
 * @return Reference to loader factory
 */
auto get_loader_factory() -> ComponentFactory<ILoader>& {
    initialize_component_factories();
    return loader_factory;
}

/**
 * @brief Factory method to create extractor by type
 * @param type Extractor type
 * @param config Configuration parameters
 * @return Unique pointer to created extractor
 */
auto create_extractor(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config) {

    auto& factory = get_extractor_factory();
    auto extractor = factory.create(type);

    // Initialize with configuration
    ProcessingContext context;
    extractor->initialize(config, context);

    return extractor;
}

/**
 * @brief Factory method to create transformer by type
 * @param type Transformer type
 * @param config Configuration parameters
 * @return Unique pointer to created transformer
 */
std::unique_ptr<ITransformer> create_transformer(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config) {

    auto& factory = get_transformer_factory();
    auto transformer = factory.create(type);

    // Initialize with configuration
    ProcessingContext context;
    transformer->initialize(config, context);

    return transformer;
}

/**
 * @brief Factory method to create loader by type
 * @param type Loader type
 * @param config Configuration parameters
 * @return Unique pointer to created loader
 */
std::unique_ptr<ILoader> create_loader(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config) {

    auto& factory = get_loader_factory();
    auto loader = factory.create(type);

    // Initialize with configuration
    ProcessingContext context;
    loader->initialize(config, context);

    return loader;
}

/**
 * @brief Register custom extractor type
 * @param type Type name
 * @param creator Creator function
 */
void register_extractor_type(
    const std::string& type,
    std::function<std::unique_ptr<IExtractor>()> creator) {
    get_extractor_factory().register_creator(type, std::move(creator));
}

/**
 * @brief Register custom transformer type
 * @param type Type name
 * @param creator Creator function
 */
void register_transformer_type(
    const std::string& type,
    std::function<std::unique_ptr<ITransformer>()> creator) {
    get_transformer_factory().register_creator(type, std::move(creator));
}

/**
 * @brief Register custom loader type
 * @param type Type name
 * @param creator Creator function
 */
void register_loader_type(
    const std::string& type,
    std::function<std::unique_ptr<ILoader>()> creator) {
    get_loader_factory().register_creator(type, std::move(creator));
}

/**
 * @brief Get list of registered extractor types
 * @return Vector of type names
 */
std::vector<std::string> get_registered_extractor_types() {
    return get_extractor_factory().get_registered_types();
}

/**
 * @brief Get list of registered transformer types
 * @return Vector of type names
 */
std::vector<std::string> get_registered_transformer_types() {
    return get_transformer_factory().get_registered_types();
}

/**
 * @brief Get list of registered loader types
 * @return Vector of type names
 */
std::vector<std::string> get_registered_loader_types() {
    return get_loader_factory().get_registered_types();
}

} // namespace omop::core