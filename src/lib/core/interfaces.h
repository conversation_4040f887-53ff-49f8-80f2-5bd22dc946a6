#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>
#include <variant>
#include <optional>
#include <chrono>
#include <concepts>
#include <iostream>
#include <format>
#include <mutex>
#include <spdlog/spdlog.h>
#include <stdexcept>

#include "common/exceptions.h"
#include "common/logging.h"
#include "record.h"

namespace omop::core {

/**
 * @file interfaces.h
 * @brief Core interfaces for the OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header file defines the core interfaces used throughout the OMOP ETL pipeline.
 * These interfaces establish the contract between different components of the system,
 * ensuring consistent behavior and enabling loose coupling between modules.
 * 
 * @section design Design Principles
 * - Interface Segregation: Each interface has a single, well-defined responsibility
 * - Dependency Inversion: High-level modules depend on abstractions
 * - Open/Closed: Interfaces are designed for extension without modification
 * - Thread Safety: All operations are designed to be thread-safe
 * - Error Handling: Comprehensive error reporting and validation
 * 
 * @section components Components
 * - ProcessingContext: Execution context and state management
 * - IExtractor: Data extraction interface
 * - ITransformer: Data transformation interface
 * - ILoader: Data loading interface
 * - IValidator: Data validation interface
 * - IDataProcessor: Generic data processing interface
 * - IDataReader: Data reading interface
 * - IDataWriter: Data writing interface
 * 
 * @section usage Usage
 * To use these interfaces:
 * 1. Include this header in your implementation file
 * 2. Create concrete classes that implement the required interfaces
 * 3. Use the interfaces as type hints in your function parameters
 * 4. Handle errors and validation results appropriately
 * 
 * @section example Example
 * @code
 * class MyDataProcessor : public IDataProcessor {
 *     bool processData(const std::vector<std::string>& data) override {
 *         // Implementation
 *         return true;
 *     }
 * };
 * @endcode
 */

/**
 * @brief Forward declarations
 */
class ProcessingContext;
class ValidationResult;

/**
 * @brief Concept for record-like types
 * 
 * @section description Description
 * Defines requirements for types that can be used as records in the ETL pipeline.
 * Ensures consistent field access and manipulation across different record types.
 * 
 * @section requirements Requirements
 * - getField: Retrieve field value by name
 * - setField: Set field value by name
 * - getFieldNames: Get list of field names
 */
template<typename T>
concept RecordLike = requires(T t) {
    { t.getField(std::string{}) } -> std::convertible_to<std::optional<std::any>>;
    { t.setField(std::string{}, std::any{}) } -> std::same_as<void>;
    { t.getFieldNames() } -> std::convertible_to<std::vector<std::string>>;
};

/**
 * @class ProcessingContext
 * @brief Processing context for ETL operations
 * @ingroup core
 * 
 * @section description Description
 * Manages the execution context and state for ETL operations. Provides access to
 * shared resources, tracks processing progress, and maintains job metadata.
 * 
 * @section features Features
 * - Stage tracking
 * - Progress monitoring
 * - Error counting
 * - Context data storage
 * - Logging integration
 * 
 * @section thread_safety Thread Safety
 * All operations are thread-safe through mutex protection.
 * 
 * @section dependencies Dependencies
 * - spdlog: Logging framework
 * - std::chrono: Timing utilities
 */
class ProcessingContext {
public:
    /**
     * @brief Processing stage enumeration
     * 
     * @section values Values
     * - Extract: Data extraction phase
     * - Transform: Data transformation phase
     * - Load: Data loading phase
     */
    enum class Stage {
        Extract,
        Transform,
        Load
    };

    /**
     * @brief Default constructor
     * 
     * @section details Implementation Details
     * - Initializes start time
     * - Sets default stage to Extract
     */
    ProcessingContext() : start_time_(std::chrono::steady_clock::now()) {}

    /**
     * @brief Get current processing stage
     * @return Stage Current stage
     * 
     * @section details Implementation Details
     * - Thread-safe
     * - No state modification
     */
    [[nodiscard]] Stage current_stage() const noexcept { return current_stage_; }

    /**
     * @brief Set current processing stage
     * @param stage Processing stage
     * 
     * @section details Implementation Details
     * - Thread-safe
     * - Updates internal state
     */
    void set_stage(Stage stage) { current_stage_ = stage; }

    /**
     * @brief Get job ID
     * @return const std::string& Job identifier
     * 
     * @section details Implementation Details
     * - Thread-safe
     * - No state modification
     */
    [[nodiscard]] const std::string& job_id() const noexcept { return job_id_; }

    /**
     * @brief Set job ID
     * @param id Job identifier
     * 
     * @section details Implementation Details
     * - Thread-safe
     * - Updates internal state
     */
    void set_job_id(std::string id) { job_id_ = std::move(id); }

    /**
     * @brief Increment processed record count
     * @param count Number of records to add
     * 
     * @section details Implementation Details
     * - Thread-safe
     * - Atomic operation
     */
    void increment_processed(size_t count = 1) { processed_count_ += count; }

    /**
     * @brief Increment error count
     * @param count Number of errors to add
     * 
     * @section details Implementation Details
     * - Thread-safe
     * - Atomic operation
     */
    void increment_errors(size_t count = 1) { error_count_ += count; }

    /**
     * @brief Get processed record count
     * @return size_t Number of processed records
     * 
     * @section details Implementation Details
     * - Thread-safe
     * - No state modification
     */
    [[nodiscard]] size_t processed_count() const noexcept { return processed_count_; }

    /**
     * @brief Get error count
     * @return size_t Number of errors
     * 
     * @section details Implementation Details
     * - Thread-safe
     * - No state modification
     */
    [[nodiscard]] size_t error_count() const noexcept { return error_count_; }

    /**
     * @brief Get elapsed time
     * @return std::chrono::duration<double> Elapsed time in seconds
     * 
     * @section details Implementation Details
     * - Thread-safe
     * - Calculates current duration
     */
    [[nodiscard]] std::chrono::duration<double> elapsed_time() const {
        return std::chrono::steady_clock::now() - start_time_;
    }

    /**
     * @brief Store context data
     * @param key Data key
     * @param value Data value
     * 
     * @section details Implementation Details
     * - Thread-safe
     * - Stores in context_data_
     */
    void set_data(const std::string& key, std::any value);

    /**
     * @brief Retrieve context data
     * @param key Data key
     * @return std::optional<std::any> Data value if exists
     * 
     * @section details Implementation Details
     * - Thread-safe
     * - No state modification
     */
    [[nodiscard]] std::optional<std::any> get_data(const std::string& key) const;

    /**
     * @brief Log message
     * @param level Log level
     * @param message Log message
     * 
     * @section details Implementation Details
     * - Thread-safe
     * - Uses spdlog
     */
    void log(const std::string& level, const std::string& message);

private:
    Stage current_stage_{Stage::Extract};  ///< Current processing stage
    std::string job_id_;  ///< Job identifier
    size_t processed_count_{0};  ///< Number of processed records
    size_t error_count_{0};  ///< Number of errors
    std::chrono::steady_clock::time_point start_time_;  ///< Processing start time
    mutable std::mutex context_mutex_;  ///< Thread safety mutex
    std::unordered_map<std::string, std::any> context_data_;  ///< Context data storage
};

/**
 * @interface IExtractor
 * @brief Base interface for data extraction
 * @ingroup core
 * 
 * @section description Description
 * Defines the contract for data extraction components. Implementations are responsible
 * for reading data from source systems and converting it to the Record format.
 * 
 * @section features Features
 * - Batch extraction
 * - Progress tracking
 * - Resource management
 * - Statistics collection
 * 
 * @section thread_safety Thread Safety
 * Implementations must be thread-safe.
 * 
 * @section dependencies Dependencies
 * - ProcessingContext: Execution context
 * - Record: Data representation
 */
class IExtractor {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~IExtractor() = default;

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Sets up resources
     * - Validates configuration
     * - Initializes state
     */
    virtual void initialize(const std::unordered_map<std::string, std::any>& config,
                           ProcessingContext& context) = 0;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return RecordBatch Extracted records (empty if no more data)
     * 
     * @section details Implementation Details
     * - Reads source data
     * - Converts to records
     * - Updates context
     */
    virtual RecordBatch extract_batch(size_t batch_size, ProcessingContext& context) = 0;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     * 
     * @section details Implementation Details
     * - Checks source state
     * - No state modification
     */
    virtual bool has_more_data() const = 0;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     * 
     * @section details Implementation Details
     * - Returns type name
     * - No state modification
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Finalize extraction and clean up resources
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Releases resources
     * - Updates context
     */
    virtual void finalize(ProcessingContext& context) = 0;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     * 
     * @section details Implementation Details
     * - Returns statistics
     * - No state modification
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() const = 0;
};

/**
 * @interface ITransformer
 * @brief Base interface for data transformation
 * @ingroup core
 * 
 * @details
 * Defines the contract for data transformation components. Implementations apply
 * business rules, validate data quality, and enrich records with additional data.
 * 
 * @dependencies
 * - ProcessingContext: Execution context
 * - Record: Data representation
 * - ValidationResult: Validation results
 * 
 * @see @ref transform "Transformation Components"
 */
class ITransformer {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~ITransformer() = default;

    /**
     * @brief Initialize the transformer
     * @param config Configuration parameters
     * @param context Processing context
     */
    virtual void initialize(const std::unordered_map<std::string, std::any>& config,
                           ProcessingContext& context) = 0;

    /**
     * @brief Transform a single record
     * @param record Record to transform
     * @param context Processing context
     * @return std::optional<Record> Transformed record or empty if filtered out
     */
    virtual std::optional<Record> transform(const Record& record,
                                          ProcessingContext& context) = 0;

    /**
     * @brief Transform a batch of records
     * @param batch Batch to transform
     * @param context Processing context
     * @return RecordBatch Transformed batch
     */
    virtual RecordBatch transform_batch(const RecordBatch& batch,
                                       ProcessingContext& context) = 0;

    /**
     * @brief Get transformer type name
     * @return std::string Transformer type identifier
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Validate record according to transformation rules
     * @param record Record to validate
     * @return ValidationResult Validation result
     */
    virtual ValidationResult validate(const Record& record) const = 0;

    /**
     * @brief Get transformation statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() const = 0;
};

/**
 * @interface ILoader
 * @brief Base interface for data loading
 * @ingroup core
 * 
 * @details
 * Defines the contract for data loading components. Implementations handle writing
 * data to target systems, managing transactions, and handling loading errors.
 * 
 * @dependencies
 * - ProcessingContext: Execution context
 * - Record: Data representation
 * 
 * @see @ref load "Loading Components"
 */
class ILoader {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~ILoader() = default;

    /**
     * @brief Initialize the loader
     * @param config Configuration parameters
     * @param context Processing context
     */
    virtual void initialize(const std::unordered_map<std::string, std::any>& config,
                           ProcessingContext& context) = 0;

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     */
    virtual bool load(const Record& record, ProcessingContext& context) = 0;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     */
    virtual size_t load_batch(const RecordBatch& batch, ProcessingContext& context) = 0;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     */
    virtual void commit(ProcessingContext& context) = 0;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     */
    virtual void rollback(ProcessingContext& context) = 0;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Finalize loading and clean up resources
     * @param context Processing context
     */
    virtual void finalize(ProcessingContext& context) = 0;

    /**
     * @brief Get loading statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() const = 0;
};

/**
 * @class ValidationResult
 * @brief Validation result for record validation
 * @ingroup core
 * 
 * @details
 * Represents the result of record validation operations. Tracks validation status,
 * collects validation errors, and provides detailed error information.
 * 
 * @dependencies
 * - Record: Data validation
 * 
 * @see @ref validation "Validation Framework"
 */
class ValidationResult {
public:
    struct ValidationError {
        std::string field_name;
        std::string error_message;
        std::string rule_name;
    };

    /**
     * @brief Default constructor (valid result)
     */
    ValidationResult() = default;

    /**
     * @brief Add validation error
     * @param error Validation error
     */
    void add_error(ValidationError error) {
        errors_.push_back(std::move(error));
        is_valid_ = false;
    }

    /**
     * @brief Add validation error with field, message and rule
     * @param field_name Field name
     * @param error_message Error message
     * @param rule_name Rule name
     */
    void add_error(const std::string& field_name, 
                  const std::string& error_message,
                  const std::string& rule_name) {
        errors_.push_back({field_name, error_message, rule_name});
        is_valid_ = false;
    }

    /**
     * @brief Check if validation passed
     * @return bool True if valid
     */
    [[nodiscard]] bool is_valid() const noexcept { return is_valid_; }

    /**
     * @brief Get validation errors
     * @return const std::vector<ValidationError>& Vector of errors
     */
    [[nodiscard]] const std::vector<ValidationError>& errors() const noexcept {
        return errors_;
    }

    /**
     * @brief Get error count
     * @return size_t Number of errors
     */
    [[nodiscard]] size_t error_count() const noexcept { return errors_.size(); }
    
    /**
     * @brief Get error messages as a formatted string
     * @return std::string Formatted error messages
     */
    [[nodiscard]] std::string error_messages() const {
        if (errors_.empty()) {
            return "";
        }
        
        std::string result;
        for (const auto& error : errors_) {
            result += std::format("Field '{}': {} (rule: {})\n", 
                                 error.field_name, 
                                 error.error_message,
                                 error.rule_name);
        }
        return result;
    }
    
    /**
     * @brief Merge another validation result
     * @param other Other validation result
     */
    void merge(const ValidationResult& other) {
        if (!other.is_valid()) {
            is_valid_ = false;
            errors_.insert(errors_.end(), other.errors().begin(), other.errors().end());
        }
    }

private:
    bool is_valid_{true};
    std::vector<ValidationError> errors_;
};

/**
 * @class ComponentFactory
 * @brief Factory for creating ETL components
 * @ingroup core
 * 
 * @details
 * Creates and manages ETL component instances. Provides a registry for component
 * creators and handles component lifecycle management.
 * 
 * @tparam T The type of component to create (must implement IExtractor, ITransformer, or ILoader)
 * 
 * @dependencies
 * - IExtractor/ITransformer/ILoader: Component interfaces
 * 
 * @see @ref factory "Component Factory Pattern"
 */
template<typename T>
class ComponentFactory {
public:
    using Creator = std::function<std::unique_ptr<T>()>;

    /**
     * @brief Register component creator
     * @param type Component type name
     * @param creator Creator function
     */
    void register_creator(const std::string& type, Creator creator) {
        creators_[type] = std::move(creator);
    }

    /**
     * @brief Create component by type
     * @param type Component type name
     * @return std::unique_ptr<T> Created component
     */
    [[nodiscard]] std::unique_ptr<T> create(const std::string& type) const {
        auto it = creators_.find(type);
        if (it != creators_.end()) {
            return it->second();
        }
        throw common::ConfigurationException(
            std::format("Unknown component type: '{}'", type));
    }

    /**
     * @brief Get registered types
     * @return std::vector<std::string> Vector of type names
     */
    [[nodiscard]] std::vector<std::string> get_registered_types() const {
        std::vector<std::string> types;
        types.reserve(creators_.size());
        for (const auto& [type, _] : creators_) {
            types.push_back(type);
        }
        return types;
    }
    
    /**
     * @brief Check if a type is registered
     * @param type Component type name
     * @return bool True if type is registered
     */
    [[nodiscard]] bool is_registered(const std::string& type) const {
        return creators_.find(type) != creators_.end();
    }
    
    /**
     * @brief Get number of registered types
     * @return size_t Number of registered types
     */
    [[nodiscard]] size_t registered_count() const noexcept {
        return creators_.size();
    }

private:
    std::unordered_map<std::string, Creator> creators_;
};

/**
 * @brief Interface for data processing operations
 * 
 * @section description Description
 * Defines the contract for data processing operations in the ETL pipeline.
 * Implementations should handle data transformation, validation, and processing
 * according to OMOP CDM specifications.
 * 
 * @section thread_safety Thread Safety
 * Implementations must be thread-safe as this interface may be used in
 * concurrent processing scenarios.
 * 
 * @section error_handling Error Handling
 * Implementations should throw appropriate exceptions for error conditions
 * and provide detailed error messages.
 */
class IDataProcessor {
public:
    /**
     * @brief Virtual destructor for proper cleanup
     */
    virtual ~IDataProcessor() = default;

    /**
     * @brief Process a batch of data
     * 
     * @param data The input data to process
     * @return bool True if processing was successful
     * @throw std::runtime_error if processing fails
     * 
     * @section details Implementation Details
     * - Validates input data format
     * - Applies transformations according to OMOP CDM rules
     * - Performs data quality checks
     * - Returns success status
     */
    virtual bool processData(const std::vector<std::string>& data) = 0;

    /**
     * @brief Get the current processing status
     * 
     * @return std::string Status message
     * 
     * @section details Implementation Details
     * - Returns current processing state
     * - Includes progress information if available
     * - May include error messages if processing failed
     */
    virtual std::string getStatus() const = 0;
};

/**
 * @brief Interface for data validation operations
 * 
 * @section description Description
 * Defines the contract for data validation in the ETL pipeline.
 * Implementations should ensure data quality and compliance with
 * OMOP CDM specifications.
 * 
 * @section thread_safety Thread Safety
 * Implementations must be thread-safe as this interface may be used in
 * concurrent processing scenarios.
 */
class IDataValidator {
public:
    /**
     * @brief Virtual destructor for proper cleanup
     */
    virtual ~IDataValidator() = default;

    /**
     * @brief Validate a data record
     * 
     * @param record The data record to validate
     * @return bool True if validation passes
     * @throw std::invalid_argument if record format is invalid
     * 
     * @section details Implementation Details
     * - Checks data format and structure
     * - Validates against OMOP CDM rules
     * - Performs data quality checks
     * - Returns validation status
     */
    virtual bool validateRecord(const std::string& record) = 0;

    /**
     * @brief Get validation errors
     * 
     * @return std::vector<std::string> List of validation errors
     * 
     * @section details Implementation Details
     * - Returns all validation errors found
     * - Errors are ordered by severity
     * - Includes detailed error messages
     */
    virtual std::vector<std::string> getErrors() const = 0;
};

/**
 * @brief Interface for data source operations
 * 
 * @section description Description
 * Defines the contract for data source operations in the ETL pipeline.
 * Implementations should handle data retrieval from various sources
 * according to OMOP CDM specifications.
 * 
 * @section thread_safety Thread Safety
 * Implementations must be thread-safe as this interface may be used in
 * concurrent processing scenarios.
 */
class IDataSource {
public:
    /**
     * @brief Virtual destructor for proper cleanup
     */
    virtual ~IDataSource() = default;

    /**
     * @brief Read data from the source
     * 
     * @param batchSize Number of records to read
     * @return std::optional<std::vector<std::string>> Batch of records
     * @throw std::runtime_error if reading fails
     * 
     * @section details Implementation Details
     * - Reads specified number of records
     * - Handles source-specific formatting
     * - Returns empty optional if no more data
     */
    virtual std::optional<std::vector<std::string>> readData(size_t batchSize) = 0;

    /**
     * @brief Check if more data is available
     * 
     * @return bool True if more data can be read
     * 
     * @section details Implementation Details
     * - Checks source for remaining data
     * - Considers source-specific conditions
     * - Returns availability status
     */
    virtual bool hasMoreData() const = 0;
};

/**
 * @brief Interface for data sink operations
 * 
 * @section description Description
 * Defines the contract for data sink operations in the ETL pipeline.
 * Implementations should handle data writing to various destinations
 * according to OMOP CDM specifications.
 * 
 * @section thread_safety Thread Safety
 * Implementations must be thread-safe as this interface may be used in
 * concurrent processing scenarios.
 */
class IDataSink {
public:
    /**
     * @brief Virtual destructor for proper cleanup
     */
    virtual ~IDataSink() = default;

    /**
     * @brief Write data to the sink
     * 
     * @param data The data to write
     * @return bool True if writing was successful
     * @throw std::runtime_error if writing fails
     * 
     * @section details Implementation Details
     * - Writes data to destination
     * - Handles sink-specific formatting
     * - Returns success status
     */
    virtual bool writeData(const std::vector<std::string>& data) = 0;

    /**
     * @brief Flush any buffered data
     * 
     * @return bool True if flush was successful
     * @throw std::runtime_error if flush fails
     * 
     * @section details Implementation Details
     * - Ensures all data is written
     * - Handles sink-specific cleanup
     * - Returns success status
     */
    virtual bool flush() = 0;
};

} // namespace omop::core