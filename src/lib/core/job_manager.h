/**
 * @file job_manager.h
 * @brief Job management system for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 * @version 1.0
 * 
 * @section overview Overview
 * This header file defines the job management system for the OMOP ETL pipeline.
 * It provides functionality for orchestrating, monitoring, and managing ETL jobs
 * throughout their lifecycle, including job submission, execution, monitoring,
 * and error handling.
 * 
 * @section design Design Principles
 * - Thread Safety: All operations are thread-safe
 * - Priority-based Scheduling: Jobs are executed based on priority
 * - Fault Tolerance: Built-in retry mechanism and checkpointing
 * - Resource Management: Controls concurrent job execution
 * 
 * @section components Components
 * - Job: Represents an individual ETL job
 * - JobManager: Orchestrates job execution and management
 * - JobConfig: Configuration for job execution
 * - JobStatistics: Runtime statistics and metrics
 * 
 * @section usage Usage
 * To use the job management system:
 * 1. Create a JobManager instance
 * 2. Configure and submit jobs
 * 3. Monitor job progress and status
 * 4. Handle job completion and errors
 * 
 * @section example Example
 * @code
 * auto job_manager = std::make_shared<JobManager>(config, logger);
 * JobConfig job_config{...};
 * std::string job_id = job_manager->submitJob(job_config);
 * @endcode
 */

#pragma once

#include <string>
#include <memory>
#include <vector>
#include <unordered_map>
#include <chrono>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <thread>
#include <functional>

#include "interfaces.h"
#include "pipeline.h"
#include "common/logging.h"
#include "common/configuration.h"

namespace omop::core {

// JobStatus is defined in pipeline.h

/**
 * @brief ETL job priority levels
 * 
 * @section description Description
 * Defines the priority levels for ETL jobs, which determine the order
 * of job execution when multiple jobs are queued.
 * 
 * @section values Values
 * - LOW: Low priority jobs
 * - NORMAL: Standard priority jobs
 * - HIGH: High priority jobs
 * - CRITICAL: Critical priority jobs
 */
enum class JobPriority {
    LOW = 0,        ///< Low priority jobs
    NORMAL = 1,     ///< Standard priority jobs
    HIGH = 2,       ///< High priority jobs
    CRITICAL = 3    ///< Critical priority jobs
};

/**
 * @brief Job execution statistics
 * 
 * @section description Description
 * Contains runtime statistics and metrics for an ETL job, including
 * processing rates, resource usage, and timing information.
 * 
 * @section metrics Metrics
 * - Record counts (total, successful, failed, skipped)
 * - Processing rate (records per second)
 * - Resource usage (memory, CPU)
 * - Timing information (elapsed time, stage timings)
 */
struct JobStatistics {
    size_t total_records_processed{0};    ///< Total records processed
    size_t successful_records{0};         ///< Successfully processed records
    size_t failed_records{0};             ///< Failed records
    size_t skipped_records{0};            ///< Skipped records
    double processing_rate{0.0};          ///< Records per second
    size_t memory_usage_mb{0};            ///< Memory usage in MB
    double cpu_usage_percent{0.0};        ///< CPU usage percentage
    std::chrono::duration<double> elapsed_time; ///< Total elapsed time
    std::unordered_map<std::string, double> stage_timings; ///< Timing per stage
};

/**
 * @brief ETL job configuration
 * 
 * @section description Description
 * Contains configuration parameters for an ETL job, including
 * job identification, execution parameters, and metadata.
 * 
 * @section parameters Parameters
 * - Job identification (ID, name)
 * - Pipeline configuration
 * - Execution parameters (priority, retries, timeout)
 * - Checkpointing settings
 * - Custom parameters and metadata
 */
struct JobConfig {
    std::string job_id;                   ///< Unique job identifier
    std::string job_name;                 ///< Human-readable job name
    std::string pipeline_config_path;     ///< Path to pipeline configuration
    JobPriority priority{JobPriority::NORMAL}; ///< Job priority
    size_t max_retries{3};                ///< Maximum retry attempts
    std::chrono::seconds retry_delay{60}; ///< Delay between retries
    std::chrono::seconds timeout{0};      ///< Job timeout (0 = no timeout)
    bool enable_checkpointing{true};      ///< Enable job checkpointing
    size_t checkpoint_interval{10000};    ///< Checkpoint interval (records)
    std::unordered_map<std::string, std::string> parameters; ///< Job parameters
    std::unordered_map<std::string, std::string> metadata;   ///< Job metadata
};

/**
 * @brief ETL job instance
 * 
 * @section description Description
 * Represents an individual ETL job, managing its lifecycle and state.
 * Provides methods for job control, status monitoring, and statistics tracking.
 * 
 * @section lifecycle Lifecycle
 * 1. Creation: Job is created with configuration
 * 2. Execution: Job runs through the pipeline
 * 3. Monitoring: Status and statistics are tracked
 * 4. Completion: Job finishes or fails
 * 
 * @section thread_safety Thread Safety
 * All public methods are thread-safe, using appropriate synchronization
 * mechanisms for concurrent access.
 */
class Job {
public:
    /**
     * @brief Constructor
     * @param config Job configuration
     * @param pipeline Pipeline instance
     * 
     * @section details Implementation Details
     * - Initializes job with provided configuration
     * - Sets up pipeline instance
     * - Initializes statistics and state
     */
    Job(const JobConfig& config, std::unique_ptr<ETLPipeline> pipeline);

    /**
     * @brief Get job ID
     * @return Job identifier
     * 
     * @section details Implementation Details
     * - Returns the unique identifier assigned to the job
     * - Thread-safe access to job ID
     */
    const std::string& getId() const { return config_.job_id; }

    /**
     * @brief Get job name
     * @return Job name
     * 
     * @section details Implementation Details
     * - Returns the human-readable name of the job
     * - Thread-safe access to job name
     */
    const std::string& getName() const { return config_.job_name; }

    /**
     * @brief Get job status
     * @return Current job status
     * 
     * @section details Implementation Details
     * - Returns the current status of the job
     * - Thread-safe access using atomic operations
     */
    JobStatus getStatus() const { return status_.load(); }

    /**
     * @brief Set job status
     * @param status New status
     * 
     * @section details Implementation Details
     * - Updates the job status atomically
     * - May trigger status change notifications
     */
    void setStatus(JobStatus status);

    /**
     * @brief Get job configuration
     * @return Job configuration
     * 
     * @section details Implementation Details
     * - Returns a const reference to the job configuration
     * - Thread-safe access to configuration
     */
    const JobConfig& getConfig() const { return config_; }

    /**
     * @brief Get job statistics
     * @return Job statistics
     * 
     * @section details Implementation Details
     * - Returns current job statistics
     * - Thread-safe access using mutex
     */
    JobStatistics getStatistics() const;

    /**
     * @brief Get pipeline instance
     * @return Pipeline pointer
     * 
     * @section details Implementation Details
     * - Returns pointer to the pipeline instance
     * - Thread-safe access to pipeline
     */
    ETLPipeline* getPipeline() { return pipeline_.get(); }

    /**
     * @brief Get creation time
     * @return Creation timestamp
     * 
     * @section details Implementation Details
     * - Returns the time when the job was created
     * - Thread-safe access to timestamp
     */
    std::chrono::system_clock::time_point getCreationTime() const { return creation_time_; }

    /**
     * @brief Get start time
     * @return Start timestamp
     * 
     * @section details Implementation Details
     * - Returns the time when the job started execution
     * - Thread-safe access to timestamp
     */
    std::chrono::system_clock::time_point getStartTime() const { return start_time_; }

    /**
     * @brief Get end time
     * @return End timestamp
     * 
     * @section details Implementation Details
     * - Returns the time when the job completed
     * - Thread-safe access to timestamp
     */
    std::chrono::system_clock::time_point getEndTime() const { return end_time_; }

    /**
     * @brief Get error message
     * @return Error message if job failed
     * 
     * @section details Implementation Details
     * - Returns the error message if job failed
     * - Thread-safe access to error message
     */
    const std::string& getErrorMessage() const { return error_message_; }

    /**
     * @brief Set error message
     * @param message Error message
     * 
     * @section details Implementation Details
     * - Sets the error message for the job
     * - Thread-safe access to error message
     */
    void setErrorMessage(const std::string& message) { error_message_ = message; }

    /**
     * @brief Get retry count
     * @return Number of retries
     * 
     * @section details Implementation Details
     * - Returns the number of retry attempts
     * - Thread-safe access to retry count
     */
    size_t getRetryCount() const { return retry_count_; }

    /**
     * @brief Increment retry count
     * 
     * @section details Implementation Details
     * - Increments the retry counter
     * - Thread-safe operation
     */
    void incrementRetryCount() { ++retry_count_; }

    /**
     * @brief Update job statistics
     * @param stats New statistics
     * 
     * @section details Implementation Details
     * - Updates job statistics with new values
     * - Thread-safe operation using mutex
     */
    void updateStatistics(const JobStatistics& stats);

    /**
     * @brief Check if job can be retried
     * @return true if job can be retried
     * 
     * @section details Implementation Details
     * - Checks retry count against maximum retries
     * - Thread-safe operation
     */
    bool canRetry() const;

    /**
     * @brief Save checkpoint
     * @return true if checkpoint saved successfully
     * 
     * @section details Implementation Details
     * - Saves current job state to checkpoint file
     * - Thread-safe operation
     */
    bool saveCheckpoint();

    /**
     * @brief Load checkpoint
     * @return true if checkpoint loaded successfully
     * 
     * @section details Implementation Details
     * - Loads job state from checkpoint file
     * - Thread-safe operation
     */
    bool loadCheckpoint();

private:
    friend class JobManager; // Allow JobManager to access private members

    JobConfig config_;                              ///< Job configuration
    std::unique_ptr<ETLPipeline> pipeline_;         ///< Pipeline instance
    std::atomic<JobStatus> status_{JobStatus::Created}; ///< Current status
    JobStatistics statistics_;                      ///< Job statistics
    mutable std::mutex stats_mutex_;                ///< Statistics mutex

    std::chrono::system_clock::time_point creation_time_; ///< Creation time
    std::chrono::system_clock::time_point start_time_;    ///< Start time
    std::chrono::system_clock::time_point end_time_;      ///< End time

    std::string error_message_;                     ///< Error message
    size_t retry_count_{0};                         ///< Retry count
    std::string checkpoint_path_;                   ///< Checkpoint file path
};

/**
 * @brief Job execution context
 * 
 * @section description Description
 * Contains the execution context for a job, including the job instance,
 * logger, and control mechanisms.
 * 
 * @section components Components
 * - Job instance
 * - Logger
 * - Progress callback
 * - Stop flag
 */
struct JobExecutionContext {
    std::shared_ptr<Job> job;                       ///< Job instance
    std::shared_ptr<common::Logger> logger;         ///< Logger instance
    std::function<void(const JobStatistics&)> progress_callback; ///< Progress callback
    std::atomic<bool> should_stop{false};           ///< Stop flag
};

/**
 * @brief Job comparator for priority queue
 * 
 * @section description Description
 * Defines the comparison logic for job priority queue, ensuring
 * jobs are processed in the correct order based on priority and
 * creation time.
 * 
 * @section ordering Ordering
 * 1. Higher priority jobs are processed first
 * 2. For equal priority, older jobs are processed first (FIFO)
 */
struct JobPriorityComparator {
    bool operator()(const std::shared_ptr<Job>& a, const std::shared_ptr<Job>& b) const {
        // Higher priority jobs should be processed first
        if (a->getConfig().priority != b->getConfig().priority) {
            return static_cast<int>(a->getConfig().priority) < 
                   static_cast<int>(b->getConfig().priority);
        }
        // If priorities are equal, older jobs should be processed first (FIFO)
        return a->getCreationTime() > b->getCreationTime();
    }
};

/**
 * @brief Job manager for orchestrating ETL jobs
 * 
 * @section description Description
 * Manages the lifecycle of ETL jobs, including submission, execution,
 * monitoring, and cleanup. Provides thread-safe operations for job
 * management and resource control.
 * 
 * @section features Features
 * - Job submission and scheduling
 * - Priority-based execution
 * - Concurrent job management
 * - Job monitoring and control
 * - Resource management
 * - Error handling and retries
 * 
 * @section thread_safety Thread Safety
 * All public methods are thread-safe, using appropriate synchronization
 * mechanisms for concurrent access.
 */
class JobManager {
public:
    /**
     * @brief Constructor
     * @param config Configuration manager
     * @param logger Logger instance
     * 
     * @section details Implementation Details
     * - Initializes job manager with configuration and logger
     * - Sets up worker threads and queues
     * - Configures resource limits
     */
    JobManager(std::shared_ptr<common::ConfigurationManager> config,
               std::shared_ptr<common::Logger> logger);

    /**
     * @brief Destructor
     */
    ~JobManager();

    /**
     * @brief Start the job manager
     * @return true if started successfully
     * 
     * @section details Implementation Details
     * - Initializes worker threads
     * - Starts job processing
     * - Returns success status
     */
    bool start();

    /**
     * @brief Stop the job manager
     * 
     * @section details Implementation Details
     * - Stops worker threads
     * - Cancels running jobs
     * - Cleans up resources
     */
    void stop();

    /**
     * @brief Submit a new job
     * @param config Job configuration
     * @return Job ID
     * 
     * @section details Implementation Details
     * - Creates new job instance
     * - Adds to priority queue
     * - Returns job identifier
     */
    std::string submitJob(const JobConfig& config);

    /**
     * @brief Cancel a job
     * @param job_id Job identifier
     * @return true if cancelled successfully
     * 
     * @section details Implementation Details
     * - Stops job execution
     * - Updates job status
     * - Returns success status
     */
    bool cancelJob(const std::string& job_id);

    /**
     * @brief Pause a job
     * @param job_id Job identifier
     * @return true if paused successfully
     * 
     * @section details Implementation Details
     * - Pauses job execution
     * - Updates job status
     * - Returns success status
     */
    bool pauseJob(const std::string& job_id);

    /**
     * @brief Resume a job
     * @param job_id Job identifier
     * @return true if resumed successfully
     * 
     * @section details Implementation Details
     * - Resumes job execution
     * - Updates job status
     * - Returns success status
     */
    bool resumeJob(const std::string& job_id);

    /**
     * @brief Retry a failed job
     * @param job_id Job identifier
     * @return true if retry started successfully
     * 
     * @section details Implementation Details
     * - Resets job state
     * - Increments retry count
     * - Restarts job execution
     */
    bool retryJob(const std::string& job_id);

    /**
     * @brief Get number of active jobs
     * @return Active job count
     * 
     * @section details Implementation Details
     * - Returns count of currently running jobs
     * - Thread-safe operation
     */
    size_t getActiveJobCount() const;

    /**
     * @brief Get number of queued jobs
     * @return Queued job count
     * 
     * @section details Implementation Details
     * - Returns count of jobs in queue
     * - Thread-safe operation
     */
    size_t getQueuedJobCount() const;

    /**
     * @brief Set maximum concurrent jobs
     * @param max_jobs Maximum concurrent jobs
     * 
     * @section details Implementation Details
     * - Updates maximum concurrent jobs limit
     * - Thread-safe operation
     */
    void setMaxConcurrentJobs(size_t max_jobs);

    /**
     * @brief Register job event callback
     * @param callback Event callback function
     * 
     * @section details Implementation Details
     * - Registers callback for job status changes
     * - Thread-safe operation
     */
    void registerJobEventCallback(
        std::function<void(const std::string&, JobStatus, JobStatus)> callback);

    /**
     * @brief Clean up old jobs
     * @param age Age threshold
     * @return Number of jobs cleaned up
     * 
     * @section details Implementation Details
     * - Removes completed jobs older than threshold
     * - Thread-safe operation
     */
    size_t cleanupOldJobs(std::chrono::hours age);

    /**
     * @brief Get job by ID
     * @param job_id Job identifier
     * @return Job instance or nullptr if not found
     */
    std::shared_ptr<Job> getJob(const std::string& job_id) const;

    /**
     * @brief Get all jobs
     * @return Vector of all jobs
     */
    std::vector<std::shared_ptr<Job>> getAllJobs() const;

    /**
     * @brief Get jobs by status
     * @param status Job status filter
     * @return Vector of jobs with specified status
     */
    std::vector<std::shared_ptr<Job>> getJobsByStatus(JobStatus status) const;

private:
    /**
     * @brief Worker thread function
     * 
     * @section details Implementation Details
     * - Processes jobs from queue
     * - Handles job execution
     * - Manages job lifecycle
     */
    void workerThread();

    /**
     * @brief Execute a job
     * @param context Job execution context
     * 
     * @section details Implementation Details
     * - Runs job through pipeline
     * - Handles job completion
     * - Updates job statistics
     */
    void executeJob(JobExecutionContext& context);

    /**
     * @brief Create pipeline instance
     * @param config_path Pipeline configuration path
     * @return Pipeline instance
     * 
     * @section details Implementation Details
     * - Creates pipeline from configuration
     * - Initializes pipeline components
     */
    std::unique_ptr<ETLPipeline> createPipeline(const std::string& config_path);

    /**
     * @brief Get next job from queue
     * @return Next job to process
     * 
     * @section details Implementation Details
     * - Gets highest priority job
     * - Thread-safe operation
     */
    std::shared_ptr<Job> getNextJob();

    /**
     * @brief Handle job completion
     * @param job Completed job
     * 
     * @section details Implementation Details
     * - Updates job status
     * - Triggers completion callbacks
     * - Updates statistics
     */
    void handleJobCompletion(std::shared_ptr<Job> job);

    /**
     * @brief Handle job failure
     * @param job Failed job
     * 
     * @section details Implementation Details
     * - Updates job status
     * - Handles retry logic
     * - Triggers failure callbacks
     */
    void handleJobFailure(std::shared_ptr<Job> job);

    /**
     * @brief Notify job status change
     * @param job_id Job identifier
     * @param old_status Previous status
     * @param new_status New status
     * 
     * @section details Implementation Details
     * - Triggers status change callbacks
     * - Updates job state
     */
    void notifyJobStatusChange(const std::string& job_id,
                              JobStatus old_status,
                              JobStatus new_status);

    std::shared_ptr<common::ConfigurationManager> config_; ///< Configuration manager
    std::shared_ptr<common::Logger> logger_;              ///< Logger instance
    std::vector<std::thread> worker_threads_;             ///< Worker threads
    std::priority_queue<std::shared_ptr<Job>,
                       std::vector<std::shared_ptr<Job>>,
                       JobPriorityComparator> job_queue_; ///< Job queue
    std::unordered_map<std::string, std::shared_ptr<Job>> active_jobs_map_; ///< Active jobs map
    std::unordered_map<std::string, std::shared_ptr<Job>> jobs_;     ///< All jobs
    mutable std::mutex queue_mutex_;                     ///< Queue mutex
    mutable std::mutex jobs_mutex_;                      ///< Jobs mutex
    mutable std::mutex callbacks_mutex_;                 ///< Callbacks mutex
    std::condition_variable queue_cv_;                   ///< Queue condition variable
    std::atomic<bool> running_{false};                   ///< Running flag
    std::atomic<size_t> active_jobs_{0};                 ///< Active job count
    std::atomic<size_t> max_concurrent_jobs_{4};         ///< Max concurrent jobs
    std::function<void(const std::string&, JobStatus, JobStatus)> status_callback_; ///< Status callback
    std::vector<std::function<void(const std::string&, JobStatus, JobStatus)>> event_callbacks_; ///< Event callbacks
};

} // namespace omop::core