/**
 * @file job_scheduler.h
 * @brief Job scheduling system for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header file defines the job scheduling system that manages the execution
 * of ETL jobs based on various scheduling strategies. It provides a flexible
 * and extensible framework for scheduling, monitoring, and managing ETL jobs
 * with support for different scheduling algorithms and trigger types.
 * 
 * @section design Design Principles
 * - Flexibility: Multiple scheduling strategies
 * - Reliability: Robust job dependency management
 * - Scalability: Efficient job queue management
 * - Extensibility: Support for custom scheduling policies
 * - Monitoring: Comprehensive job tracking and statistics
 * - Thread Safety: Concurrent operation support
 * 
 * @section components Components
 * - JobScheduler: Main scheduling orchestrator
 * - JobSchedule: Schedule definition and configuration
 * - QueuedJob: Job queue entry with metadata
 * - SchedulingStrategy: Different scheduling algorithms
 * - TriggerType: Various job trigger mechanisms
 * 
 * @section usage Usage
 * To use the job scheduler:
 * 1. Create a JobScheduler instance
 * 2. Define job schedules
 * 3. Start the scheduler
 * 4. Monitor job execution
 * 5. Handle job completion
 * 
 * @section example Example
 * @code
 * auto scheduler = std::make_shared<JobScheduler>(job_manager);
 * JobSchedule schedule;
 * schedule.job_config_id = "daily_extract";
 * schedule.trigger_type = TriggerType::SCHEDULED;
 * schedule.cron_expression = "0 0 * * *";
 * scheduler->addSchedule(schedule);
 * scheduler->start();
 * @endcode
 */

#pragma once

#include <string>
#include <memory>
#include <vector>
#include <queue>
#include <chrono>
#include <functional>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>

#include "interfaces.h"
#include "pipeline.h"
#include "job_manager.h"

namespace omop::core {

/**
 * @brief Job scheduling strategy
 * 
 * @section description Description
 * Defines different algorithms for determining the order of job execution
 * in the scheduler. Each strategy has different characteristics and is
 * suitable for different use cases.
 * 
 * @section strategies Strategies
 * - FIFO: Simple first-in-first-out ordering
 * - PRIORITY: Based on job priority levels
 * - ROUND_ROBIN: Equal time distribution
 * - FAIR_SHARE: Resource allocation fairness
 * - DEADLINE: Based on job deadlines
 * 
 * @section thread_safety Thread Safety
 * Strategy selection is thread-safe and atomic.
 */
enum class SchedulingStrategy {
    FIFO,           ///< First In First Out
    PRIORITY,       ///< Priority-based scheduling
    ROUND_ROBIN,    ///< Round-robin scheduling
    FAIR_SHARE,     ///< Fair share between users/groups
    DEADLINE        ///< Deadline-based scheduling
};

/**
 * @brief Scheduled job trigger type
 * 
 * @section description Description
 * Defines different mechanisms for triggering job execution.
 * Each trigger type has specific characteristics and use cases.
 * 
 * @section types Types
 * - MANUAL: User-initiated execution
 * - SCHEDULED: Time-based execution
 * - EVENT: Event-driven execution
 * - DEPENDENCY: Dependency-based execution
 * - FILE_WATCH: File system change detection
 * 
 * @section thread_safety Thread Safety
 * Trigger type changes are thread-safe and atomic.
 */
enum class TriggerType {
    MANUAL,         ///< Manual trigger
    SCHEDULED,      ///< Time-based schedule
    EVENT,          ///< Event-based trigger
    DEPENDENCY,     ///< Dependency-based trigger
    FILE_WATCH      ///< File system watch trigger
};

/**
 * @brief Job schedule definition
 * 
 * @section description Description
 * Contains all configuration and metadata for a scheduled job,
 * including trigger conditions, timing information, and dependencies.
 * 
 * @section fields Fields
 * - schedule_id: Unique schedule identifier
 * - job_config_id: Associated job configuration
 * - trigger_type: How the job is triggered
 * - cron_expression: Time-based schedule
 * - next_run: Next scheduled execution
 * - last_run: Last execution time
 * - enabled: Schedule status
 * - dependencies: Required job completions
 * - parameters: Additional configuration
 * 
 * @section thread_safety Thread Safety
 * Schedule modifications are thread-safe with appropriate synchronization.
 */
struct JobSchedule {
    std::string schedule_id;                    ///< Schedule identifier
    std::string job_config_id;                  ///< Job configuration ID
    TriggerType trigger_type{TriggerType::MANUAL}; ///< Trigger type
    std::string cron_expression;                ///< Cron expression for scheduled jobs
    std::chrono::system_clock::time_point next_run; ///< Next scheduled run time
    std::chrono::system_clock::time_point last_run; ///< Last run time
    bool enabled{true};                         ///< Whether schedule is enabled
    std::vector<std::string> dependencies;      ///< Job dependencies
    std::unordered_map<std::string, std::string> parameters; ///< Schedule parameters
};

/**
 * @brief Job queue entry
 * 
 * @section description Description
 * Represents a job waiting to be executed in the scheduler's queue.
 * Contains all necessary information for job execution and tracking.
 * 
 * @section fields Fields
 * - job_id: Unique job identifier
 * - job_config: Job configuration
 * - priority: Execution priority
 * - enqueue_time: When job was queued
 * - deadline: Latest execution time
 * - dependencies: Required completions
 * - callback: Completion handler
 * 
 * @section thread_safety Thread Safety
 * Queue entry modifications are thread-safe with appropriate synchronization.
 */
struct QueuedJob {
    std::string job_id;                         ///< Job identifier
    JobConfig job_config;                       ///< Job configuration
    JobPriority priority;                       ///< Job priority
    std::chrono::system_clock::time_point enqueue_time; ///< Enqueue time
    std::chrono::system_clock::time_point deadline; ///< Job deadline
    std::vector<std::string> dependencies;      ///< Job dependencies
    std::function<void()> callback;             ///< Completion callback
};

/**
 * @brief Job scheduler for managing ETL job execution
 * 
 * @section description Description
 * Provides advanced scheduling capabilities for ETL jobs, including
 * cron-based scheduling, dependency management, and various scheduling
 * strategies. Manages job queues, execution order, and monitoring.
 * 
 * @section features Features
 * - Multiple scheduling strategies
 * - Cron-based scheduling
 * - Job dependency management
 * - Priority-based execution
 * - Job completion callbacks
 * - Comprehensive monitoring
 * - Resource management
 * - Error handling
 * 
 * @section thread_safety Thread Safety
 * The scheduler is thread-safe and can be used from multiple threads.
 * All public methods are synchronized appropriately.
 * 
 * @section memory Memory Management
 * - RAII resource management
 * - Smart pointer ownership
 * - Automatic cleanup
 */
class JobScheduler {
public:
    /**
     * @brief Constructor
     * @param job_manager Job manager instance
     * @param strategy Scheduling strategy
     * 
     * @section details Implementation Details
     * - Initializes scheduler with job manager
     * - Sets scheduling strategy
     * - Prepares internal data structures
     * - Thread-safe initialization
     */
    JobScheduler(std::shared_ptr<JobManager> job_manager,
                 SchedulingStrategy strategy = SchedulingStrategy::PRIORITY);

    /**
     * @brief Destructor
     * 
     * @section details Implementation Details
     * - Stops scheduler if running
     * - Cleans up resources
     * - Thread-safe cleanup
     */
    ~JobScheduler();

    /**
     * @brief Start the scheduler
     * @return true if started successfully
     * 
     * @section details Implementation Details
     * - Initializes scheduler thread
     * - Starts processing jobs
     * - Returns false if already running
     * - Thread-safe operation
     */
    bool start();

    /**
     * @brief Stop the scheduler
     * 
     * @section details Implementation Details
     * - Signals scheduler thread to stop
     * - Waits for completion
     * - Cleans up resources
     * - Thread-safe operation
     */
    void stop();

    /**
     * @brief Add a job schedule
     * @param schedule Job schedule definition
     * @return Schedule ID
     * 
     * @section details Implementation Details
     * - Validates schedule configuration
     * - Generates unique schedule ID
     * - Adds to schedule collection
     * - Thread-safe operation
     */
    std::string addSchedule(const JobSchedule& schedule);

    /**
     * @brief Remove a job schedule
     * @param schedule_id Schedule identifier
     * @return true if removed successfully
     * 
     * @section details Implementation Details
     * - Removes schedule from collection
     * - Cancels pending executions
     * - Thread-safe operation
     */
    bool removeSchedule(const std::string& schedule_id);

    /**
     * @brief Update a job schedule
     * @param schedule_id Schedule identifier
     * @param schedule New schedule definition
     * @return true if updated successfully
     * 
     * @section details Implementation Details
     * - Updates schedule configuration
     * - Recalculates next run time
     * - Thread-safe operation
     */
    bool updateSchedule(const std::string& schedule_id, const JobSchedule& schedule);

    /**
     * @brief Enable or disable a schedule
     * @param schedule_id Schedule identifier
     * @param enabled New enabled state
     * @return true if state changed successfully
     * 
     * @section details Implementation Details
     * - Updates schedule enabled state
     * - Affects future executions
     * - Thread-safe operation
     */
    bool setScheduleEnabled(const std::string& schedule_id, bool enabled);

    /**
     * @brief Submit a job for execution
     * @param job_config Job configuration
     * @param priority Job priority
     * @param callback Optional completion callback
     * @return Job ID
     * 
     * @section details Implementation Details
     * - Creates job queue entry
     * - Adds to execution queue
     * - Returns job identifier
     * - Thread-safe operation
     */
    std::string submitJob(const JobConfig& job_config,
                         JobPriority priority = JobPriority::NORMAL,
                         std::function<void()> callback = nullptr);

    /**
     * @brief Set scheduling strategy
     * @param strategy New scheduling strategy
     * 
     * @section details Implementation Details
     * - Updates scheduling algorithm
     * - Reorders job queue
     * - Thread-safe operation
     */
    void setSchedulingStrategy(SchedulingStrategy strategy);

    /**
     * @brief Register job completion callback
     * @param callback Completion callback function
     * 
     * @section details Implementation Details
     * - Sets global completion handler
     * - Called for all job completions
     * - Thread-safe operation
     */
    void registerJobCompletionCallback(
        std::function<void(const std::string&, JobStatus)> callback);

    /**
     * @brief Trigger a scheduled job immediately
     * @param schedule_id Schedule identifier
     * @return Job ID if triggered
     * 
     * @section details Implementation Details
     * - Validates schedule exists
     * - Creates job configuration
     * - Submits for execution
     */
    std::optional<std::string> triggerSchedule(const std::string& schedule_id);

    /**
     * @brief Get queued jobs
     * @return Vector of queued jobs
     * 
     * @section details Implementation Details
     * - Returns copy of queue contents
     * - No exceptions thrown
     */
    std::vector<QueuedJob> getQueuedJobs() const;

    /**
     * @brief Get scheduler statistics
     * @return Statistics map
     * 
     * @section details Implementation Details
     * - Returns current statistics
     * - Includes queue size, execution times
     * - No exceptions thrown
     */
    std::unordered_map<std::string, std::any> getStatistics() const;

    /**
     * @brief Get schedule by ID
     * @param schedule_id Schedule identifier
     * @return Schedule if found
     * 
     * @section details Implementation Details
     * - Returns copy of schedule
     * - Returns empty optional if not found
     */
    std::optional<JobSchedule> getSchedule(const std::string& schedule_id) const;

    /**
     * @brief Get all schedules
     * @return Vector of all schedules
     * 
     * @section details Implementation Details
     * - Returns copy of all schedules
     * - No exceptions thrown
     */
    std::vector<JobSchedule> getAllSchedules() const;

private:
    /**
     * @brief Main scheduler loop
     * 
     * @section details Implementation Details
     * - Processes scheduled jobs
     * - Manages job queue
     * - Handles job execution
     * - Thread-safe operation
     */
    void schedulerLoop();

    /**
     * @brief Process scheduled jobs
     * 
     * @section details Implementation Details
     * - Checks schedule triggers
     * - Queues due jobs
     * - Updates next run times
     * - Thread-safe operation
     */
    void processScheduledJobs();

    /**
     * @brief Process job queue
     * 
     * @section details Implementation Details
     * - Executes queued jobs
     * - Manages dependencies
     * - Updates job status
     * - Thread-safe operation
     */
    void processJobQueue();

    /**
     * @brief Check job dependencies
     * @param job Job to check
     * @return true if dependencies satisfied
     * 
     * @section details Implementation Details
     * - Verifies dependency completion
     * - Checks job status
     * - Thread-safe operation
     */
    bool checkDependencies(const QueuedJob& job) const;

    /**
     * @brief Calculate next run time from cron expression
     * @param cron_expr Cron expression
     * @param from_time Starting time
     * @return Next run time
     * 
     * @section details Implementation Details
     * - Parses cron expression
     * - Calculates next occurrence
     * - Returns system time point
     */
    std::chrono::system_clock::time_point calculateNextRunTime(
        const std::string& cron_expr,
        std::chrono::system_clock::time_point from_time) const;

    /**
     * @brief Get next job based on scheduling strategy
     * @return Next job to execute
     * 
     * @section details Implementation Details
     * - Applies scheduling strategy
     * - Checks dependencies
     * - Returns next job if ready
     */
    std::optional<QueuedJob> getNextJob();

    /**
     * @brief Job queue comparator
     * 
     * @section description Description
     * Defines ordering of jobs in the queue based on
     * the current scheduling strategy.
     * 
     * @section thread_safety Thread Safety
     * Comparator operations are thread-safe.
     */
    struct JobComparator {
        SchedulingStrategy strategy;  ///< Current strategy

        /**
         * @brief Compare two jobs
         * @param a First job
         * @param b Second job
         * @return true if a should come before b
         * 
         * @section details Implementation Details
         * - Implements strategy-specific ordering
         * - Thread-safe operation
         */
        bool operator()(const QueuedJob& a, const QueuedJob& b) const;
    };

private:
    std::shared_ptr<JobManager> job_manager_;    ///< Job manager
    SchedulingStrategy strategy_;                ///< Scheduling strategy
    std::unordered_map<std::string, JobSchedule> schedules_; ///< Schedule collection
    std::priority_queue<QueuedJob, std::vector<QueuedJob>, JobComparator> job_queue_; ///< Job queue
    std::thread scheduler_thread_;               ///< Scheduler thread
    std::atomic<bool> running_{false};          ///< Running flag
    mutable std::mutex mutex_;                  ///< Synchronization mutex
    mutable std::mutex schedules_mutex_;        ///< Schedules mutex
    mutable std::mutex queue_mutex_;            ///< Queue mutex
    mutable std::mutex callbacks_mutex_;        ///< Callbacks mutex
    mutable std::mutex completed_mutex_;        ///< Completed jobs mutex
    std::condition_variable cv_;                ///< Thread synchronization
    std::condition_variable queue_cv_;          ///< Queue condition variable
    std::function<void(const std::string&, JobStatus)> completion_callback_; ///< Completion handler
    std::vector<std::function<void(const std::string&, JobStatus)>> completion_callbacks_; ///< Completion callbacks
    std::unordered_map<std::string, JobStatus> completed_jobs_; ///< Completed jobs tracking
    std::atomic<size_t> jobs_scheduled_{0};     ///< Jobs scheduled counter
    std::atomic<size_t> jobs_executed_{0};      ///< Jobs executed counter
    std::atomic<size_t> jobs_failed_{0};        ///< Jobs failed counter
};

} // namespace omop::core