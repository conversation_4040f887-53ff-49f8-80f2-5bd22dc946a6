/**
 * @file table_definitions.h
 * @brief OMOP CDM table definitions and SQL generation
 * <AUTHOR> ETL Team
 * @date 2024
 * @version 1.0
 * 
 * @section overview Overview
 * This header provides comprehensive definitions for OMOP CDM tables, including
 * field definitions, indexes, constraints, and SQL generation capabilities for
 * multiple database dialects.
 * 
 * @section design Design Principles
 * - Database Agnostic: Support for multiple database platforms
 * - Type Safety: Strong typing for all definitions
 * - Extensibility: Easy to add new tables and features
 * - SQL Generation: Platform-specific SQL syntax
 * - Dependency Management: Proper table creation order
 * 
 * @section components Components
 * - FieldDefinition: Column metadata
 * - IndexDefinition: Index specifications
 * - ForeignKeyDefinition: Referential integrity
 * - TableDefinition: Complete table metadata
 * - SchemaDefinitions: OMOP CDM schema
 * 
 * @section usage Usage
 * To use table definitions:
 * 1. Get schema instance
 * 2. Retrieve table definitions
 * 3. Generate SQL statements
 * 4. Execute in target database
 * 
 * @section example Example
 * @code
 * auto& schema = SchemaDefinitions::instance();
 * auto table = schema.get_table("person");
 * auto sql = table->generate_create_table_sql("cdm", DatabaseDialect::PostgreSQL);
 * @endcode
 */

#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <any>
#include <mutex>

namespace omop::cdm {

/**
 * @brief Field definition for OMOP CDM tables
 * 
 * @section description Description
 * Represents a single column in an OMOP CDM table with all its metadata
 * including data type, nullability, and constraints.
 * 
 * @section fields Fields
 * - name: Column name
 * - data_type: SQL data type
 * - is_nullable: Nullability flag
 * - is_primary_key: Primary key flag
 * - default_value: Default value
 * - comment: Column description
 */
struct FieldDefinition {
    std::string name;  ///< Column name
    std::string data_type;  ///< SQL data type
    bool is_nullable;  ///< Nullability flag
    bool is_primary_key;  ///< Primary key flag
    std::string default_value;  ///< Default value
    std::string comment;  ///< Column description
};

/**
 * @brief Index definition for OMOP CDM tables
 * 
 * @section description Description
 * Defines database indexes to improve query performance on OMOP tables.
 * Supports both unique and non-unique indexes, as well as clustered indexes
 * for databases that support them (e.g., SQL Server).
 * 
 * @section fields Fields
 * - name: Index name
 * - table_name: Target table
 * - columns: Indexed columns
 * - is_unique: Uniqueness flag
 * - is_clustered: Clustering flag
 */
struct IndexDefinition {
    std::string name;  ///< Index name
    std::string table_name;  ///< Target table
    std::vector<std::string> columns;  ///< Indexed columns
    bool is_unique;  ///< Uniqueness flag
    bool is_clustered;  ///< Clustering flag
};

/**
 * @brief Foreign key constraint definition
 * 
 * @section description Description
 * Defines referential integrity constraints between OMOP tables to ensure
 * data consistency across the CDM schema.
 * 
 * @section fields Fields
 * - name: Constraint name
 * - table_name: Source table
 * - column_name: Source column
 * - referenced_table: Target table
 * - referenced_column: Target column
 */
struct ForeignKeyDefinition {
    std::string name;  ///< Constraint name
    std::string table_name;  ///< Source table
    std::string column_name;  ///< Source column
    std::string referenced_table;  ///< Target table
    std::string referenced_column;  ///< Target column
};

/**
 * @brief Database dialect for SQL generation
 * 
 * @section description Description
 * Enumeration of supported database platforms. Each dialect has specific
 * SQL syntax requirements for identifiers, data types, and features.
 * 
 * @section values Values
 * - PostgreSQL: PostgreSQL database
 * - MySQL: MySQL/MariaDB database
 * - SQLServer: Microsoft SQL Server
 * - SQLite: SQLite database
 * - Oracle: Oracle Database
 */
enum class DatabaseDialect {
    PostgreSQL,  ///< PostgreSQL database
    MySQL,  ///< MySQL/MariaDB database
    SQLServer,  ///< Microsoft SQL Server
    SQLite,  ///< SQLite database
    Oracle  ///< Oracle Database
};

/**
 * @brief Table definition containing all metadata
 * 
 * @section description Description
 * Comprehensive representation of an OMOP CDM table including fields,
 * indexes, and constraints. Provides SQL generation methods for different
 * database dialects.
 * 
 * @section features Features
 * - Field management
 * - Index management
 * - Constraint management
 * - SQL generation
 * - Dialect support
 */
class TableDefinition {
public:
    /**
     * @brief Constructor
     * @param name Table name
     * 
     * @section details Implementation Details
     * - Sets name
     * - Thread-safe
     */
    explicit TableDefinition(const std::string& name);

    /**
     * @brief Add field to table
     * @param field Field definition
     * 
     * @section details Implementation Details
     * - Adds field
     * - Thread-safe
     */
    void add_field(const FieldDefinition& field);

    /**
     * @brief Add index to table
     * @param index Index definition
     * 
     * @section details Implementation Details
     * - Adds index
     * - Thread-safe
     */
    void add_index(const IndexDefinition& index);

    /**
     * @brief Add foreign key constraint
     * @param fk Foreign key definition
     * 
     * @section details Implementation Details
     * - Adds constraint
     * - Thread-safe
     */
    void add_foreign_key(const ForeignKeyDefinition& fk);

    /**
     * @brief Get table name
     * @return std::string Table name
     * 
     * @section details Implementation Details
     * - Returns name
     * - Thread-safe
     */
    [[nodiscard]] const std::string& get_name() const { return name_; }

    /**
     * @brief Get all fields
     * @return const std::vector<FieldDefinition>& Fields
     * 
     * @section details Implementation Details
     * - Returns fields
     * - Thread-safe
     */
    [[nodiscard]] const std::vector<FieldDefinition>& get_fields() const { return fields_; }

    /**
     * @brief Get all indexes
     * @return const std::vector<IndexDefinition>& Indexes
     * 
     * @section details Implementation Details
     * - Returns indexes
     * - Thread-safe
     */
    [[nodiscard]] const std::vector<IndexDefinition>& get_indexes() const { return indexes_; }

    /**
     * @brief Get all foreign keys
     * @return const std::vector<ForeignKeyDefinition>& Foreign keys
     * 
     * @section details Implementation Details
     * - Returns constraints
     * - Thread-safe
     */
    [[nodiscard]] const std::vector<ForeignKeyDefinition>& get_foreign_keys() const { return foreign_keys_; }

    /**
     * @brief Generate CREATE TABLE SQL
     * 
     * @section description Description
     * Creates a complete CREATE TABLE statement for the specified database dialect.
     * Includes all fields, data types, constraints, and primary key definition.
     * 
     * @param schema_name Schema name (e.g., "cdm", "public")
     * @param dialect Database dialect for SQL syntax
     * @return std::string CREATE TABLE SQL statement
     * 
     * @section details Implementation Details
     * - Generates SQL
     * - Thread-safe
     */
    [[nodiscard]] std::string generate_create_table_sql(
        const std::string& schema_name,
        DatabaseDialect dialect = DatabaseDialect::PostgreSQL) const;

    /**
     * @brief Generate CREATE INDEX SQL statements
     * 
     * @section description Description
     * Creates all index definitions for the table. Returns a vector as multiple
     * indexes may be defined for a single table.
     * 
     * @param schema_name Schema name
     * @param dialect Database dialect
     * @return std::vector<std::string> CREATE INDEX statements
     * 
     * @section details Implementation Details
     * - Generates SQL
     * - Thread-safe
     */
    [[nodiscard]] std::vector<std::string> generate_create_index_sql(
        const std::string& schema_name,
        DatabaseDialect dialect = DatabaseDialect::PostgreSQL) const;

    /**
     * @brief Generate ALTER TABLE ADD CONSTRAINT SQL statements
     * 
     * @section description Description
     * Creates foreign key constraint definitions. These are typically applied
     * after all tables are created to avoid dependency issues.
     * 
     * @param schema_name Schema name
     * @param dialect Database dialect
     * @return std::vector<std::string> ALTER TABLE statements
     * 
     * @section details Implementation Details
     * - Generates SQL
     * - Thread-safe
     */
    [[nodiscard]] std::vector<std::string> generate_foreign_key_sql(
        const std::string& schema_name,
        DatabaseDialect dialect = DatabaseDialect::PostgreSQL) const;

private:
    std::string name_;  ///< Table name
    std::vector<FieldDefinition> fields_;  ///< Table fields
    std::vector<IndexDefinition> indexes_;  ///< Table indexes
    std::vector<ForeignKeyDefinition> foreign_keys_;  ///< Foreign keys

    /**
     * @brief Convert field type to SQL data type
     * @param field_type Field type
     * @param dialect Database dialect
     * @return std::string SQL data type
     * 
     * @section details Implementation Details
     * - Converts type
     * - Thread-safe
     */
    [[nodiscard]] std::string field_type_to_sql(
        const std::string& field_type,
        DatabaseDialect dialect) const;
};

/**
 * @brief OMOP CDM schema definitions
 * 
 * @section description Description
 * Singleton class containing all OMOP CDM v5.4 table definitions.
 * Provides methods to retrieve table metadata and generate SQL DDL
 * statements for schema creation.
 * 
 * @section features Features
 * - Table definitions
 * - Dependency management
 * - SQL generation
 * - Schema validation
 */
class SchemaDefinitions {
public:
    /**
     * @brief Get singleton instance
     * @return SchemaDefinitions& Instance
     * 
     * @section details Implementation Details
     * - Returns instance
     * - Thread-safe
     */
    static SchemaDefinitions& instance();

    /**
     * @brief Get table definition
     * @param table_name Table name
     * @return const TableDefinition* Table definition or nullptr if not found
     * 
     * @section details Implementation Details
     * - Returns definition
     * - Thread-safe
     */
    [[nodiscard]] const TableDefinition* get_table(const std::string& table_name) const;

    /**
     * @brief Get all table names
     * @return std::vector<std::string> Table names
     * 
     * @section details Implementation Details
     * - Returns names
     * - Thread-safe
     */
    [[nodiscard]] std::vector<std::string> get_table_names() const;

    /**
     * @brief Get tables in dependency order for creation
     * 
     * @section description Description
     * Returns table names ordered to respect foreign key dependencies.
     * Tables without dependencies come first, followed by dependent tables.
     * 
     * @return std::vector<std::string> Ordered table names
     * 
     * @section details Implementation Details
     * - Orders tables
     * - Thread-safe
     */
    [[nodiscard]] std::vector<std::string> get_creation_order() const;

    /**
     * @brief Get tables in dependency order for dropping
     * 
     * @section description Description
     * Returns table names in reverse dependency order for safe deletion.
     * Dependent tables come first to avoid foreign key constraint violations.
     * 
     * @return std::vector<std::string> Ordered table names
     * 
     * @section details Implementation Details
     * - Orders tables
     * - Thread-safe
     */
    [[nodiscard]] std::vector<std::string> get_drop_order() const;

    /**
     * @brief Generate full schema SQL
     * 
     * @section description Description
     * Generates all SQL statements needed to create a complete OMOP CDM schema.
     * Includes schema creation, tables, indexes, and foreign key constraints.
     * 
     * @param schema_name Schema name (default: "cdm")
     * @param dialect Database dialect (default: PostgreSQL)
     * @param include_indexes Include index creation statements (default: true)
     * @param include_constraints Include foreign key constraints (default: true)
     * @return std::vector<std::string> Ordered SQL statements for execution
     * 
     * @section details Implementation Details
     * - Generates SQL
     * - Thread-safe
     */
    [[nodiscard]] std::vector<std::string> generate_schema_sql(
        const std::string& schema_name = "cdm",
        DatabaseDialect dialect = DatabaseDialect::PostgreSQL,
        bool include_indexes = true,
        bool include_constraints = true) const;

private:
    /**
     * @brief Initialize all table definitions
     * 
     * @section details Implementation Details
     * - Initializes tables
     * - Thread-safe
     */
    void initialize_tables();

    /**
     * @brief Initialize concept table definition
     * 
     * @section details Implementation Details
     * - Initializes table
     * - Thread-safe
     */
    void initialize_concept_table();

    /**
     * @brief Initialize person table definition
     * 
     * @section details Implementation Details
     * - Initializes table
     * - Thread-safe
     */
    void initialize_person_table();

    /**
     * @brief Initialize observation period table definition
     * 
     * @section details Implementation Details
     * - Initializes table
     * - Thread-safe
     */
    void initialize_observation_period_table();

    /**
     * @brief Initialize visit occurrence table definition
     * 
     * @section details Implementation Details
     * - Initializes table
     * - Thread-safe
     */
    void initialize_visit_occurrence_table();

    /**
     * @brief Initialize condition occurrence table definition
     * 
     * @section details Implementation Details
     * - Initializes table
     * - Thread-safe
     */
    void initialize_condition_occurrence_table();

    /**
     * @brief Initialize drug exposure table definition
     * 
     * @section details Implementation Details
     * - Initializes table
     * - Thread-safe
     */
    void initialize_drug_exposure_table();

    /**
     * @brief Initialize procedure occurrence table definition
     * 
     * @section details Implementation Details
     * - Initializes table
     * - Thread-safe
     */
    void initialize_procedure_occurrence_table();

    /**
     * @brief Initialize measurement table definition
     * 
     * @section details Implementation Details
     * - Initializes table
     * - Thread-safe
     */
    void initialize_measurement_table();

    /**
     * @brief Initialize observation table definition
     * 
     * @section details Implementation Details
     * - Initializes table
     * - Thread-safe
     */
    void initialize_observation_table();

    /**
     * @brief Initialize death table definition
     * 
     * @section details Implementation Details
     * - Initializes table
     * - Thread-safe
     */
    void initialize_death_table();

    /**
     * @brief Initialize note table definition
     * 
     * @section details Implementation Details
     * - Initializes table
     * - Thread-safe
     */
    void initialize_note_table();

    /**
     * @brief Initialize location table definition
     * 
     * @section details Implementation Details
     * - Initializes table
     * - Thread-safe
     */
    void initialize_location_table();

    /**
     * @brief Initialize care site table definition
     * 
     * @section details Implementation Details
     * - Initializes table
     * - Thread-safe
     */
    void initialize_care_site_table();

    /**
     * @brief Initialize provider table definition
     * 
     * @section details Implementation Details
     * - Initializes table
     * - Thread-safe
     */
    void initialize_provider_table();

    /**
     * @brief Initialize visit detail table definition
     * 
     * @section details Implementation Details
     * - Initializes table
     * - Thread-safe
     */
    void initialize_visit_detail_table();

    /**
     * @brief Quote identifier for SQL
     * @param identifier Identifier to quote
     * @param dialect Database dialect
     * @return std::string Quoted identifier
     * 
     * @section details Implementation Details
     * - Quotes identifier
     * - Thread-safe
     */
    [[nodiscard]] static std::string quote_identifier(
        const std::string& identifier,
        DatabaseDialect dialect);

    /**
     * @brief Format table name with schema
     * @param schema_name Schema name
     * @param table_name Table name
     * @param dialect Database dialect
     * @return std::string Formatted name
     * 
     * @section details Implementation Details
     * - Formats name
     * - Thread-safe
     */
    [[nodiscard]] static std::string format_table_name(
        const std::string& schema_name,
        const std::string& table_name,
        DatabaseDialect dialect);

    /**
     * @brief Get auto-increment syntax
     * @param dialect Database dialect
     * @return std::string Syntax
     * 
     * @section details Implementation Details
     * - Returns syntax
     * - Thread-safe
     */
    [[nodiscard]] static std::string get_auto_increment_syntax(DatabaseDialect dialect);

    /**
     * @brief Get current timestamp function
     * @param dialect Database dialect
     * @return std::string Function
     * 
     * @section details Implementation Details
     * - Returns function
     * - Thread-safe
     */
    [[nodiscard]] static std::string get_current_timestamp_function(DatabaseDialect dialect);

    /**
     * @brief Quote value for SQL
     * @param value Value to quote
     * @param dialect Database dialect
     * @return std::string Quoted value
     * 
     * @section details Implementation Details
     * - Quotes value
     * - Thread-safe
     */
    [[nodiscard]] static std::string quote_value(
        const std::string& value,
        DatabaseDialect dialect);

    /**
     * @brief Format datetime for SQL
     * @param time_point Time point
     * @param dialect Database dialect
     * @return std::string Formatted datetime
     * 
     * @section details Implementation Details
     * - Formats datetime
     * - Thread-safe
     */
    [[nodiscard]] static std::string format_datetime(
        const std::chrono::system_clock::time_point& time_point,
        DatabaseDialect dialect);

    std::unordered_map<std::string, std::unique_ptr<TableDefinition>> tables_;  ///< Table definitions
    mutable std::mutex tables_mutex_;  ///< Tables mutex
};

/**
 * @brief SQL generator for different database dialects
 * 
 * Utility class providing static methods for generating SQL syntax
 * specific to different database platforms. Handles identifier quoting,
 * data type mapping, and platform-specific features.
 */
class SqlGenerator {
public:
    /**
     * @brief Quote identifier based on dialect
     * @param identifier Identifier to quote
     * @param dialect Database dialect
     * @return std::string Quoted identifier
     */
    [[nodiscard]] static std::string quote_identifier(
        const std::string& identifier,
        DatabaseDialect dialect);

    /**
     * @brief Format schema and table name
     * @param schema_name Schema name
     * @param table_name Table name
     * @param dialect Database dialect
     * @return std::string Formatted table name
     */
    [[nodiscard]] static std::string format_table_name(
        const std::string& schema_name,
        const std::string& table_name,
        DatabaseDialect dialect);

    /**
     * @brief Get auto-increment syntax
     * @param dialect Database dialect
     * @return std::string Auto-increment syntax
     */
    [[nodiscard]] static std::string get_auto_increment_syntax(DatabaseDialect dialect);

    /**
     * @brief Get current timestamp function
     * @param dialect Database dialect
     * @return std::string Current timestamp function
     */
    [[nodiscard]] static std::string get_current_timestamp_function(DatabaseDialect dialect);

    /**
     * @brief Escape string value for SQL
     * 
     * Properly escapes string values to prevent SQL injection.
     * Doubles single quotes and handles special characters based on dialect.
     * 
     * @param value String value to escape
     * @param dialect Database dialect
     * @return std::string Escaped string value with quotes
     */
    [[nodiscard]] static std::string quote_value(
        const std::string& value,
        DatabaseDialect dialect);

    /**
     * @brief Format date/time value for SQL
     * 
     * Thread-safe formatting of date/time values for SQL statements.
     * 
     * @param time_point Time point to format
     * @param dialect Database dialect
     * @return std::string Formatted date/time string
     */
    [[nodiscard]] static std::string format_datetime(
        const std::chrono::system_clock::time_point& time_point,
        DatabaseDialect dialect);
};

} // namespace omop::cdm