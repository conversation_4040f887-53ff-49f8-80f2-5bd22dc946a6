/**
 * @file omop_tables.h
 * @brief OMOP CDM table implementations and schema management
 * <AUTHOR> ETL Team
 * @date 2024
 * @version 1.0
 * 
 * @section overview Overview
 * This header provides concrete implementations of OMOP CDM tables with
 * field definitions, validation rules, and SQL generation capabilities.
 * 
 * @section design Design Principles
 * - Type Safety: Strong typing for all fields
 * - Validation: Comprehensive data validation
 * - Performance: Efficient field access
 * - Extensibility: Support for custom tables
 * - SQL Generation: Safe SQL statement creation
 * 
 * @section components Components
 * - OmopTable: Base class for all tables
 * - FieldVisitor: Field access interface
 * - Table Classes: Concrete table implementations
 * - OmopTableFactory: Table creation factory
 * - OmopSchema: Schema management
 * 
 * @section usage Usage
 * To use OMOP tables:
 * 1. Create table instance
 * 2. Set field values
 * 3. Validate data
 * 4. Generate SQL
 * 
 * @section example Example
 * @code
 * auto person = std::make_unique<Person>();
 * person->person_id = 1;
 * person->gender_concept_id = 8507;
 * if (person->validate()) {
 *     auto sql = person->to_insert_sql();
 * }
 * @endcode
 */

#pragma once

#include <string>
#include <optional>
#include <chrono>
#include <vector>
#include <unordered_map>
#include <any>
#include <memory>
#include <functional>

namespace omop::cdm {

/**
 * @brief OMOP CDM version
 * 
 * @section description Description
 * Current version of the OMOP Common Data Model specification.
 */
constexpr const char* CDM_VERSION = "5.4";

/**
 * @brief Validation result containing status and error messages
 * 
 * @section description Description
 * Structure for returning validation results with detailed error information.
 * 
 * @section fields Fields
 * - is_valid: Validation status
 * - errors: List of error messages
 */
struct ValidationResult {
    bool is_valid;  ///< Validation status
    std::vector<std::string> errors;  ///< Error messages
    
    /**
     * @brief Implicit conversion to bool for convenience
     * @return bool Validation status
     * 
     * @section details Implementation Details
     * - Returns is_valid
     * - Thread-safe
     */
    operator bool() const { return is_valid; }
};

/**
 * @brief Field visitor interface for efficient field access
 * 
 * @section description Description
 * Interface for visiting table fields without creating intermediate collections.
 * Improves performance for large tables.
 * 
 * @section features Features
 * - Field iteration
 * - Value access
 * - Memory efficiency
 */
class FieldVisitor {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~FieldVisitor() = default;
    
    /**
     * @brief Visit a field with its name and value
     * @param name Field name
     * @param value Field value
     * 
     * @section details Implementation Details
     * - Processes field
     * - Thread-safe
     */
    virtual void visit(const std::string& name, const std::any& value) = 0;
};

/**
 * @brief Base class for all OMOP CDM tables
 * 
 * @section description Description
 * Provides common functionality for OMOP table records including
 * field access, validation, and SQL generation. Implements visitor
 * pattern for efficient field iteration.
 * 
 * @section features Features
 * - Field management
 * - SQL generation
 * - Data validation
 * - Visitor pattern
 */
class OmopTable {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~OmopTable() = default;

    /**
     * @brief Get table name
     * @return std::string Table name
     * 
     * @section details Implementation Details
     * - Returns name
     * - Thread-safe
     */
    [[nodiscard]] virtual std::string table_name() const = 0;

    /**
     * @brief Get schema name
     * @return std::string Schema name
     * 
     * @section details Implementation Details
     * - Returns schema
     * - Thread-safe
     */
    [[nodiscard]] virtual std::string schema_name() const { return "cdm"; }

    /**
     * @brief Convert to INSERT SQL statement
     * 
     * @section description Description
     * Generates a properly escaped SQL INSERT statement for the record.
     * String values are escaped to prevent SQL injection.
     * 
     * @param escape_values Whether to escape string values (default: true)
     * @return std::string INSERT SQL
     * 
     * @section details Implementation Details
     * - Generates SQL
     * - Escapes values
     * - Thread-safe
     */
    [[nodiscard]] virtual std::string to_insert_sql(bool escape_values = true) const = 0;

    /**
     * @brief Get field names
     * @return std::vector<std::string> Field names
     * 
     * @section details Implementation Details
     * - Returns names
     * - Thread-safe
     */
    [[nodiscard]] virtual std::vector<std::string> field_names() const = 0;

    /**
     * @brief Get field values
     * 
     * @section description Description
     * Returns all field values as a vector. Note that this creates
     * intermediate collections and may be less efficient than using
     * the visitor pattern for large tables.
     * 
     * @deprecated Use visit_fields() for better performance
     * @return std::vector<std::any> Field values
     * 
     * @section details Implementation Details
     * - Returns values
     * - Thread-safe
     */
    [[nodiscard]] virtual std::vector<std::any> field_values() const = 0;

    /**
     * @brief Visit fields with a visitor for efficient access
     * 
     * @section description Description
     * Allows iteration over fields without creating intermediate vectors.
     * More efficient than field_names() + field_values() for large tables.
     * 
     * @param visitor Field visitor
     * 
     * @section details Implementation Details
     * - Visits fields
     * - Thread-safe
     */
    virtual void visit_fields(FieldVisitor& visitor) const = 0;

    /**
     * @brief Validate record
     * @return bool True if valid
     * 
     * @section details Implementation Details
     * - Validates data
     * - Thread-safe
     */
    [[nodiscard]] virtual bool validate() const = 0;

    /**
     * @brief Get validation errors
     * @return std::vector<std::string> Validation errors
     * 
     * @section details Implementation Details
     * - Returns errors
     * - Thread-safe
     */
    [[nodiscard]] virtual std::vector<std::string> validation_errors() const = 0;

    /**
     * @brief Validate record with detailed result
     * @return ValidationResult Validation result with errors
     * 
     * @section details Implementation Details
     * - Validates data
     * - Returns result
     * - Thread-safe
     */
    [[nodiscard]] virtual ValidationResult validate_detailed() const {
        ValidationResult result;
        result.is_valid = validate();
        if (!result.is_valid) {
            result.errors = validation_errors();
        }
        return result;
    }

protected:
    /**
     * @brief Helper to format datetime for SQL
     * @param time_point Time point to format
     * @return std::string Formatted datetime string
     * 
     * @section details Implementation Details
     * - Formats datetime
     * - Thread-safe
     */
    [[nodiscard]] static std::string format_datetime_sql(
        const std::chrono::system_clock::time_point& time_point);

    /**
     * @brief Helper to escape string value for SQL
     * @param value String value to escape
     * @return std::string Escaped string with quotes
     * 
     * @section details Implementation Details
     * - Escapes string
     * - Thread-safe
     */
    [[nodiscard]] static std::string escape_string_sql(const std::string& value);
};

/**
 * @brief Person table
 * 
 * @section description Description
 * Central table containing demographic information about each person.
 * Includes comprehensive validation for data integrity.
 * 
 * @section fields Fields
 * Required:
 * - person_id: Unique identifier
 * - gender_concept_id: Gender concept
 * - year_of_birth: Birth year
 * - race_concept_id: Race concept
 * - ethnicity_concept_id: Ethnicity concept
 * 
 * Optional:
 * - month_of_birth: Birth month
 * - day_of_birth: Birth day
 * - birth_datetime: Birth timestamp
 * - location_id: Location reference
 * - provider_id: Provider reference
 * - care_site_id: Care site reference
 * - Various source values and concepts
 */
class Person : public OmopTable {
public:
    // Required fields
    int64_t person_id{0};  ///< Unique identifier
    int32_t gender_concept_id{0};  ///< Gender concept
    int32_t year_of_birth{0};  ///< Birth year
    int32_t race_concept_id{0};  ///< Race concept
    int32_t ethnicity_concept_id{0};  ///< Ethnicity concept

    // Optional fields
    std::optional<int32_t> month_of_birth;  ///< Birth month
    std::optional<int32_t> day_of_birth;  ///< Birth day
    std::optional<std::chrono::system_clock::time_point> birth_datetime;  ///< Birth timestamp
    std::optional<int32_t> location_id;  ///< Location reference
    std::optional<int32_t> provider_id;  ///< Provider reference
    std::optional<int32_t> care_site_id;  ///< Care site reference
    std::optional<std::string> person_source_value;  ///< Source identifier
    std::optional<std::string> gender_source_value;  ///< Source gender
    std::optional<int32_t> gender_source_concept_id;  ///< Source gender concept
    std::optional<std::string> race_source_value;  ///< Source race
    std::optional<int32_t> race_source_concept_id;  ///< Source race concept
    std::optional<std::string> ethnicity_source_value;  ///< Source ethnicity
    std::optional<int32_t> ethnicity_source_concept_id;  ///< Source ethnicity concept

    /**
     * @brief Get table name
     * @return std::string Table name
     * 
     * @section details Implementation Details
     * - Returns name
     * - Thread-safe
     */
    [[nodiscard]] std::string table_name() const override { return "person"; }

    /**
     * @brief Convert to INSERT SQL
     * @param escape_values Whether to escape values
     * @return std::string SQL statement
     * 
     * @section details Implementation Details
     * - Generates SQL
     * - Thread-safe
     */
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;

    /**
     * @brief Get field names
     * @return std::vector<std::string> Field names
     * 
     * @section details Implementation Details
     * - Returns names
     * - Thread-safe
     */
    [[nodiscard]] std::vector<std::string> field_names() const override;

    /**
     * @brief Get field values
     * @return std::vector<std::any> Field values
     * 
     * @section details Implementation Details
     * - Returns values
     * - Thread-safe
     */
    [[nodiscard]] std::vector<std::any> field_values() const override;

    /**
     * @brief Visit fields
     * @param visitor Field visitor
     * 
     * @section details Implementation Details
     * - Visits fields
     * - Thread-safe
     */
    void visit_fields(FieldVisitor& visitor) const override;

    /**
     * @brief Validate record
     * @return bool True if valid
     * 
     * @section details Implementation Details
     * - Validates data
     * - Thread-safe
     */
    [[nodiscard]] bool validate() const override;

    /**
     * @brief Get validation errors
     * @return std::vector<std::string> Validation errors
     * 
     * @section details Implementation Details
     * - Returns errors
     * - Thread-safe
     */
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
    
private:
    /**
     * @brief Validate year of birth
     * @return bool True if valid
     * 
     * @section details Implementation Details
     * - Validates year
     * - Thread-safe
     */
    [[nodiscard]] bool validate_year_of_birth() const;
    
    /**
     * @brief Validate month of birth
     * @return bool True if valid
     * 
     * @section details Implementation Details
     * - Validates month
     * - Thread-safe
     */
    [[nodiscard]] bool validate_month_of_birth() const;
    
    /**
     * @brief Validate day of birth
     * @return bool True if valid
     * 
     * @section details Implementation Details
     * - Validates day
     * - Thread-safe
     */
    [[nodiscard]] bool validate_day_of_birth() const;
};

/**
 * @brief Observation Period table
 *
 * Defines periods of time during which a person is observed.
 * Validates that periods have logical date ranges.
 */
class ObservationPeriod : public OmopTable {
public:
    // Required fields
    int64_t observation_period_id{0};
    int64_t person_id{0};
    std::chrono::system_clock::time_point observation_period_start_date;
    std::chrono::system_clock::time_point observation_period_end_date;
    int32_t period_type_concept_id{0};

    [[nodiscard]] std::string table_name() const override { return "observation_period"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
    
private:
    /**
     * @brief Validate date range is logical
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_date_range() const;
};

/**
 * @brief Visit Occurrence table
 *
 * Records encounters between a person and healthcare provider.
 * Includes validation for visit dates and relationships.
 */
class VisitOccurrence : public OmopTable {
public:
    // Required fields
    int64_t visit_occurrence_id{0};
    int64_t person_id{0};
    int32_t visit_concept_id{0};
    std::chrono::system_clock::time_point visit_start_date;
    std::chrono::system_clock::time_point visit_end_date;
    int32_t visit_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> visit_start_datetime;
    std::optional<std::chrono::system_clock::time_point> visit_end_datetime;
    std::optional<int32_t> provider_id;
    std::optional<int32_t> care_site_id;
    std::optional<std::string> visit_source_value;
    std::optional<int32_t> visit_source_concept_id;
    std::optional<int32_t> admitted_from_concept_id;
    std::optional<std::string> admitted_from_source_value;
    std::optional<int32_t> discharged_to_concept_id;
    std::optional<std::string> discharged_to_source_value;
    std::optional<int64_t> preceding_visit_occurrence_id;

    [[nodiscard]] std::string table_name() const override { return "visit_occurrence"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Condition Occurrence table
 *
 * Records conditions (diseases, symptoms, findings) diagnosed or reported.
 */
class ConditionOccurrence : public OmopTable {
public:
    // Required fields
    int64_t condition_occurrence_id{0};
    int64_t person_id{0};
    int32_t condition_concept_id{0};
    std::chrono::system_clock::time_point condition_start_date;
    int32_t condition_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> condition_start_datetime;
    std::optional<std::chrono::system_clock::time_point> condition_end_date;
    std::optional<std::chrono::system_clock::time_point> condition_end_datetime;
    std::optional<int32_t> condition_status_concept_id;
    std::optional<std::string> stop_reason;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> condition_source_value;
    std::optional<int32_t> condition_source_concept_id;
    std::optional<std::string> condition_status_source_value;

    [[nodiscard]] std::string table_name() const override { return "condition_occurrence"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Drug Exposure table
 *
 * Records drug exposures including prescriptions, administrations, and dispensings.
 * Includes validation for drug quantities and date ranges.
 */
class DrugExposure : public OmopTable {
public:
    // Required fields
    int64_t drug_exposure_id{0};
    int64_t person_id{0};
    int32_t drug_concept_id{0};
    std::chrono::system_clock::time_point drug_exposure_start_date;
    std::chrono::system_clock::time_point drug_exposure_end_date;
    int32_t drug_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> drug_exposure_start_datetime;
    std::optional<std::chrono::system_clock::time_point> drug_exposure_end_datetime;
    std::optional<std::chrono::system_clock::time_point> verbatim_end_date;
    std::optional<std::string> stop_reason;
    std::optional<int32_t> refills;
    std::optional<double> quantity;
    std::optional<int32_t> days_supply;
    std::optional<std::string> sig;
    std::optional<int32_t> route_concept_id;
    std::optional<std::string> lot_number;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> drug_source_value;
    std::optional<int32_t> drug_source_concept_id;
    std::optional<std::string> route_source_value;
    std::optional<std::string> dose_unit_source_value;

    [[nodiscard]] std::string table_name() const override { return "drug_exposure"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
    
private:
    /**
     * @brief Validate drug quantity if provided
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_quantity() const;
    
    /**
     * @brief Validate days supply if provided
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_days_supply() const;
};

/**
 * @brief Procedure Occurrence table
 *
 * Records procedures performed on a person.
 */
class ProcedureOccurrence : public OmopTable {
public:
    // Required fields
    int64_t procedure_occurrence_id{0};
    int64_t person_id{0};
    int32_t procedure_concept_id{0};
    std::chrono::system_clock::time_point procedure_date;
    int32_t procedure_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> procedure_datetime;
    std::optional<std::chrono::system_clock::time_point> procedure_end_date;
    std::optional<std::chrono::system_clock::time_point> procedure_end_datetime;
    std::optional<int32_t> modifier_concept_id;
    std::optional<int32_t> quantity;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> procedure_source_value;
    std::optional<int32_t> procedure_source_concept_id;
    std::optional<std::string> modifier_source_value;

    [[nodiscard]] std::string table_name() const override { return "procedure_occurrence"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Measurement table
 *
 * Records measurements including laboratory tests, vital signs, and other clinical observations.
 * Includes validation for measurement values and ranges.
 */
class Measurement : public OmopTable {
public:
    // Required fields
    int64_t measurement_id{0};
    int64_t person_id{0};
    int32_t measurement_concept_id{0};
    std::chrono::system_clock::time_point measurement_date;
    int32_t measurement_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> measurement_datetime;
    std::optional<std::chrono::system_clock::time_point> measurement_time;
    std::optional<int32_t> operator_concept_id;
    std::optional<double> value_as_number;
    std::optional<int32_t> value_as_concept_id;
    std::optional<int32_t> unit_concept_id;
    std::optional<double> range_low;
    std::optional<double> range_high;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> measurement_source_value;
    std::optional<int32_t> measurement_source_concept_id;
    std::optional<std::string> unit_source_value;
    std::optional<int32_t> unit_source_concept_id;
    std::optional<std::string> value_source_value;
    std::optional<int64_t> measurement_event_id;
    std::optional<int32_t> meas_event_field_concept_id;

    [[nodiscard]] std::string table_name() const override { return "measurement"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
    
private:
    /**
     * @brief Validate measurement ranges if provided
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_ranges() const;
};

/**
 * @brief Observation table
 *
 * Records clinical facts about a person that are not measurements or procedures.
 */
class Observation : public OmopTable {
public:
    // Required fields
    int64_t observation_id{0};
    int64_t person_id{0};
    int32_t observation_concept_id{0};
    std::chrono::system_clock::time_point observation_date;
    int32_t observation_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> observation_datetime;
    std::optional<double> value_as_number;
    std::optional<std::string> value_as_string;
    std::optional<int32_t> value_as_concept_id;
    std::optional<int32_t> qualifier_concept_id;
    std::optional<int32_t> unit_concept_id;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> observation_source_value;
    std::optional<int32_t> observation_source_concept_id;
    std::optional<std::string> unit_source_value;
    std::optional<std::string> qualifier_source_value;
    std::optional<std::string> value_source_value;
    std::optional<int64_t> observation_event_id;
    std::optional<int32_t> obs_event_field_concept_id;

    [[nodiscard]] std::string table_name() const override { return "observation"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Death table
 *
 * Records death information for a person.
 */
class Death : public OmopTable {
public:
    // Required fields
    int64_t person_id{0};
    std::chrono::system_clock::time_point death_date;

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> death_datetime;
    std::optional<int32_t> death_type_concept_id;
    std::optional<int32_t> cause_concept_id;
    std::optional<std::string> cause_source_value;
    std::optional<int32_t> cause_source_concept_id;

    [[nodiscard]] std::string table_name() const override { return "death"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
    
private:
    /**
     * @brief Validate death date is not in the future
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_death_date() const;
};

/**
 * @brief Note table
 *
 * Records unstructured text notes about a person.
 */
class Note : public OmopTable {
public:
    // Required fields
    int64_t note_id{0};
    int64_t person_id{0};
    std::chrono::system_clock::time_point note_date;
    int32_t note_type_concept_id{0};
    int32_t note_class_concept_id{0};
    std::string note_title;
    std::string note_text;
    int32_t encoding_concept_id{0};
    int32_t language_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> note_datetime;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> note_source_value;
    std::optional<int64_t> note_event_id;
    std::optional<int32_t> note_event_field_concept_id;

    [[nodiscard]] std::string table_name() const override { return "note"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Factory for creating OMOP table instances
 * 
 * @section description Description
 * Factory class for creating instances of OMOP CDM tables.
 * Supports both standard and custom table implementations.
 * 
 * @section features Features
 * - Table creation
 * - Custom registration
 * - Table discovery
 */
class OmopTableFactory {
public:
    /**
     * @brief Create table instance
     * @param table_name Table name
     * @return std::unique_ptr<OmopTable> Table instance
     * 
     * @section details Implementation Details
     * - Creates instance
     * - Thread-safe
     */
    [[nodiscard]] static std::unique_ptr<OmopTable> create(const std::string& table_name);

    /**
     * @brief Get supported tables
     * @return std::vector<std::string> Table names
     * 
     * @section details Implementation Details
     * - Returns names
     * - Thread-safe
     */
    [[nodiscard]] static std::vector<std::string> get_supported_tables();

    /**
     * @brief Check table support
     * @param table_name Table name
     * @return bool True if supported
     * 
     * @section details Implementation Details
     * - Checks support
     * - Thread-safe
     */
    [[nodiscard]] static bool is_supported(const std::string& table_name);

    /**
     * @brief Register custom table
     * @param table_name Table name
     * @param creator Factory function
     * 
     * @section details Implementation Details
     * - Registers table
     * - Thread-safe
     */
    static void register_table(
        const std::string& table_name,
        std::function<std::unique_ptr<OmopTable>()> creator);

private:
    /**
     * @brief Get table creators
     * @return std::unordered_map<std::string, std::function<std::unique_ptr<OmopTable>()>>& Creators
     * 
     * @section details Implementation Details
     * - Returns creators
     * - Thread-safe
     */
    static std::unordered_map<std::string, std::function<std::unique_ptr<OmopTable>()>>& get_creators();
};

/**
 * @brief OMOP CDM table schema information
 * 
 * @section description Description
 * Provides schema information and management for OMOP CDM tables.
 * 
 * @section features Features
 * - Schema validation
 * - Table discovery
 * - Field management
 */
class OmopSchema {
public:
    /**
     * @brief Get table schema
     * @param table_name Table name
     * @param schema_name Schema name
     * @return std::string Schema information
     * 
     * @section details Implementation Details
     * - Returns schema
     * - Thread-safe
     */
    [[nodiscard]] static std::string get_table_schema(
        const std::string& table_name,
        const std::string& schema_name = "cdm");

    /**
     * @brief Get all table schemas
     * @param schema_name Schema name
     * @return std::vector<std::string> Schema information
     * 
     * @section details Implementation Details
     * - Returns schemas
     * - Thread-safe
     */
    [[nodiscard]] static std::vector<std::string> get_all_schemas(
        const std::string& schema_name = "cdm");

    /**
     * @brief Get table fields
     * @param table_name Table name
     * @param schema_name Schema name
     * @return std::vector<std::string> Field information
     * 
     * @section details Implementation Details
     * - Returns fields
     * - Thread-safe
     */
    [[nodiscard]] static std::vector<std::string> get_table_fields(
        const std::string& table_name,
        const std::string& schema_name = "cdm");

    /**
     * @brief Get table constraints
     * @param table_name Table name
     * @param schema_name Schema name
     * @return std::vector<std::string> Constraint information
     * 
     * @section details Implementation Details
     * - Returns constraints
     * - Thread-safe
     */
    [[nodiscard]] static std::vector<std::string> get_table_constraints(
        const std::string& table_name,
        const std::string& schema_name = "cdm");
};

} // namespace omop::cdm