/**
 * @file logging.h
 * @brief Logging utilities for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 * @version 1.0
 * 
 * @section overview Overview
 * This header file defines a comprehensive logging system for the OMOP ETL
 * pipeline. It provides structured logging capabilities with support for
 * multiple output formats, sinks, and specialized logging features for
 * ETL operations, performance monitoring, and audit trails.
 * 
 * @section design Design Principles
 * - Structured Logging: JSON and text formats
 * - Multiple Sinks: File, database, and custom outputs
 * - Performance: Efficient buffering and async writing
 * - Flexibility: Extensible formatters and sinks
 * - Context: Rich metadata and context tracking
 * 
 * @section components Components
 * - Logger: Main logging interface
 * - LogEntry: Structured log data
 * - ILogFormatter: Format customization
 * - ILogSink: Output customization
 * - PerformanceLogger: Performance metrics
 * - AuditLogger: Security and compliance
 * 
 * @section usage Usage
 * To use the logging system:
 * 1. Initialize with configuration
 * 2. Get logger instance
 * 3. Set context (job, component)
 * 4. Log messages with levels
 * 5. Use specialized loggers as needed
 * 
 * @section example Example
 * @code
 * auto logger = Logger::get("etl");
 * logger->set_job_id("job123");
 * logger->set_component("extract");
 * logger->info("Starting data extraction");
 * logger->log_metrics({{"records_processed", 1000}});
 * @endcode
 */

#pragma once

#include <string>
#include <memory>
#include <chrono>
#include <unordered_map>
#include <vector>
#include <functional>
#include <mutex>
#include <optional>
#include <any>
#include <sstream>
#include <spdlog/spdlog.h>
#include <spdlog/sinks/sink.h>
#include <spdlog/fmt/ostr.h>

// Forward declarations for external dependencies
namespace omop::extract {
    class IDatabaseConnection;
}

namespace omop::common {

/**
 * @brief Log level enumeration
 * 
 * @section description Description
 * Defines the severity levels for log messages, from most
 * detailed (Trace) to most critical (Critical).
 * 
 * @section levels Levels
 * - Trace: Detailed debugging
 * - Debug: General debugging
 * - Info: Normal operation
 * - Warning: Potential issues
 * - Error: Operation failures
 * - Critical: System failures
 */
enum class LogLevel {
    Trace,    ///< Most detailed debugging
    Debug,    ///< General debugging
    Info,     ///< Normal operation
    Warning,  ///< Potential issues
    Error,    ///< Operation failures
    Critical  ///< System failures
};

/**
 * @brief Structured log entry
 * 
 * @section description Description
 * Represents a single log entry with structured data for better
 * analysis and processing. Contains metadata about the logging
 * context and the actual message.
 * 
 * @section fields Fields
 * - timestamp: When the event occurred
 * - level: Log severity level
 * - logger_name: Source logger
 * - message: Log message
 * - thread_id: Thread identifier
 * - job_id: Associated job
 * - component: System component
 * - operation: Current operation
 * - context: Additional metadata
 * - error_code: Error identifier
 * - stack_trace: Error details
 */
struct LogEntry {
    std::chrono::system_clock::time_point timestamp; ///< Event time
    LogLevel level;                                  ///< Severity level
    std::string logger_name;                         ///< Logger name
    std::string message;                             ///< Log message
    std::string thread_id;                           ///< Thread ID
    std::string job_id;                              ///< Job ID
    std::string component;                           ///< Component name
    std::string operation;                           ///< Operation name
    std::unordered_map<std::string, std::any> context; ///< Context data
    std::optional<std::string> error_code;           ///< Error code
    std::optional<std::string> stack_trace;          ///< Stack trace
};

/**
 * @brief Log formatter interface
 * 
 * @section description Description
 * Defines the interface for customizing log message formatting.
 * Implementations can format log entries in different ways
 * (e.g., JSON, text) for different use cases.
 * 
 * @section features Features
 * - Custom formatting logic
 * - Multiple output formats
 * - Extensible design
 */
class ILogFormatter {
public:
    virtual ~ILogFormatter() = default;

    /**
     * @brief Format log entry
     * @param entry Log entry
     * @return std::string Formatted log message
     * 
     * @section details Implementation Details
     * - Converts LogEntry to string
     * - Applies formatting rules
     * - Returns formatted message
     */
    virtual std::string format(const LogEntry& entry) = 0;
};

/**
 * @brief JSON log formatter
 * 
 * @section description Description
 * Formats log entries as JSON for structured logging and
 * machine processing. Supports pretty printing for readability.
 * 
 * @section features Features
 * - JSON output format
 * - Pretty printing option
 * - Structured data support
 */
class JsonLogFormatter : public ILogFormatter {
public:
    /**
     * @brief Format log entry as JSON
     * @param entry Log entry
     * @return std::string JSON formatted log
     * 
     * @section details Implementation Details
     * - Converts LogEntry to JSON
     * - Includes all fields
     * - Applies pretty printing
     */
    std::string format(const LogEntry& entry) override;

    /**
     * @brief Set whether to pretty print JSON
     * @param pretty Whether to pretty print
     * 
     * @section details Implementation Details
     * - Enables/disables formatting
     * - Affects output readability
     */
    void set_pretty_print(bool pretty) { pretty_print_ = pretty; }

private:
    bool pretty_print_{false}; ///< Pretty print flag
};

/**
 * @brief Plain text log formatter
 * 
 * @section description Description
 * Formats log entries as human-readable text with customizable
 * patterns. Suitable for console output and log files.
 * 
 * @section features Features
 * - Customizable patterns
 * - Human-readable output
 * - Format string support
 */
class TextLogFormatter : public ILogFormatter {
public:
    /**
     * @brief Constructor
     * @param pattern Log pattern
     * 
     * @section details Implementation Details
     * - Sets format pattern
     * - Default: "[%Y-%m-%d %H:%M:%S.%e] [%l] [%n] %v"
     */
    explicit TextLogFormatter(const std::string& pattern =
        "[%Y-%m-%d %H:%M:%S.%e] [%l] [%n] %v");

    /**
     * @brief Format log entry as text
     * @param entry Log entry
     * @return std::string Text formatted log
     * 
     * @section details Implementation Details
     * - Applies pattern to entry
     * - Formats timestamp
     * - Includes context data
     */
    std::string format(const LogEntry& entry) override;

private:
    std::string pattern_; ///< Format pattern
};

/**
 * @brief Log sink interface
 * 
 * @section description Description
 * Defines the interface for customizing log output destinations.
 * Implementations can write logs to different targets (e.g.,
 * files, databases, network).
 * 
 * @section features Features
 * - Custom output handling
 * - Multiple destinations
 * - Buffering support
 */
class ILogSink {
public:
    virtual ~ILogSink() = default;

    /**
     * @brief Write log entry
     * @param entry Log entry
     * 
     * @section details Implementation Details
     * - Formats entry
     * - Writes to destination
     * - Handles errors
     */
    virtual void write(const LogEntry& entry) = 0;

    /**
     * @brief Flush buffered logs
     * 
     * @section details Implementation Details
     * - Ensures all logs written
     * - Clears buffers
     * - Handles errors
     */
    virtual void flush() = 0;

    /**
     * @brief Set formatter
     * @param formatter Log formatter
     * 
     * @section details Implementation Details
     * - Sets output format
     * - Takes ownership
     */
    void set_formatter(std::unique_ptr<ILogFormatter> formatter) {
        formatter_ = std::move(formatter);
    }

protected:
    std::unique_ptr<ILogFormatter> formatter_; ///< Log formatter
};

/**
 * @brief Database log sink
 * 
 * @section description Description
 * Writes log entries to a database table for centralized logging
 * and analysis. Supports buffering for performance.
 * 
 * @section features Features
 * - Database storage
 * - Buffered writing
 * - Table management
 */
class DatabaseLogSink : public ILogSink {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     * @param table_name Log table name
     * 
     * @section details Implementation Details
     * - Stores connection
     * - Sets table name
     * - Initializes buffer
     */
    DatabaseLogSink(std::shared_ptr<omop::extract::IDatabaseConnection> connection,
                    const std::string& table_name = "etl_logs");

    /**
     * @brief Write log entry
     * @param entry Log entry
     * 
     * @section details Implementation Details
     * - Buffers entry
     * - Flushes if full
     * - Handles errors
     */
    void write(const LogEntry& entry) override;

    /**
     * @brief Flush buffered logs
     * 
     * @section details Implementation Details
     * - Writes all entries
     * - Clears buffer
     * - Handles errors
     */
    void flush() override;

    /**
     * @brief Create log table if not exists
     * 
     * @section details Implementation Details
     * - Creates table
     * - Sets schema
     * - Handles errors
     */
    void create_table_if_not_exists();

private:
    std::shared_ptr<omop::extract::IDatabaseConnection> connection_; ///< Database connection
    std::string table_name_;                                        ///< Table name
    std::vector<LogEntry> buffer_;                                  ///< Log buffer
    std::mutex buffer_mutex_;                                       ///< Buffer mutex
    size_t buffer_size_{100};                                       ///< Buffer size

    /**
     * @brief Flush buffer to database
     * 
     * @section details Implementation Details
     * - Writes entries
     * - Clears buffer
     * - Handles errors
     */
    void flush_buffer();
};

/**
 * @brief ETL-specific logger
 * 
 * @section description Description
 * Provides logging functionality tailored for ETL operations with
 * support for job tracking, component identification, and metrics.
 * 
 * @section features Features
 * - Multiple log levels
 * - Context tracking
 * - Structured logging
 * - Performance metrics
 * - Audit logging
 * 
 * @section thread_safety Thread Safety
 * The logger is thread-safe and can be used from multiple threads.
 * All public methods are synchronized appropriately.
 */
class Logger {
    friend class LoggingConfig;
public:
    /**
     * @brief Get logger instance
     * @param name Logger name
     * @return std::shared_ptr<Logger> Logger instance
     * 
     * @section details Implementation Details
     * - Returns existing or creates new
     * - Thread-safe
     * - No exceptions thrown
     */
    static std::shared_ptr<Logger> get(const std::string& name);

    /**
     * @brief Constructor
     * @param name Logger name
     * 
     * @section details Implementation Details
     * - Sets logger name
     * - Initializes state
     * - No exceptions thrown
     */
    explicit Logger(const std::string& name);

    /**
     * @brief Set log level
     * @param level Log level
     * 
     * @section details Implementation Details
     * - Updates level
     * - Thread-safe
     * - No exceptions thrown
     */
    void set_level(LogLevel level);

    /**
     * @brief Add log sink
     * @param sink Log sink
     * 
     * @section details Implementation Details
     * - Adds sink
     * - Thread-safe
     * - No exceptions thrown
     */
    void add_sink(std::shared_ptr<ILogSink> sink);

    /**
     * @brief Clear all sinks
     * 
     * @section details Implementation Details
     * - Removes all sinks
     * - Thread-safe
     * - No exceptions thrown
     */
    void clear_sinks() { sinks_.clear(); }

    /**
     * @brief Set job context
     * @param job_id Job identifier
     * 
     * @section details Implementation Details
     * - Updates job ID
     * - Thread-safe
     * - No exceptions thrown
     */
    void set_job_id(const std::string& job_id) { job_id_ = job_id; }

    /**
     * @brief Set component context
     * @param component Component name
     * 
     * @section details Implementation Details
     * - Updates component
     * - Thread-safe
     * - No exceptions thrown
     */
    void set_component(const std::string& component) { component_ = component; }

    /**
     * @brief Log trace message
     * @tparam Args Template arguments
     * @param msg Message format
     * @param args Format arguments
     *
     * @section details Implementation Details
     * - Logs at Trace level
     * - Formats message
     * - Thread-safe
     */
    template<typename... Args>
    void trace(const std::string& msg, Args&&... args) {
        if constexpr (sizeof...(args) == 0) {
            log_structured(LogLevel::Trace, msg, {});
        } else {
            // For arguments, we'll use a simple string stream approach
            std::ostringstream oss;
            oss << msg;
            ((oss << " " << args), ...);
            log_structured(LogLevel::Trace, oss.str(), {});
        }
    }

    /**
     * @brief Log debug message
     * @tparam Args Template arguments
     * @param msg Message format
     * @param args Format arguments
     *
     * @section details Implementation Details
     * - Logs at Debug level
     * - Formats message
     * - Thread-safe
     */
    template<typename... Args>
    void debug(const std::string& msg, Args&&... args) {
        if constexpr (sizeof...(args) == 0) {
            log_structured(LogLevel::Debug, msg, {});
        } else {
            // For arguments, we'll use a simple string stream approach
            std::ostringstream oss;
            oss << msg;
            ((oss << " " << args), ...);
            log_structured(LogLevel::Debug, oss.str(), {});
        }
    }

    /**
     * @brief Log info message
     * @tparam Args Template arguments
     * @param msg Message format
     * @param args Format arguments
     *
     * @section details Implementation Details
     * - Logs at Info level
     * - Formats message
     * - Thread-safe
     */
    template<typename... Args>
    void info(const std::string& msg, Args&&... args) {
        if constexpr (sizeof...(args) == 0) {
            log_structured(LogLevel::Info, msg, {});
        } else {
            // For arguments, we'll use a simple string stream approach
            std::ostringstream oss;
            oss << msg;
            ((oss << " " << args), ...);
            log_structured(LogLevel::Info, oss.str(), {});
        }
    }

    /**
     * @brief Log warning message
     * @tparam Args Template arguments
     * @param msg Message format
     * @param args Format arguments
     *
     * @section details Implementation Details
     * - Logs at Warning level
     * - Formats message
     * - Thread-safe
     */
    template<typename... Args>
    void warn(const std::string& msg, Args&&... args) {
        if constexpr (sizeof...(args) == 0) {
            log_structured(LogLevel::Warning, msg, {});
        } else {
            // For arguments, we'll use a simple string stream approach
            std::ostringstream oss;
            oss << msg;
            ((oss << " " << args), ...);
            log_structured(LogLevel::Warning, oss.str(), {});
        }
    }

    /**
     * @brief Log error message
     * @tparam Args Template arguments
     * @param msg Message format
     * @param args Format arguments
     *
     * @section details Implementation Details
     * - Logs at Error level
     * - Formats message
     * - Thread-safe
     */
    template<typename... Args>
    void error(const std::string& msg, Args&&... args) {
        if constexpr (sizeof...(args) == 0) {
            log_structured(LogLevel::Error, msg, {});
        } else {
            // For arguments, we'll use a simple string stream approach
            std::ostringstream oss;
            oss << msg;
            ((oss << " " << args), ...);
            log_structured(LogLevel::Error, oss.str(), {});
        }
    }

    /**
     * @brief Log critical message
     * @tparam Args Template arguments
     * @param msg Message format
     * @param args Format arguments
     *
     * @section details Implementation Details
     * - Logs at Critical level
     * - Formats message
     * - Thread-safe
     */
    template<typename... Args>
    void critical(const std::string& msg, Args&&... args) {
        if constexpr (sizeof...(args) == 0) {
            log_structured(LogLevel::Critical, msg, {});
        } else {
            // For arguments, we'll use a simple string stream approach
            std::ostringstream oss;
            oss << msg;
            ((oss << " " << args), ...);
            log_structured(LogLevel::Critical, oss.str(), {});
        }
    }

    /**
     * @brief Log with structured context
     * @param level Log level
     * @param msg Message
     * @param context Additional context
     * 
     * @section details Implementation Details
     * - Logs with structured context
     * - Thread-safe
     */
    void log_structured(LogLevel level, const std::string& msg,
                       const std::unordered_map<std::string, std::any>& context);

    /**
     * @brief Log ETL operation
     * @param operation Operation name
     * @param status Operation status
     * @param details Operation details
     * 
     * @section details Implementation Details
     * - Logs ETL operation
     * - Thread-safe
     */
    void log_operation(const std::string& operation,
                      const std::string& status,
                      const std::unordered_map<std::string, std::any>& details = {});

    /**
     * @brief Log ETL metrics
     * @param metrics Metrics map
     * 
     * @section details Implementation Details
     * - Logs ETL metrics
     * - Thread-safe
     */
    void log_metrics(const std::unordered_map<std::string, double>& metrics);

    /**
     * @brief Log exception
     * @param e Exception
     * @param context Additional context
     *
     * @section details Implementation Details
     * - Logs exception
     * - Thread-safe
     */
    void log_exception(const std::exception& e,
                      const std::unordered_map<std::string, std::any>& context = {});



private:
    std::string name_;                           ///< Logger name
    LogLevel level_{LogLevel::Info};            ///< Log level
    LogLevel min_level_{LogLevel::Info};        ///< Minimum log level
    std::vector<std::shared_ptr<ILogSink>> sinks_; ///< Log sinks
    std::string job_id_;                         ///< Job context
    std::string component_;                      ///< Component context
    std::mutex mutex_;                           ///< Synchronization mutex
    std::shared_ptr<spdlog::logger> spdlog_logger_; ///< SPDLog logger instance

    // Static members for logger registry
    static std::unordered_map<std::string, std::shared_ptr<Logger>> loggers_; ///< Logger registry
    static std::mutex loggers_mutex_;           ///< Logger registry mutex

    /**
     * @brief Get thread ID as string
     * @return Thread ID string
     */
    std::string get_thread_id() const;

    /**
     * @brief Write log entry
     * @param entry Log entry
     *
     * @section details Implementation Details
     * - Formats entry
     * - Writes to sinks
     * - Thread-safe
     */
    void write_entry(const LogEntry& entry);
};

/**
 * @brief Performance logging
 * 
 * @section description Description
 * Specialized logger for tracking performance metrics and
 * resource usage in the ETL pipeline.
 * 
 * @section features Features
 * - Operation timing
 * - Throughput metrics
 * - Resource monitoring
 * - Scoped timing
 */
class PerformanceLogger {
public:
    /**
     * @brief Constructor
     * @param logger Base logger
     * 
     * @section details Implementation Details
     * - Stores logger
     * - Initializes state
     */
    explicit PerformanceLogger(std::shared_ptr<Logger> logger);

    /**
     * @brief Start timing operation
     * @param operation_id Operation identifier
     * 
     * @section details Implementation Details
     * - Records start time
     * - Stores operation ID
     */
    void start_timing(const std::string& operation_id);

    /**
     * @brief End timing operation
     * @param operation_id Operation identifier
     * @param record_count Optional record count
     * 
     * @section details Implementation Details
     * - Calculates duration
     * - Logs metrics
     * - Records throughput
     */
    void end_timing(const std::string& operation_id,
                   std::optional<size_t> record_count = std::nullopt);

    /**
     * @brief Log throughput
     * @param operation Operation name
     * @param records_per_second Throughput rate
     * 
     * @section details Implementation Details
     * - Logs rate
     * - Includes context
     */
    void log_throughput(const std::string& operation, double records_per_second);

    /**
     * @brief Log resource usage
     * @param cpu_percent CPU usage
     * @param memory_mb Memory usage
     * @param disk_io_mb Disk I/O
     * 
     * @section details Implementation Details
     * - Logs metrics
     * - Includes context
     */
    void log_resource_usage(double cpu_percent, double memory_mb, double disk_io_mb);

private:
    std::shared_ptr<Logger> logger_;             ///< Base logger
    std::unordered_map<std::string, std::chrono::system_clock::time_point> start_times_; ///< Operation start times
    std::unordered_map<std::string, std::chrono::steady_clock::time_point> timings_; ///< Operation timings
    std::mutex mutex_;                           ///< Synchronization mutex
    std::mutex timings_mutex_;                   ///< Timings mutex
};

/**
 * @brief Audit logging
 * 
 * @section description Description
 * Specialized logger for tracking security events, data access,
 * and configuration changes for compliance and auditing.
 * 
 * @section features Features
 * - Data access tracking
 * - Configuration changes
 * - Security events
 * - User activity
 */
class AuditLogger {
public:
    /**
     * @brief Constructor
     * @param logger Base logger
     * 
     * @section details Implementation Details
     * - Stores logger
     * - Initializes state
     */
    explicit AuditLogger(std::shared_ptr<Logger> logger);

    /**
     * @brief Log data access
     * @param table_name Table name
     * @param operation Operation type
     * @param record_count Record count
     * @param user User identifier
     * 
     * @section details Implementation Details
     * - Logs access
     * - Includes context
     * - Thread-safe
     */
    void log_data_access(const std::string& table_name,
                        const std::string& operation,
                        size_t record_count,
                        const std::string& user);

    /**
     * @brief Log configuration change
     * @param config_item Configuration item
     * @param old_value Previous value
     * @param new_value New value
     * @param user User identifier
     * 
     * @section details Implementation Details
     * - Logs change
     * - Includes context
     * - Thread-safe
     */
    void log_config_change(const std::string& config_item,
                          const std::string& old_value,
                          const std::string& new_value,
                          const std::string& user);

    /**
     * @brief Log security event
     * @param event_type Event type
     * @param details Event details
     * 
     * @section details Implementation Details
     * - Logs event
     * - Includes context
     * - Thread-safe
     */
    void log_security_event(const std::string& event_type,
                           const std::unordered_map<std::string, std::string>& details);

private:
    std::shared_ptr<Logger> logger_;             ///< Base logger
    std::mutex mutex_;                           ///< Synchronization mutex
};

/**
 * @brief Logging configuration
 * 
 * @section description Description
 * Manages global logging configuration and initialization.
 * Provides static methods for configuring the logging system.
 * 
 * @section features Features
 * - Global configuration
 * - Multiple sinks
 * - Level control
 * - Initialization
 */
class LoggingConfig {
public:
    /**
     * @brief Initialize logging
     * @param config_file Configuration file
     * 
     * @section details Implementation Details
     * - Loads configuration
     * - Sets up sinks
     * - Configures levels
     */
    static void initialize(const std::string& config_file);

    /**
     * @brief Initialize with defaults
     * 
     * @section details Implementation Details
     * - Sets default config
     * - Creates console sink
     * - Sets default level
     */
    static void initialize_default();

    /**
     * @brief Set global log level
     * @param level Log level
     * 
     * @section details Implementation Details
     * - Updates all loggers
     * - Thread-safe
     */
    static void set_global_level(LogLevel level);

    /**
     * @brief Add global sink
     * @param sink Log sink
     * 
     * @section details Implementation Details
     * - Adds to all loggers
     * - Thread-safe
     */
    static void add_global_sink(std::shared_ptr<ILogSink> sink);

    /**
     * @brief Flush all loggers
     * 
     * @section details Implementation Details
     * - Flushes all sinks
     * - Thread-safe
     */
    static void flush_all();

    /**
     * @brief Shutdown logging
     * 
     * @section details Implementation Details
     * - Flushes logs
     * - Cleans up
     * - Thread-safe
     */
    static void shutdown();

private:
    static std::mutex mutex_;                    ///< Synchronization mutex
    static std::vector<std::shared_ptr<ILogSink>> global_sinks_; ///< Global sinks
    static LogLevel global_level_;               ///< Global level
};

// Convenience macros
#define LOG_TRACE(logger, ...) (logger)->trace(__VA_ARGS__)
#define LOG_DEBUG(logger, ...) (logger)->debug(__VA_ARGS__)
#define LOG_INFO(logger, ...) (logger)->info(__VA_ARGS__)
#define LOG_WARN(logger, ...) (logger)->warn(__VA_ARGS__)
#define LOG_ERROR(logger, ...) (logger)->error(__VA_ARGS__)
#define LOG_CRITICAL(logger, ...) (logger)->critical(__VA_ARGS__)

} // namespace omop::common