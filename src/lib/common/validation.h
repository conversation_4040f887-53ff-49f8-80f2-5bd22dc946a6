/**
 * @file validation.h
 * @brief Data validation framework for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header file defines a comprehensive validation framework for ensuring
 * data quality and integrity throughout the ETL process. It provides a flexible
 * and extensible system for defining and applying validation rules to data
 * records.
 * 
 * @section design Design Principles
 * - Extensibility: Easy to add new validation rules
 * - Flexibility: Support for various data types
 * - Performance: Efficient validation execution
 * - Thread Safety: Safe concurrent usage
 * - Error Handling: Detailed validation results
 * 
 * @section components Components
 * - ValidationType: Enumeration of rule types
 * - ValidationResult: Structure for validation results
 * - ValidationRule: Base class for validation rules
 * - Rule Implementations: Various rule types
 * - ValidationEngine: Main validation orchestrator
 * 
 * @section usage Usage
 * To use the validation framework:
 * 1. Create validation rules
 * 2. Configure validation engine
 * 3. Apply validation to records
 * 4. Process validation results
 * 
 * @section example Example
 * @code
 * auto engine = std::make_unique<ValidationEngine>();
 * engine->addRule(std::make_unique<NotNullRule>("patient_id"));
 * engine->addRule(std::make_unique<DateRangeRule>("birth_date", min_date, max_date));
 * auto result = engine->validateRecord(record);
 * if (!result.passed()) {
 *     // Handle validation errors
 * }
 * @endcode
 */

#pragma once

#include "exceptions.h"
#include "logging.h"

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <unordered_map>
#include <variant>
#include <optional>
#include <chrono>
#include <regex>
#include <any>

namespace omop::common {

/**
 * @brief Enumeration of validation rule types
 * 
 * @section description Description
 * Defines the different types of validation rules that can be applied
 * to data fields.
 * 
 * @section values Values
 * - NOT_NULL: Field must not be null
 * - NOT_EMPTY: Field must not be empty
 * - UNIQUE: Field must be unique
 * - IN_LIST: Field must be in specified list
 * - REGEX: Field must match regex pattern
 * - DATE_RANGE: Date must be within range
 * - NUMERIC_RANGE: Number must be within range
 * - GREATER_THAN: Number must be greater than value
 * - LESS_THAN: Number must be less than value
 * - BETWEEN: Value must be between two values
 * - BEFORE: Date must be before another date
 * - AFTER: Date must be after another date
 * - LENGTH: String length validation
 * - CUSTOM: Custom validation function
 * - NOT_ZERO: Number must not be zero
 * - NOT_FUTURE_DATE: Date must not be in the future
 * - FOREIGN_KEY: Foreign key constraint
 * - COMPOSITE_KEY: Composite key validation
 * - CONDITIONAL: Conditional validation based on other fields
 */
enum class ValidationType {
    NOT_NULL,           ///< Field must not be null
    NOT_EMPTY,          ///< Field must not be empty
    UNIQUE,             ///< Field must be unique
    IN_LIST,            ///< Field must be in specified list
    REGEX,              ///< Field must match regex pattern
    DATE_RANGE,         ///< Date must be within range
    NUMERIC_RANGE,      ///< Number must be within range
    GREATER_THAN,       ///< Number must be greater than value
    LESS_THAN,          ///< Number must be less than value
    BETWEEN,            ///< Value must be between two values
    BEFORE,             ///< Date must be before another date
    AFTER,              ///< Date must be after another date
    LENGTH,             ///< String length validation
    CUSTOM,             ///< Custom validation function
    NOT_ZERO,           ///< Number must not be zero
    NOT_FUTURE_DATE,    ///< Date must not be in the future
    FOREIGN_KEY,        ///< Foreign key constraint
    COMPOSITE_KEY,      ///< Composite key validation
    CONDITIONAL         ///< Conditional validation based on other fields
};

/**
 * @brief Structure to hold validation results
 * 
 * @section description Description
 * Contains the results of a validation operation, including
 * overall status, errors, warnings, and statistics.
 * 
 * @section features Features
 * - Validation status tracking
 * - Error and warning collection
 * - Statistics gathering
 * - Result merging
 */
struct ValidationResult {
    bool is_valid;                          ///< Overall validation status
    std::vector<std::string> errors;        ///< List of validation errors
    std::vector<std::string> warnings;      ///< List of validation warnings
    size_t records_validated;               ///< Number of records validated
    size_t records_failed;                  ///< Number of records that failed validation

    /**
     * @brief Check if validation passed
     * @return true if validation passed, false otherwise
     * 
     * @section details Implementation Details
     * - Checks overall status
     * - Verifies no errors
     * - Thread-safe
     */
    bool passed() const { return is_valid && errors.empty(); }

    /**
     * @brief Merge another validation result into this one
     * @param other The validation result to merge
     * 
     * @section details Implementation Details
     * - Combines statistics
     * - Merges errors/warnings
     * - Updates status
     */
    void merge(const ValidationResult& other);
};

/**
 * @brief Base class for validation rules
 * 
 * @section description Description
 * Abstract base class that defines the interface for all
 * validation rules in the system.
 * 
 * @section features Features
 * - Field name tracking
 * - Validation type support
 * - Custom error messages
 * - Record context access
 */
class ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param type Type of validation
     * @param error_message Custom error message
     * 
     * @section details Implementation Details
     * - Initializes fields
     * - Sets validation type
     * - Stores error message
     */
    ValidationRule(const std::string& field_name,
                  ValidationType type,
                  const std::string& error_message = "");

    /**
     * @brief Virtual destructor
     */
    virtual ~ValidationRule() = default;

    /**
     * @brief Validate a value
     * @param value The value to validate
     * @param record The entire record (for context)
     * @return true if validation passes, false otherwise
     * 
     * @section details Implementation Details
     * - Pure virtual method
     * - Must be implemented
     * - Thread-safe
     */
    virtual bool validate(const std::any& value,
                         const std::unordered_map<std::string, std::any>& record) const = 0;

    /**
     * @brief Get the error message for validation failure
     * @return Error message string
     * 
     * @section details Implementation Details
     * - Returns custom message
     * - Falls back to default
     * - Thread-safe
     */
    virtual std::string getErrorMessage() const;

    /**
     * @brief Get the field name
     * @return Field name string
     * 
     * @section details Implementation Details
     * - Returns stored name
     * - Thread-safe
     */
    const std::string& getFieldName() const { return field_name_; }

    /**
     * @brief Get the validation type
     * @return Validation type enum
     * 
     * @section details Implementation Details
     * - Returns stored type
     * - Thread-safe
     */
    ValidationType getType() const { return type_; }

protected:
    std::string field_name_;      ///< Name of the field to validate
    ValidationType type_;         ///< Type of validation
    std::string error_message_;   ///< Custom error message
};

/**
 * @brief Not null validation rule
 * 
 * @section description Description
 * Validates that a field value is not null.
 * 
 * @section features Features
 * - Null checking
 * - Optional value support
 * - Thread-safe
 */
class NotNullRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * 
     * @section details Implementation Details
     * - Sets validation type
     * - Initializes base class
     */
    explicit NotNullRule(const std::string& field_name);

    /**
     * @brief Validate a value
     * @param value The value to validate
     * @param record The entire record (for context)
     * @return true if value is not null, false otherwise
     * 
     * @section details Implementation Details
     * - Checks for null
     * - Handles optional
     * - Thread-safe
     */
    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;
};

/**
 * @brief In list validation rule
 * 
 * @section description Description
 * Validates that a field value is in a specified list of allowed values.
 * 
 * @section features Features
 * - List membership checking
 * - Type-safe comparison
 * - Thread-safe
 * 
 * @tparam T Type of values in the list
 */
template<typename T>
class InListRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param allowed_values List of allowed values
     * 
     * @section details Implementation Details
     * - Stores allowed values
     * - Initializes base class
     */
    InListRule(const std::string& field_name, const std::vector<T>& allowed_values);

    /**
     * @brief Validate a value
     * @param value The value to validate
     * @param record The entire record (for context)
     * @return true if value is in list, false otherwise
     * 
     * @section details Implementation Details
     * - Checks list membership
     * - Type-safe comparison
     * - Thread-safe
     */
    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::vector<T> allowed_values_;  ///< List of allowed values
};

/**
 * @brief Date range validation rule
 * 
 * @section description Description
 * Validates that a date value falls within a specified range.
 * 
 * @section features Features
 * - Date range checking
 * - Optional bounds
 * - Thread-safe
 */
class DateRangeRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param min_date Minimum allowed date (optional)
     * @param max_date Maximum allowed date (optional)
     * 
     * @section details Implementation Details
     * - Stores date bounds
     * - Initializes base class
     */
    DateRangeRule(const std::string& field_name,
                  const std::optional<std::chrono::system_clock::time_point>& min_date,
                  const std::optional<std::chrono::system_clock::time_point>& max_date);

    /**
     * @brief Validate a value
     * @param value The value to validate
     * @param record The entire record (for context)
     * @return true if date is in range, false otherwise
     * 
     * @section details Implementation Details
     * - Checks date bounds
     * - Handles optional
     * - Thread-safe
     */
    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::optional<std::chrono::system_clock::time_point> min_date_;  ///< Minimum date
    std::optional<std::chrono::system_clock::time_point> max_date_;  ///< Maximum date
};

/**
 * @brief Numeric range validation rule
 * 
 * @section description Description
 * Validates that a numeric value falls within a specified range.
 * 
 * @section features Features
 * - Range checking
 * - Optional bounds
 * - Thread-safe
 * 
 * @tparam T Numeric type (int, float, etc.)
 */
template<typename T>
class NumericRangeRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param min_value Minimum allowed value (optional)
     * @param max_value Maximum allowed value (optional)
     * 
     * @section details Implementation Details
     * - Stores value bounds
     * - Initializes base class
     */
    NumericRangeRule(const std::string& field_name,
                     const std::optional<T>& min_value,
                     const std::optional<T>& max_value);

    /**
     * @brief Validate a value
     * @param value The value to validate
     * @param record The entire record (for context)
     * @return true if value is in range, false otherwise
     * 
     * @section details Implementation Details
     * - Checks value bounds
     * - Handles optional
     * - Thread-safe
     */
    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::optional<T> min_value_;  ///< Minimum value
    std::optional<T> max_value_;  ///< Maximum value
};

/**
 * @brief Regular expression validation rule
 * 
 * @section description Description
 * Validates that a string value matches a specified regular expression pattern.
 * 
 * @section features Features
 * - Pattern matching
 * - Regex support
 * - Thread-safe
 */
class RegexRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param pattern Regular expression pattern
     * 
     * @section details Implementation Details
     * - Compiles pattern
     * - Initializes base class
     */
    RegexRule(const std::string& field_name, const std::string& pattern);

    /**
     * @brief Validate a value
     * @param value The value to validate
     * @param record The entire record (for context)
     * @return true if value matches pattern, false otherwise
     * 
     * @section details Implementation Details
     * - Applies regex
     * - Handles errors
     * - Thread-safe
     */
    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::regex pattern_;  ///< Regular expression pattern
};

/**
 * @brief Custom validation rule with user-defined function
 * 
 * @section description Description
 * Allows definition of custom validation rules using user-provided
 * validation functions.
 * 
 * @section features Features
 * - Custom validation logic
 * - Function support
 * - Thread-safe
 */
class CustomRule : public ValidationRule {
public:
    using ValidationFunction = std::function<bool(const std::any&, const std::unordered_map<std::string, std::any>&)>;

    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param validator Custom validation function
     * @param error_message Custom error message
     * 
     * @section details Implementation Details
     * - Stores function
     * - Initializes base class
     */
    CustomRule(const std::string& field_name,
               ValidationFunction validator,
               const std::string& error_message = "");

    /**
     * @brief Validate a value
     * @param value The value to validate
     * @param record The entire record (for context)
     * @return true if validation passes, false otherwise
     * 
     * @section details Implementation Details
     * - Calls function
     * - Handles errors
     * - Thread-safe
     */
    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    ValidationFunction validator_;  ///< Custom validation function
};

/**
 * @brief Main validation engine
 * 
 * @section description Description
 * Orchestrates the validation process by managing rules and
 * applying them to records.
 * 
 * @section features Features
 * - Rule management
 * - Batch validation
 * - Result aggregation
 * - Thread-safe
 */
class ValidationEngine {
public:
    /**
     * @brief Constructor
     * @param logger Optional logger for validation events
     * 
     * @section details Implementation Details
     * - Initializes engine
     * - Sets up logger
     */
    explicit ValidationEngine(std::shared_ptr<Logger> logger = nullptr);

    /**
     * @brief Add a validation rule
     * @param rule Rule to add
     * 
     * @section details Implementation Details
     * - Takes ownership
     * - Thread-safe
     */
    void addRule(std::unique_ptr<ValidationRule> rule);

    /**
     * @brief Validate a single record
     * @param record Record to validate
     * @return Validation result
     * 
     * @section details Implementation Details
     * - Applies all rules
     * - Collects results
     * - Thread-safe
     */
    ValidationResult validateRecord(const std::unordered_map<std::string, std::any>& record);

    /**
     * @brief Validate a batch of records
     * @param records Records to validate
     * @param stop_on_error Whether to stop on first error
     * @return Validation result
     * 
     * @section details Implementation Details
     * - Processes batch
     * - Aggregates results
     * - Thread-safe
     */
    ValidationResult validateBatch(const std::vector<std::unordered_map<std::string, std::any>>& records,
                                  bool stop_on_error = false);

    /**
     * @brief Clear all validation rules
     * 
     * @section details Implementation Details
     * - Removes all rules
     * - Thread-safe
     */
    void clearRules();

    /**
     * @brief Get the number of rules
     * @return Number of rules
     * 
     * @section details Implementation Details
     * - Returns count
     * - Thread-safe
     */
    size_t getRuleCount() const { return rules_.size(); }

    /**
     * @brief Enable or disable a validation type
     * @param type Type to enable/disable
     * @param enabled Whether to enable
     * 
     * @section details Implementation Details
     * - Updates state
     * - Thread-safe
     */
    void setValidationTypeEnabled(ValidationType type, bool enabled);

    /**
     * @brief Create a validation rule from configuration
     * @param config Rule configuration
     * @return Created rule
     * 
     * @section details Implementation Details
     * - Parses config
     * - Creates rule
     * - Thread-safe
     */
    static std::unique_ptr<ValidationRule> createRule(const std::unordered_map<std::string, std::any>& config);

    /**
     * @brief Create a validation engine from YAML configuration
     * @param yaml_config YAML configuration string
     * @return Created engine
     * 
     * @section details Implementation Details
     * - Parses YAML
     * - Creates engine
     * - Thread-safe
     */
    static std::unique_ptr<ValidationEngine> createEngineFromYaml(const std::string& yaml_config);

    /**
     * @brief Validate an email address
     * @param email Email to validate
     * @return true if valid, false otherwise
     * 
     * @section details Implementation Details
     * - Checks format
     * - Validates domain
     * - Thread-safe
     */
    bool isValidEmail(const std::string& email);

    /**
     * @brief Validate a phone number
     * @param phone Phone to validate
     * @param country_code Country code
     * @return true if valid, false otherwise
     * 
     * @section details Implementation Details
     * - Checks format
     * - Validates country
     * - Thread-safe
     */
    bool isValidPhoneNumber(const std::string& phone, const std::string& country_code = "US");

    /**
     * @brief Validate a postal code
     * @param postal_code Code to validate
     * @param country_code Country code
     * @return true if valid, false otherwise
     * 
     * @section details Implementation Details
     * - Checks format
     * - Validates country
     * - Thread-safe
     */
    bool isValidPostalCode(const std::string& postal_code, const std::string& country_code = "US");

    /**
     * @brief Validate a UUID
     * @param uuid UUID to validate
     * @return true if valid, false otherwise
     * 
     * @section details Implementation Details
     * - Checks format
     * - Validates version
     * - Thread-safe
     */
    bool isValidUUID(const std::string& uuid);

    /**
     * @brief Validate a URL
     * @param url URL to validate
     * @return true if valid, false otherwise
     * 
     * @section details Implementation Details
     * - Checks format
     * - Validates protocol
     * - Thread-safe
     */
    bool isValidURL(const std::string& url);

private:
    std::vector<std::unique_ptr<ValidationRule>> rules_;  ///< Validation rules
    std::shared_ptr<Logger> logger_;                      ///< Logger for validation events
    std::unordered_map<ValidationType, bool> enabled_types_;  ///< Enabled validation types
};

/**
 * @brief Factory for creating validation rules and engines
 *
 * @section description Description
 * Provides factory methods for creating validation rules and engines from configuration.
 * Supports YAML-based configuration for flexible validation setup.
 */
class ValidationRuleFactory {
public:
    /**
     * @brief Create validation rule from configuration
     * @param config Rule configuration (must include 'type' field)
     * @return Unique pointer to validation rule
     *
     * @section details Implementation Details
     * - Creates rule based on type in config
     * - Configures rule parameters
     * - Returns nullptr for unknown types
     */
    static std::unique_ptr<ValidationRule> createRule(
        const std::unordered_map<std::string, std::any>& config);

    /**
     * @brief Create validation engine from YAML configuration
     * @param yaml_config YAML configuration string
     * @return Unique pointer to validation engine
     *
     * @section details Implementation Details
     * - Parses YAML configuration
     * - Creates validation rules
     * - Configures validation engine
     */
    static std::unique_ptr<ValidationEngine> createEngineFromYaml(const std::string& yaml_config);
};

} // namespace omop::common
