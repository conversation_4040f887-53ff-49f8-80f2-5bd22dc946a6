/**
 * @file logging.cpp
 * @brief Implementation of logging framework for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 */

#include "logging.h"
#include <nlohmann/json.hpp>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <thread>
#include <sstream>
#include <iomanip>
#include <chrono>

namespace omop::common {

// Static member definitions
std::unordered_map<std::string, std::shared_ptr<Logger>> Logger::loggers_;
std::mutex Logger::loggers_mutex_;

// Helper function to convert LogLevel to spdlog level
static spdlog::level::level_enum to_spdlog_level(LogLevel level) {
    switch (level) {
        case LogLevel::Trace: return spdlog::level::trace;
        case LogLevel::Debug: return spdlog::level::debug;
        case LogLevel::Info: return spdlog::level::info;
        case LogLevel::Warning: return spdlog::level::warn;
        case LogLevel::Error: return spdlog::level::err;
        case LogLevel::Critical: return spdlog::level::critical;
        default: return spdlog::level::info;
    }
}

// Helper function to convert LogLevel to string
static std::string log_level_to_string(LogLevel level) {
    switch (level) {
        case LogLevel::Trace: return "TRACE";
        case LogLevel::Debug: return "DEBUG";
        case LogLevel::Info: return "INFO";
        case LogLevel::Warning: return "WARN";
        case LogLevel::Error: return "ERROR";
        case LogLevel::Critical: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

// JsonLogFormatter implementation
std::string JsonLogFormatter::format(const LogEntry& entry) {
    nlohmann::json j;

    // Format timestamp as ISO 8601
    auto time_t = std::chrono::system_clock::to_time_t(entry.timestamp);
    std::stringstream ss;
    ss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%S");

    // Add milliseconds
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        entry.timestamp.time_since_epoch()) % 1000;
    ss << "." << std::setfill('0') << std::setw(3) << ms.count() << "Z";

    j["timestamp"] = ss.str();
    j["level"] = log_level_to_string(entry.level);
    j["logger"] = entry.logger_name;
    j["message"] = entry.message;
    j["thread_id"] = entry.thread_id;

    if (!entry.job_id.empty()) {
        j["job_id"] = entry.job_id;
    }

    if (!entry.component.empty()) {
        j["component"] = entry.component;
    }

    if (!entry.operation.empty()) {
        j["operation"] = entry.operation;
    }

    if (entry.error_code.has_value()) {
        j["error_code"] = entry.error_code.value();
    }

    if (entry.stack_trace.has_value()) {
        j["stack_trace"] = entry.stack_trace.value();
    }

    // Add context fields
    if (!entry.context.empty()) {
        nlohmann::json context_json;
        for (const auto& [key, value] : entry.context) {
            // Handle different types in std::any
            if (value.type() == typeid(int)) {
                context_json[key] = std::any_cast<int>(value);
            } else if (value.type() == typeid(double)) {
                context_json[key] = std::any_cast<double>(value);
            } else if (value.type() == typeid(std::string)) {
                context_json[key] = std::any_cast<std::string>(value);
            } else if (value.type() == typeid(bool)) {
                context_json[key] = std::any_cast<bool>(value);
            } else if (value.type() == typeid(size_t)) {
                context_json[key] = std::any_cast<size_t>(value);
            } else {
                // Try to convert to string
                try {
                    context_json[key] = std::any_cast<std::string>(value);
                } catch (...) {
                    context_json[key] = "<unsupported type>";
                }
            }
        }
        j["context"] = context_json;
    }

    if (pretty_print_) {
        return j.dump(4);
    } else {
        return j.dump();
    }
}

// TextLogFormatter implementation
TextLogFormatter::TextLogFormatter(const std::string& pattern)
    : pattern_(pattern) {}

std::string TextLogFormatter::format(const LogEntry& entry) {
    std::string result = pattern_;

    // Format timestamp
    auto time_t = std::chrono::system_clock::to_time_t(entry.timestamp);
    std::stringstream timestamp_ss;
    timestamp_ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");

    // Add milliseconds
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        entry.timestamp.time_since_epoch()) % 1000;
    timestamp_ss << "." << std::setfill('0') << std::setw(3) << ms.count();

    // Replace placeholders
    size_t pos = 0;
    while ((pos = result.find("%Y-%m-%d %H:%M:%S.%e", pos)) != std::string::npos) {
        result.replace(pos, 21, timestamp_ss.str());
        pos += timestamp_ss.str().length();
    }

    // Replace level
    pos = 0;
    while ((pos = result.find("%l", pos)) != std::string::npos) {
        std::string level_str = log_level_to_string(entry.level);
        result.replace(pos, 2, level_str);
        pos += level_str.length();
    }

    // Replace logger name
    pos = 0;
    while ((pos = result.find("%n", pos)) != std::string::npos) {
        result.replace(pos, 2, entry.logger_name);
        pos += entry.logger_name.length();
    }

    // Replace message
    pos = 0;
    while ((pos = result.find("%v", pos)) != std::string::npos) {
        std::string full_message = entry.message;

        // Append job_id and component if present
        if (!entry.job_id.empty() || !entry.component.empty()) {
            full_message += " [";
            if (!entry.job_id.empty()) {
                full_message += "job:" + entry.job_id;
                if (!entry.component.empty()) {
                    full_message += ", ";
                }
            }
            if (!entry.component.empty()) {
                full_message += "component:" + entry.component;
            }
            full_message += "]";
        }

        result.replace(pos, 2, full_message);
        pos += full_message.length();
    }

    return result;
}

// Logger implementation
std::shared_ptr<Logger> Logger::get(const std::string& name) {
    std::lock_guard<std::mutex> lock(loggers_mutex_);

    auto it = loggers_.find(name);
    if (it != loggers_.end()) {
        return it->second;
    }

    auto logger = std::make_shared<Logger>(name);
    loggers_[name] = logger;
    return logger;
}

Logger::Logger(const std::string& name)
    : name_(name) {
    // Create spdlog logger with console sink by default
    auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
    spdlog_logger_ = std::make_shared<spdlog::logger>(name, console_sink);
    spdlog_logger_->set_level(spdlog::level::info);
}

void Logger::set_level(LogLevel level) {
    min_level_ = level;
    if (spdlog_logger_) {
        spdlog_logger_->set_level(to_spdlog_level(level));
    }
}

void Logger::add_sink(std::shared_ptr<ILogSink> sink) {
    sinks_.push_back(sink);
}

void Logger::log_structured(LogLevel level, const std::string& msg,
                           const std::unordered_map<std::string, std::any>& context) {
    if (level < min_level_) return;

    LogEntry entry{
        .timestamp = std::chrono::system_clock::now(),
        .level = level,
        .logger_name = name_,
        .message = msg,
        .thread_id = get_thread_id(),
        .job_id = job_id_,
        .component = component_,
        .operation = "",
        .context = context,
        .error_code = std::nullopt,
        .stack_trace = std::nullopt
    };

    write_entry(entry);
}

void Logger::log_operation(const std::string& operation,
                          const std::string& status,
                          const std::unordered_map<std::string, std::any>& details) {
    std::unordered_map<std::string, std::any> context = details;
    context["operation_status"] = status;

    LogEntry entry{
        .timestamp = std::chrono::system_clock::now(),
        .level = LogLevel::Info,
        .logger_name = name_,
        .message = std::format("Operation '{}' completed with status: {}", operation, status),
        .thread_id = get_thread_id(),
        .job_id = job_id_,
        .component = component_,
        .operation = operation,
        .context = context,
        .error_code = std::nullopt,
        .stack_trace = std::nullopt
    };

    write_entry(entry);
}

void Logger::log_metrics(const std::unordered_map<std::string, double>& metrics) {
    std::unordered_map<std::string, std::any> context;
    for (const auto& [key, value] : metrics) {
        context[key] = value;
    }

    LogEntry entry{
        .timestamp = std::chrono::system_clock::now(),
        .level = LogLevel::Info,
        .logger_name = name_,
        .message = "Metrics update",
        .thread_id = get_thread_id(),
        .job_id = job_id_,
        .component = component_,
        .operation = "metrics",
        .context = context,
        .error_code = std::nullopt,
        .stack_trace = std::nullopt
    };

    write_entry(entry);
}

void Logger::log_exception(const std::exception& e,
                          const std::unordered_map<std::string, std::any>& context) {
    LogEntry entry{
        .timestamp = std::chrono::system_clock::now(),
        .level = LogLevel::Error,
        .logger_name = name_,
        .message = std::format("Exception caught: {}", e.what()),
        .thread_id = get_thread_id(),
        .job_id = job_id_,
        .component = component_,
        .operation = "",
        .context = context,
        .error_code = std::nullopt,
        .stack_trace = std::string(e.what())  // In production, use proper stack trace
    };

    write_entry(entry);
}

void Logger::write_entry(const LogEntry& entry) {
    // Write to custom sinks
    for (auto& sink : sinks_) {
        sink->write(entry);
    }

    // Also write to spdlog
    if (spdlog_logger_) {
        switch (entry.level) {
            case LogLevel::Trace:
                spdlog_logger_->trace(entry.message);
                break;
            case LogLevel::Debug:
                spdlog_logger_->debug(entry.message);
                break;
            case LogLevel::Info:
                spdlog_logger_->info(entry.message);
                break;
            case LogLevel::Warning:
                spdlog_logger_->warn(entry.message);
                break;
            case LogLevel::Error:
                spdlog_logger_->error(entry.message);
                break;
            case LogLevel::Critical:
                spdlog_logger_->critical(entry.message);
                break;
        }
    }
}

std::string Logger::get_thread_id() const {
    std::stringstream ss;
    ss << std::this_thread::get_id();
    return ss.str();
}

// PerformanceLogger implementation
void PerformanceLogger::start_timing(const std::string& operation_id) {
    std::lock_guard<std::mutex> lock(timings_mutex_);
    timings_[operation_id] = std::chrono::steady_clock::now();
}

void PerformanceLogger::end_timing(const std::string& operation_id,
                                  std::optional<size_t> record_count) {
    std::lock_guard<std::mutex> lock(timings_mutex_);

    auto it = timings_.find(operation_id);
    if (it == timings_.end()) {
        logger_->warn("No start timing found for operation: {}", operation_id);
        return;
    }

    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        end_time - it->second).count();

    std::unordered_map<std::string, std::any> metrics;
    metrics["duration_ms"] = static_cast<double>(duration);
    metrics["operation_id"] = operation_id;

    if (record_count.has_value()) {
        metrics["record_count"] = record_count.value();
        if (duration > 0) {
            double records_per_second = (record_count.value() * 1000.0) / duration;
            metrics["records_per_second"] = records_per_second;
        }
    }

    logger_->log_operation(operation_id, "completed", metrics);
    timings_.erase(it);
}

void PerformanceLogger::log_throughput(const std::string& operation,
                                      double records_per_second) {
    std::unordered_map<std::string, std::any> metrics;
    metrics["operation"] = operation;
    metrics["throughput_rps"] = records_per_second;

    logger_->log_metrics({{"throughput_" + operation, records_per_second}});
}

void PerformanceLogger::log_resource_usage(double cpu_percent,
                                          double memory_mb,
                                          double disk_io_mb) {
    std::unordered_map<std::string, double> metrics;
    metrics["cpu_percent"] = cpu_percent;
    metrics["memory_mb"] = memory_mb;
    metrics["disk_io_mb"] = disk_io_mb;

    logger_->log_metrics(metrics);
}

// AuditLogger implementation
void AuditLogger::log_data_access(const std::string& table_name,
                                 const std::string& operation,
                                 size_t record_count,
                                 const std::string& user) {
    std::unordered_map<std::string, std::any> context;
    context["table_name"] = table_name;
    context["operation"] = operation;
    context["record_count"] = record_count;
    context["user"] = user;
    context["audit_type"] = std::string("data_access");

    logger_->log_structured(LogLevel::Info,
        std::format("Data access: {} {} on {} ({} records)",
                   user, operation, table_name, record_count),
        context);
}

void AuditLogger::log_config_change(const std::string& config_item,
                                   const std::string& old_value,
                                   const std::string& new_value,
                                   const std::string& user) {
    std::unordered_map<std::string, std::any> context;
    context["config_item"] = config_item;
    context["old_value"] = old_value;
    context["new_value"] = new_value;
    context["user"] = user;
    context["audit_type"] = std::string("config_change");

    logger_->log_structured(LogLevel::Info,
        std::format("Configuration change: {} changed '{}' from '{}' to '{}'",
                   user, config_item, old_value, new_value),
        context);
}

void AuditLogger::log_security_event(const std::string& event_type,
                                    const std::unordered_map<std::string, std::string>& details) {
    std::unordered_map<std::string, std::any> context;
    for (const auto& [key, value] : details) {
        context[key] = value;
    }
    context["audit_type"] = std::string("security");
    context["event_type"] = event_type;

    logger_->log_structured(LogLevel::Warning,
        std::format("Security event: {}", event_type),
        context);
}

// LoggingConfig implementation
void LoggingConfig::initialize(const std::string& config_file) {
    // TODO: Load logging configuration from file
    // For now, use default configuration
    initialize_default();
}

void LoggingConfig::initialize_default() {
    // Set default pattern for spdlog
    spdlog::set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] [%n] %v");

    // Set default level
    spdlog::set_level(spdlog::level::info);

    // Create default file sink
    auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
        "logs/omop_etl.log", 1048576 * 10, 5);

    // Set default logger
    auto default_logger = std::make_shared<spdlog::logger>("default", file_sink);
    spdlog::set_default_logger(default_logger);
}

void LoggingConfig::set_global_level(LogLevel level) {
    spdlog::set_level(to_spdlog_level(level));
}

void LoggingConfig::add_global_sink(std::shared_ptr<ILogSink> sink) {
    // Add sink to all existing loggers
    std::lock_guard<std::mutex> lock(Logger::loggers_mutex_);
    for (auto& [name, logger] : Logger::loggers_) {
        logger->add_sink(sink);
    }
}

void LoggingConfig::flush_all() {
    spdlog::apply_all([](std::shared_ptr<spdlog::logger> l) { l->flush(); });
}

void LoggingConfig::shutdown() {
    flush_all();
    spdlog::shutdown();
}

} // namespace omop::common