/**
 * @file exceptions.h
 * @brief Exception hierarchy for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 * @version 1.0
 * 
 * @section overview Overview
 * This header file defines the exception hierarchy used throughout the OMOP ETL
 * pipeline. It provides specialized exception classes for different components
 * and operations, with enhanced error information including source location
 * tracking and formatted error messages.
 * 
 * @section design Design Principles
 * - Hierarchical: All exceptions inherit from OmopException
 * - Informative: Rich error context and details
 * - Location-aware: Source code location tracking
 * - Thread-safe: Safe concurrent usage
 * - Extensible: Easy to add new exception types
 * 
 * @section components Components
 * - OmopException: Base exception class
 * - ConfigurationException: Configuration errors
 * - ExtractionException: Data extraction errors
 * - TransformationException: Data transformation errors
 * - LoadException: Data loading errors
 * - DatabaseException: Database operation errors
 * - ValidationException: Data validation errors
 * - VocabularyException: Vocabulary mapping errors
 * 
 * @section usage Usage
 * To use the exceptions:
 * 1. Include this header
 * 2. Use appropriate exception type
 * 3. Throw with context information
 * 
 * @section example Example
 * @code
 * if (!config.is_valid()) {
 *     throw ConfigurationException("Invalid configuration", "database.url");
 * }
 * @endcode
 */

#ifndef OMOP_COMMON_EXCEPTIONS_H
#define OMOP_COMMON_EXCEPTIONS_H

#include <exception>
#include <string>
#include <string_view>
#include <source_location>
#include <format>

namespace omop::common {

/**
 * @brief Base exception class for the OMOP ETL pipeline
 * 
 * @section description Description
 * This class serves as the root of the exception hierarchy for the entire
 * OMOP ETL pipeline project. It provides enhanced error information including
 * source location tracking and formatted error messages.
 * 
 * @section features Features
 * - Source location tracking
 * - Formatted error messages
 * - Thread-safe implementation
 * - Standard exception compatibility
 */
class OmopException : public std::exception {
public:
    /**
     * @brief Construct a new OmopException
     * @param message Error message describing the exception
     * @param location Source location where the exception occurred
     * 
     * @section details Implementation Details
     * - Stores original message
     * - Formats with location
     * - Captures source location
     */
    explicit OmopException(std::string_view message,
                           const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the exception message
     * @return const char* Pointer to the error message
     * 
     * @section details Implementation Details
     * - Returns formatted message
     * - Includes location info
     * - Thread-safe
     */
    [[nodiscard]] const char* what() const noexcept override;

    /**
     * @brief Get the raw error message without location information
     * @return std::string_view The original error message
     * 
     * @section details Implementation Details
     * - Returns original message
     * - No formatting
     * - Thread-safe
     */
    [[nodiscard]] std::string_view message() const noexcept;

    /**
     * @brief Get the source location where the exception occurred
     * @return const std::source_location& Reference to the source location
     * 
     * @section details Implementation Details
     * - Returns location info
     * - Includes file/line
     * - Thread-safe
     */
    [[nodiscard]] const std::source_location& location() const noexcept;

protected:
    std::string message_;           ///< Original error message
    std::string formatted_message_; ///< Formatted message with location
    std::source_location location_; ///< Source location of the exception
};

/**
 * @brief Exception thrown during configuration operations
 * 
 * @section description Description
 * Thrown when errors occur during configuration loading, parsing,
 * or validation operations.
 * 
 * @section features Features
 * - Configuration key tracking
 * - Detailed error context
 * - Source location info
 */
class ConfigurationException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a configuration exception with a specific config key
     * @param message Error message
     * @param config_key The configuration key that caused the error
     * @param location Source location
     * 
     * @section details Implementation Details
     * - Stores config key
     * - Formats message
     * - Captures location
     */
    ConfigurationException(std::string_view message,
                           std::string_view config_key,
                           const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the configuration key that caused the error
     * @return std::string_view The configuration key
     * 
     * @section details Implementation Details
     * - Returns stored key
     * - Thread-safe
     */
    [[nodiscard]] std::string_view config_key() const noexcept;

private:
    std::string config_key_; ///< Configuration key that caused the error
};

/**
 * @brief Exception thrown during data extraction operations
 * 
 * @section description Description
 * Thrown when errors occur during data extraction from source
 * systems or files.
 * 
 * @section features Features
 * - Source name tracking
 * - Extraction context
 * - Error details
 */
class ExtractionException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct an extraction exception with source details
     * @param message Error message
     * @param source_name Name of the data source
     * @param location Source location
     * 
     * @section details Implementation Details
     * - Stores source name
     * - Formats message
     * - Captures location
     */
    ExtractionException(std::string_view message,
                        std::string_view source_name,
                        const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the name of the data source
     * @return std::string_view The source name
     * 
     * @section details Implementation Details
     * - Returns stored name
     * - Thread-safe
     */
    [[nodiscard]] std::string_view source_name() const noexcept;

private:
    std::string source_name_; ///< Name of the data source
};

/**
 * @brief Exception thrown during data transformation operations
 * 
 * @section description Description
 * Thrown when errors occur during data transformation or mapping
 * operations.
 * 
 * @section features Features
 * - Field name tracking
 * - Transformation type info
 * - Error context
 */
class TransformationException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a transformation exception with field details
     * @param message Error message
     * @param field_name Name of the field being transformed
     * @param transformation_type Type of transformation attempted
     * @param location Source location
     * 
     * @section details Implementation Details
     * - Stores field info
     * - Formats message
     * - Captures location
     */
    TransformationException(std::string_view message,
                            std::string_view field_name,
                            std::string_view transformation_type,
                            const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the field name
     * @return std::string_view The field name
     * 
     * @section details Implementation Details
     * - Returns stored name
     * - Thread-safe
     */
    [[nodiscard]] std::string_view field_name() const noexcept;

    /**
     * @brief Get the transformation type
     * @return std::string_view The transformation type
     * 
     * @section details Implementation Details
     * - Returns stored type
     * - Thread-safe
     */
    [[nodiscard]] std::string_view transformation_type() const noexcept;

private:
    std::string field_name_;           ///< Name of the field being transformed
    std::string transformation_type_;  ///< Type of transformation attempted
};

/**
 * @brief Exception thrown during data loading operations
 * 
 * @section description Description
 * Thrown when errors occur during data loading into target
 * systems or files.
 * 
 * @section features Features
 * - Target table tracking
 * - Load context
 * - Error details
 */
class LoadException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a load exception with target details
     * @param message Error message
     * @param target_table Target table name
     * @param location Source location
     * 
     * @section details Implementation Details
     * - Stores table name
     * - Formats message
     * - Captures location
     */
    LoadException(std::string_view message,
                  std::string_view target_table,
                  const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the target table name
     * @return std::string_view The target table name
     * 
     * @section details Implementation Details
     * - Returns stored name
     * - Thread-safe
     */
    [[nodiscard]] std::string_view target_table() const noexcept;

private:
    std::string target_table_; ///< Target table name
};

/**
 * @brief Exception thrown during database operations
 * 
 * @section description Description
 * Thrown when errors occur during database operations including
 * connection, query execution, and transaction management.
 * 
 * @section features Features
 * - Database type tracking
 * - Error code info
 * - Connection context
 */
class DatabaseException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a database exception with connection details
     * @param message Error message
     * @param database_type Type of database (PostgreSQL, MySQL, etc.)
     * @param error_code Database-specific error code
     * @param location Source location
     * 
     * @section details Implementation Details
     * - Stores database info
     * - Formats message
     * - Captures location
     */
    DatabaseException(std::string_view message,
                      std::string_view database_type,
                      int error_code,
                      const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the database type
     * @return std::string_view The database type
     * 
     * @section details Implementation Details
     * - Returns stored type
     * - Thread-safe
     */
    [[nodiscard]] std::string_view database_type() const noexcept;

    /**
     * @brief Get the error code
     * @return int The database-specific error code
     * 
     * @section details Implementation Details
     * - Returns stored code
     * - Thread-safe
     */
    [[nodiscard]] int error_code() const noexcept;

private:
    std::string database_type_; ///< Type of database
    int error_code_;           ///< Database-specific error code
};

/**
 * @brief Exception thrown during validation operations
 * 
 * @section description Description
 * Thrown when data validation rules fail or validation
 * operations encounter errors.
 * 
 * @section features Features
 * - Rule name tracking
 * - Field value info
 * - Validation context
 */
class ValidationException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a validation exception with rule details
     * @param message Error message
     * @param rule_name Name of the validation rule
     * @param field_value The value that failed validation
     * @param location Source location
     * 
     * @section details Implementation Details
     * - Stores rule info
     * - Formats message
     * - Captures location
     */
    ValidationException(std::string_view message,
                        std::string_view rule_name,
                        std::string_view field_value,
                        const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the validation rule name
     * @return std::string_view The rule name
     * 
     * @section details Implementation Details
     * - Returns stored name
     * - Thread-safe
     */
    [[nodiscard]] std::string_view rule_name() const noexcept;

    /**
     * @brief Get the field value that failed validation
     * @return std::string_view The field value
     * 
     * @section details Implementation Details
     * - Returns stored value
     * - Thread-safe
     */
    [[nodiscard]] std::string_view field_value() const noexcept;

private:
    std::string rule_name_;    ///< Name of the validation rule
    std::string field_value_;  ///< Value that failed validation
};

/**
 * @brief Exception thrown during vocabulary mapping operations
 * 
 * @section description Description
 * Thrown when errors occur during vocabulary mapping or
 * concept mapping operations.
 * 
 * @section features Features
 * - Mapping context
 * - Source/target info
 * - Error details
 */
class VocabularyException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a vocabulary exception with mapping details
     * @param message Error message
     * @param source_code Source vocabulary code
     * @param target_vocab Target vocabulary name
     * @param location Source location
     * 
     * @section details Implementation Details
     * - Stores mapping info
     * - Formats message
     * - Captures location
     */
    VocabularyException(std::string_view message,
                        std::string_view source_code,
                        std::string_view target_vocab,
                        const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the source vocabulary code
     * @return std::string_view The source code
     * 
     * @section details Implementation Details
     * - Returns stored code
     * - Thread-safe
     */
    [[nodiscard]] std::string_view source_code() const noexcept;

    /**
     * @brief Get the target vocabulary name
     * @return std::string_view The target vocabulary
     * 
     * @section details Implementation Details
     * - Returns stored name
     * - Thread-safe
     */
    [[nodiscard]] std::string_view target_vocab() const noexcept;

private:
    std::string source_code_;   ///< Source vocabulary code
    std::string target_vocab_;  ///< Target vocabulary name
};

/**
 * @brief Exception thrown during API operations
 */
class ApiException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct an API exception with HTTP details
     * @param message Error message
     * @param http_status HTTP status code
     * @param endpoint The API endpoint
     * @param location Source location
     */
    ApiException(std::string_view message,
                 int http_status,
                 std::string_view endpoint,
                 const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the HTTP status code
     * @return int The HTTP status code
     */
    [[nodiscard]] int http_status() const noexcept;

    /**
     * @brief Get the API endpoint
     * @return std::string_view The endpoint
     */
    [[nodiscard]] std::string_view endpoint() const noexcept;

private:
    int http_status_;
    std::string endpoint_;
};

} // namespace omop::common

#endif // OMOP_COMMON_EXCEPTIONS_H