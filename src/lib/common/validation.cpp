/**
 * @file validation.cpp
 * @brief Implementation of data validation framework for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "validation.h"
#include "exceptions.h"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <cctype>
#include <yaml-cpp/yaml.h>
#include <chrono>
#include <nlohmann/json.hpp>

namespace omop::common {

// ValidationResult implementation
void ValidationResult::merge(const ValidationResult& other) {
    is_valid = is_valid && other.is_valid;
    errors.insert(errors.end(), other.errors.begin(), other.errors.end());
    warnings.insert(warnings.end(), other.warnings.begin(), other.warnings.end());
    records_validated += other.records_validated;
    records_failed += other.records_failed;
}

// ValidationRule base class implementation
ValidationRule::ValidationRule(const std::string& field_name,
                             ValidationType type,
                             const std::string& error_message)
    : field_name_(field_name), type_(type), error_message_(error_message) {}

std::string ValidationRule::getErrorMessage() const {
    if (!error_message_.empty()) {
        return error_message_;
    }

    // Generate default error message based on validation type
    std::stringstream ss;
    ss << "Field '" << field_name_ << "' failed validation: ";

    switch (type_) {
        case ValidationType::NOT_NULL:
            ss << "value is null";
            break;
        case ValidationType::NOT_EMPTY:
            ss << "value is empty";
            break;
        case ValidationType::UNIQUE:
            ss << "value is not unique";
            break;
        case ValidationType::IN_LIST:
            ss << "value is not in allowed list";
            break;
        case ValidationType::REGEX:
            ss << "value does not match required pattern";
            break;
        case ValidationType::DATE_RANGE:
            ss << "date is outside allowed range";
            break;
        case ValidationType::NUMERIC_RANGE:
            ss << "number is outside allowed range";
            break;
        case ValidationType::GREATER_THAN:
            ss << "value is not greater than required minimum";
            break;
        case ValidationType::LESS_THAN:
            ss << "value is not less than required maximum";
            break;
        case ValidationType::BETWEEN:
            ss << "value is not between required bounds";
            break;
        case ValidationType::BEFORE:
            ss << "date is not before required date";
            break;
        case ValidationType::AFTER:
            ss << "date is not after required date";
            break;
        case ValidationType::LENGTH:
            ss << "string length is invalid";
            break;
        case ValidationType::CUSTOM:
            ss << "custom validation failed";
            break;
        case ValidationType::NOT_ZERO:
            ss << "value is zero";
            break;
        case ValidationType::NOT_FUTURE_DATE:
            ss << "date is in the future";
            break;
        case ValidationType::FOREIGN_KEY:
            ss << "foreign key constraint violation";
            break;
        case ValidationType::COMPOSITE_KEY:
            ss << "composite key validation failed";
            break;
        case ValidationType::CONDITIONAL:
            ss << "conditional validation failed";
            break;
        default:
            ss << "validation failed";
    }

    return ss.str();
}

// NotNullRule implementation
NotNullRule::NotNullRule(const std::string& field_name)
    : ValidationRule(field_name, ValidationType::NOT_NULL) {}

bool NotNullRule::validate(const std::any& value,
                          const std::unordered_map<std::string, std::any>& record) const {
    return value.has_value();
}

// InListRule implementation
template<typename T>
InListRule<T>::InListRule(const std::string& field_name, const std::vector<T>& allowed_values)
    : ValidationRule(field_name, ValidationType::IN_LIST), allowed_values_(allowed_values) {}

template<typename T>
bool InListRule<T>::validate(const std::any& value,
                            const std::unordered_map<std::string, std::any>& record) const {
    if (!value.has_value()) {
        return false;
    }

    try {
        const T& val = std::any_cast<const T&>(value);
        return std::find(allowed_values_.begin(), allowed_values_.end(), val) != allowed_values_.end();
    } catch (const std::bad_any_cast&) {
        return false;
    }
}

// Explicit template instantiations
template class InListRule<int>;
template class InListRule<int64_t>;
template class InListRule<std::string>;
template class InListRule<double>;

// DateRangeRule implementation
DateRangeRule::DateRangeRule(const std::string& field_name,
                           const std::optional<std::chrono::system_clock::time_point>& min_date,
                           const std::optional<std::chrono::system_clock::time_point>& max_date)
    : ValidationRule(field_name, ValidationType::DATE_RANGE),
      min_date_(min_date), max_date_(max_date) {}

bool DateRangeRule::validate(const std::any& value,
                           const std::unordered_map<std::string, std::any>& record) const {
    if (!value.has_value()) {
        return false;
    }

    try {
        const auto& date = std::any_cast<const std::chrono::system_clock::time_point&>(value);

        if (min_date_ && date < *min_date_) {
            return false;
        }

        if (max_date_ && date > *max_date_) {
            return false;
        }

        return true;
    } catch (const std::bad_any_cast&) {
        return false;
    }
}

// NumericRangeRule implementation
template<typename T>
NumericRangeRule<T>::NumericRangeRule(const std::string& field_name,
                                     const std::optional<T>& min_value,
                                     const std::optional<T>& max_value)
    : ValidationRule(field_name, ValidationType::NUMERIC_RANGE),
      min_value_(min_value), max_value_(max_value) {}

template<typename T>
bool NumericRangeRule<T>::validate(const std::any& value,
                                  const std::unordered_map<std::string, std::any>& record) const {
    if (!value.has_value()) {
        return false;
    }

    try {
        // First try to cast to the exact type T
        const T& val = std::any_cast<const T&>(value);

        if (min_value_ && val < *min_value_) {
            return false;
        }

        if (max_value_ && val > *max_value_) {
            return false;
        }

        return true;
    } catch (const std::bad_any_cast&) {
        // If exact type cast fails, try to convert from other numeric types
        if constexpr (std::is_same_v<T, double>) {
            try {
                // Try to convert from int to double
                int int_val = std::any_cast<int>(value);
                double val = static_cast<double>(int_val);

                if (min_value_ && val < *min_value_) {
                    return false;
                }

                if (max_value_ && val > *max_value_) {
                    return false;
                }

                return true;
            } catch (const std::bad_any_cast&) {
                try {
                    // Try to convert from float to double
                    float float_val = std::any_cast<float>(value);
                    double val = static_cast<double>(float_val);

                    if (min_value_ && val < *min_value_) {
                        return false;
                    }

                    if (max_value_ && val > *max_value_) {
                        return false;
                    }

                    return true;
                } catch (const std::bad_any_cast&) {
                    return false;
                }
            }
        } else if constexpr (std::is_same_v<T, int>) {
            try {
                // Try to convert from double to int (with range check)
                double double_val = std::any_cast<double>(value);
                if (double_val >= std::numeric_limits<int>::min() &&
                    double_val <= std::numeric_limits<int>::max() &&
                    double_val == std::floor(double_val)) {
                    int val = static_cast<int>(double_val);

                    if (min_value_ && val < *min_value_) {
                        return false;
                    }

                    if (max_value_ && val > *max_value_) {
                        return false;
                    }

                    return true;
                }
                return false;
            } catch (const std::bad_any_cast&) {
                return false;
            }
        }
        return false;
    }
}

// Explicit template instantiations
template class NumericRangeRule<int>;
template class NumericRangeRule<int64_t>;
template class NumericRangeRule<double>;
template class NumericRangeRule<float>;

// RegexRule implementation
RegexRule::RegexRule(const std::string& field_name, const std::string& pattern)
    : ValidationRule(field_name, ValidationType::REGEX), pattern_(pattern) {}

bool RegexRule::validate(const std::any& value,
                        const std::unordered_map<std::string, std::any>& record) const {
    if (!value.has_value()) {
        return false;
    }

    try {
        const auto& str = std::any_cast<const std::string&>(value);
        return std::regex_match(str, pattern_);
    } catch (const std::bad_any_cast&) {
        return false;
    }
}

// CustomRule implementation
CustomRule::CustomRule(const std::string& field_name,
                      ValidationFunction validator,
                      const std::string& error_message)
    : ValidationRule(field_name, ValidationType::CUSTOM, error_message),
      validator_(validator) {}

bool CustomRule::validate(const std::any& value,
                         const std::unordered_map<std::string, std::any>& record) const {
    return validator_(value, record);
}

// ValidationEngine implementation
ValidationEngine::ValidationEngine(std::shared_ptr<Logger> logger)
    : logger_(logger ? logger : Logger::get("ValidationEngine")) {
    // Enable all validation types by default
    for (int i = 0; i <= static_cast<int>(ValidationType::CONDITIONAL); ++i) {
        enabled_types_[static_cast<ValidationType>(i)] = true;
    }
}

void ValidationEngine::addRule(std::unique_ptr<ValidationRule> rule) {
    rules_.push_back(std::move(rule));
}

ValidationResult ValidationEngine::validateRecord(const std::unordered_map<std::string, std::any>& record) {
    ValidationResult result;
    result.is_valid = true;
    result.records_validated = 1;
    result.records_failed = 0;

    for (const auto& rule : rules_) {
        if (!enabled_types_[rule->getType()]) {
            continue;
        }

        const auto& field_name = rule->getFieldName();
        auto it = record.find(field_name);

        std::any value;
        if (it != record.end()) {
            value = it->second;
        }

        if (!rule->validate(value, record)) {
            result.is_valid = false;
            result.errors.push_back(rule->getErrorMessage());
            logger_->debug("Validation failed for field '{}': {}",
                         field_name, rule->getErrorMessage());
        }
    }

    if (!result.is_valid) {
        result.records_failed = 1;
    }

    return result;
}

ValidationResult ValidationEngine::validateBatch(
    const std::vector<std::unordered_map<std::string, std::any>>& records,
    bool stop_on_error) {

    ValidationResult total_result;
    total_result.is_valid = true;
    total_result.records_validated = 0;
    total_result.records_failed = 0;

    for (size_t i = 0; i < records.size(); ++i) {
        auto result = validateRecord(records[i]);

        if (!result.is_valid) {
            // Add record index to error messages
            for (auto& error : result.errors) {
                error = "Record " + std::to_string(i) + ": " + error;
            }

            if (stop_on_error) {
                total_result.merge(result);
                break;
            }
        }

        total_result.merge(result);
    }

    return total_result;
}

void ValidationEngine::clearRules() {
    rules_.clear();
}

void ValidationEngine::setValidationTypeEnabled(ValidationType type, bool enabled) {
    enabled_types_[type] = enabled;
}

// ValidationRuleFactory implementation
std::unique_ptr<ValidationRule> ValidationRuleFactory::createRule(
    const std::unordered_map<std::string, std::any>& config) {

    auto field_it = config.find("field");
    auto type_it = config.find("type");

    if (field_it == config.end() || type_it == config.end()) {
        throw ConfigurationException("Validation rule missing required 'field' or 'type'");
    }

    std::string field_name = std::any_cast<std::string>(field_it->second);
    std::string type_str = std::any_cast<std::string>(type_it->second);

    // Get custom error message if provided
    std::string error_message;
    auto error_it = config.find("error_message");
    if (error_it != config.end()) {
        error_message = std::any_cast<std::string>(error_it->second);
    }

    if (type_str == "not_null") {
        return std::make_unique<NotNullRule>(field_name);
    }
    else if (type_str == "in_list") {
        auto values_it = config.find("values");
        if (values_it == config.end()) {
            throw ConfigurationException("in_list validation requires 'values' parameter");
        }

        // Handle different value types
        try {
            auto int_values = std::any_cast<std::vector<int>>(values_it->second);
            return std::make_unique<InListRule<int>>(field_name, int_values);
        } catch (const std::bad_any_cast&) {
            try {
                auto str_values = std::any_cast<std::vector<std::string>>(values_it->second);
                return std::make_unique<InListRule<std::string>>(field_name, str_values);
            } catch (const std::bad_any_cast&) {
                try {
                    auto double_values = std::any_cast<std::vector<double>>(values_it->second);
                    return std::make_unique<InListRule<double>>(field_name, double_values);
                } catch (const std::bad_any_cast&) {
                    throw ConfigurationException("Invalid values type for in_list validation");
                }
            }
        }
    }
    else if (type_str == "regex") {
        auto pattern_it = config.find("pattern");
        if (pattern_it == config.end()) {
            throw ConfigurationException("regex validation requires 'pattern' parameter");
        }

        std::string pattern = std::any_cast<std::string>(pattern_it->second);
        return std::make_unique<RegexRule>(field_name, pattern);
    }
    else if (type_str == "numeric_range") {
        std::optional<double> min_value, max_value;

        auto min_it = config.find("min");
        if (min_it != config.end()) {
            // Handle both int and double types
            try {
                min_value = std::any_cast<double>(min_it->second);
            } catch (const std::bad_any_cast&) {
                try {
                    min_value = static_cast<double>(std::any_cast<int>(min_it->second));
                } catch (const std::bad_any_cast&) {
                    throw ConfigurationException("Invalid min value type for numeric_range validation");
                }
            }
        }

        auto max_it = config.find("max");
        if (max_it != config.end()) {
            // Handle both int and double types
            try {
                max_value = std::any_cast<double>(max_it->second);
            } catch (const std::bad_any_cast&) {
                try {
                    max_value = static_cast<double>(std::any_cast<int>(max_it->second));
                } catch (const std::bad_any_cast&) {
                    throw ConfigurationException("Invalid max value type for numeric_range validation");
                }
            }
        }

        return std::make_unique<NumericRangeRule<double>>(field_name, min_value, max_value);
    }
    else if (type_str == "date_range") {
        std::optional<std::chrono::system_clock::time_point> min_date, max_date;

        auto min_it = config.find("min_date");
        if (min_it != config.end()) {
            // Parse date string
            std::string min_date_str = std::any_cast<std::string>(min_it->second);
            std::tm tm = {};
            std::istringstream ss(min_date_str);
            ss >> std::get_time(&tm, "%Y-%m-%d");
            if (!ss.fail()) {
                min_date = std::chrono::system_clock::from_time_t(std::mktime(&tm));
            }
        }

        auto max_it = config.find("max_date");
        if (max_it != config.end()) {
            // Parse date string
            std::string max_date_str = std::any_cast<std::string>(max_it->second);
            std::tm tm = {};
            std::istringstream ss(max_date_str);
            ss >> std::get_time(&tm, "%Y-%m-%d");
            if (!ss.fail()) {
                max_date = std::chrono::system_clock::from_time_t(std::mktime(&tm));
            }
        }

        return std::make_unique<DateRangeRule>(field_name, min_date, max_date);
    }
    else if (type_str == "custom") {
        // Custom rules need to be registered separately
        throw ConfigurationException("Custom validation rules must be registered programmatically");
    }

    throw ConfigurationException("Unknown validation type: " + type_str);
}

std::unique_ptr<ValidationEngine> ValidationRuleFactory::createEngineFromYaml(const std::string& yaml_config) {
    auto engine = std::make_unique<ValidationEngine>();

    try {
        YAML::Node config = YAML::Load(yaml_config);

        if (config["validation_rules"]) {
            for (const auto& rule_node : config["validation_rules"]) {
                std::unordered_map<std::string, std::any> rule_config;

                for (const auto& item : rule_node) {
                    std::string key = item.first.as<std::string>();

                    // Convert YAML node to appropriate type
                    if (item.second.IsScalar()) {
                        // Try to determine the type
                        std::string scalar_value = item.second.as<std::string>();

                        // Try to parse as number first
                        try {
                            if (scalar_value.find('.') != std::string::npos) {
                                double d = std::stod(scalar_value);
                                rule_config[key] = d;
                            } else {
                                int i = std::stoi(scalar_value);
                                rule_config[key] = i;
                            }
                        } catch (...) {
                            // Not a number, store as string
                            rule_config[key] = scalar_value;
                        }
                    } else if (item.second.IsSequence()) {
                        // Check if it's a list of strings or numbers
                        if (item.second.size() > 0) {
                            if (item.second[0].IsScalar()) {
                                std::string first_val = item.second[0].as<std::string>();

                                try {
                                    // Try integer list
                                    std::stoi(first_val);
                                    std::vector<int> values;
                                    for (const auto& val : item.second) {
                                        values.push_back(val.as<int>());
                                    }
                                    rule_config[key] = values;
                                } catch (...) {
                                    try {
                                        // Try double list
                                        std::stod(first_val);
                                        std::vector<double> values;
                                        for (const auto& val : item.second) {
                                            values.push_back(val.as<double>());
                                        }
                                        rule_config[key] = values;
                                    } catch (...) {
                                        // String list
                                        std::vector<std::string> values;
                                        for (const auto& val : item.second) {
                                            values.push_back(val.as<std::string>());
                                        }
                                        rule_config[key] = values;
                                    }
                                }
                            }
                        }
                    }
                }

                auto rule = createRule(rule_config);
                engine->addRule(std::move(rule));
            }
        }
    } catch (const YAML::Exception& e) {
        throw ConfigurationException("Failed to parse validation YAML: " + std::string(e.what()));
    }

    return engine;
}

// ValidationUtils implementation
namespace ValidationUtils {

bool isValidEmail(const std::string& email) {
    const std::regex pattern(R"(^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$)");
    return std::regex_match(email, pattern);
}

bool isValidPhoneNumber(const std::string& phone, const std::string& country_code) {
    if (country_code == "US") {
        // US phone number: +1 (XXX) XXX-XXXX or variations
        const std::regex pattern(R"(^\+?1?\s*\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}$)");
        return std::regex_match(phone, pattern);
    } else if (country_code == "UK" || country_code == "GB") {
        // UK phone number
        const std::regex pattern(R"(^\+?44\s?7\d{3}\s?\d{6}$|^0?7\d{3}\s?\d{6}$)");
        return std::regex_match(phone, pattern);
    } else if (country_code == "CA") {
        // Canadian phone number (same format as US)
        const std::regex pattern(R"(^\+?1?\s*\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}$)");
        return std::regex_match(phone, pattern);
    }
    // Add more country-specific patterns as needed
    return false;
}

bool isValidPostalCode(const std::string& postal_code, const std::string& country_code) {
    if (country_code == "US") {
        // US ZIP code: XXXXX or XXXXX-XXXX
        const std::regex pattern(R"(^\d{5}(-\d{4})?$)");
        return std::regex_match(postal_code, pattern);
    } else if (country_code == "CA") {
        // Canadian postal code: A1A 1A1
        const std::regex pattern(R"(^[A-Za-z]\d[A-Za-z]\s?\d[A-Za-z]\d$)");
        return std::regex_match(postal_code, pattern);
    } else if (country_code == "UK" || country_code == "GB") {
        // UK postcode
        const std::regex pattern(R"(^[A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2}$)", std::regex::icase);
        return std::regex_match(postal_code, pattern);
    }
    // Add more country-specific patterns as needed
    return false;
}

bool isValidUUID(const std::string& uuid) {
    const std::regex pattern(
        R"(^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$)");
    return std::regex_match(uuid, pattern);
}

bool isValidURL(const std::string& url) {
    const std::regex pattern(
        R"(^(https?://)?([a-zA-Z0-9.-]+)\.([a-zA-Z]{2,})(:[0-9]+)?(/.*)?$)");
    return std::regex_match(url, pattern);
}

std::string sanitizeString(const std::string& input) {
    std::string result;
    result.reserve(input.size());

    for (char ch : input) {
        // Remove control characters except tab, newline, and carriage return
        if ((ch >= 32 && ch <= 126) || ch == '\t' || ch == '\n' || ch == '\r') {
            result += ch;
        }
    }

    // Trim leading and trailing whitespace
    size_t start = result.find_first_not_of(" \t\n\r\f\v");
    if (start == std::string::npos) {
        return "";
    }
    size_t end = result.find_last_not_of(" \t\n\r\f\v");
    return result.substr(start, end - start + 1);
}

} // namespace ValidationUtils

} // namespace omop::common