#include "exceptions.h"

namespace omop::common {

OmopException::OmopException(std::string_view message,
                           const std::source_location& location)
    : message_(message), location_(location) {
    formatted_message_ = std::format("[{}:{}] {}: {}",
        location_.file_name(),
        location_.line(),
        location_.function_name(),
        message_);
}

const char* OmopException::what() const noexcept {
    return formatted_message_.c_str();
}

std::string_view OmopException::message() const noexcept {
    return message_;
}

const std::source_location& OmopException::location() const noexcept {
    return location_;
}

ConfigurationException::ConfigurationException(std::string_view message,
                                            std::string_view config_key,
                                            const std::source_location& location)
    : OmopException(std::format("{}: key '{}'", message, config_key), location),
      config_key_(config_key) {}

std::string_view ConfigurationException::config_key() const noexcept {
    return config_key_;
}

ExtractionException::ExtractionException(std::string_view message,
                                       std::string_view source_name,
                                       const std::source_location& location)
    : OmopException(std::format("Extraction error from '{}': {}", source_name, message), location),
      source_name_(source_name) {}

std::string_view ExtractionException::source_name() const noexcept {
    return source_name_;
}

TransformationException::TransformationException(std::string_view message,
                                               std::string_view field_name,
                                               std::string_view transformation_type,
                                               const std::source_location& location)
    : OmopException(std::format("Transformation error for field '{}' (type: {}): {}",
                               field_name, transformation_type, message), location),
      field_name_(field_name),
      transformation_type_(transformation_type) {}

std::string_view TransformationException::field_name() const noexcept {
    return field_name_;
}

std::string_view TransformationException::transformation_type() const noexcept {
    return transformation_type_;
}

LoadException::LoadException(std::string_view message,
                           std::string_view target_table,
                           const std::source_location& location)
    : OmopException(std::format("Load error for table '{}': {}", target_table, message), location),
      target_table_(target_table) {}

std::string_view LoadException::target_table() const noexcept {
    return target_table_;
}

DatabaseException::DatabaseException(std::string_view message,
                                   std::string_view database_type,
                                   int error_code,
                                   const std::source_location& location)
    : OmopException(std::format("Database error [{}] (code: {}): {}",
                               database_type, error_code, message), location),
      database_type_(database_type),
      error_code_(error_code) {}

std::string_view DatabaseException::database_type() const noexcept {
    return database_type_;
}

int DatabaseException::error_code() const noexcept {
    return error_code_;
}

ValidationException::ValidationException(std::string_view message,
                                       std::string_view rule_name,
                                       std::string_view field_value,
                                       const std::source_location& location)
    : OmopException(std::format("Validation failed for rule '{}' with value '{}': {}",
                               rule_name, field_value, message), location),
      rule_name_(rule_name),
      field_value_(field_value) {}

std::string_view ValidationException::rule_name() const noexcept {
    return rule_name_;
}

std::string_view ValidationException::field_value() const noexcept {
    return field_value_;
}

VocabularyException::VocabularyException(std::string_view message,
                                       std::string_view source_code,
                                       std::string_view target_vocab,
                                       const std::source_location& location)
    : OmopException(std::format("Vocabulary mapping error for '{}' with value '{}': {}",
                               target_vocab, source_code, message), location),
      source_code_(source_code),
      target_vocab_(target_vocab) {}

std::string_view VocabularyException::source_code() const noexcept {
    return source_code_;
}

std::string_view VocabularyException::target_vocab() const noexcept {
    return target_vocab_;
}

ApiException::ApiException(std::string_view message,
                         int http_status,
                         std::string_view endpoint,
                         const std::source_location& location)
    : OmopException(std::format("API error at endpoint '{}' (status: {}): {}",
                               endpoint, http_status, message), location),
      http_status_(http_status),
      endpoint_(endpoint) {}

int ApiException::http_status() const noexcept {
    return http_status_;
}

std::string_view ApiException::endpoint() const noexcept {
    return endpoint_;
}

} // namespace omop::common