/**
 * @file utilities.h
 * @brief Utility functions for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 * @version 1.0
 * 
 * @section overview Overview
 * This header file provides a comprehensive set of utility functions and classes
 * for the OMOP ETL pipeline. It includes string manipulation, date/time handling,
 * file system operations, and various helper functions.
 * 
 * @section design Design Principles
 * - Reusability: Common functionality in one place
 * - Performance: Optimized implementations
 * - Thread Safety: Safe concurrent usage
 * - Error Handling: Robust error checking
 * - Cross-platform: Works on all supported platforms
 * 
 * @section components Components
 * - StringUtils: String manipulation utilities
 * - DateTimeUtils: Date and time handling
 * - FileUtils: File system operations
 * - SystemUtils: System-related functions
 * - ValidationUtils: Data validation helpers
 * - ProcessingUtils: ETL processing utilities
 * 
 * @section usage Usage
 * To use the utilities:
 * 1. Include this header
 * 2. Use namespace omop::utils
 * 3. Call utility functions as needed
 * 
 * @section example Example
 * @code
 * using namespace omop::utils;
 * auto trimmed = StringUtils::trim("  hello  ");
 * auto date = DateTimeUtils::parse_date("2024-01-01");
 * auto files = FileUtils::list_files("/data", "*.csv");
 * @endcode
 */

#pragma once

#include <string>
#include <vector>
#include <optional>
#include <chrono>
#include <filesystem>
#include <unordered_map>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <regex>
#include <uuid/uuid.h>
#include <any>
#include <typeinfo>

namespace omop::utils {

/**
 * @brief Convert std::any to string representation
 * @param value Any value to convert
 * @return String representation
 * 
 * @section details Implementation Details
 * - Handles common types
 * - Uses type_info for type checking
 * - Returns type name for unknown types
 */
std::string any_to_string(const std::any& value);

/**
 * @brief String utility functions
 * 
 * @section description Description
 * Provides a comprehensive set of string manipulation functions
 * for common operations like trimming, case conversion, splitting,
 * joining, and pattern matching.
 * 
 * @section features Features
 * - String manipulation
 * - Case conversion
 * - Pattern matching
 * - SQL escaping
 * - Random string generation
 */
class StringUtils {
public:
    /**
     * @brief Trim whitespace from both ends of a string
     * @param str String to trim
     * @return Trimmed string
     * 
     * @section details Implementation Details
     * - Removes leading/trailing whitespace
     * - Handles all whitespace characters
     * - Returns new string
     */
    static std::string trim(const std::string& str);

    /**
     * @brief Convert string to lowercase
     * @param str Input string
     * @return Lowercase string
     * 
     * @section details Implementation Details
     * - Converts all characters
     * - Preserves non-alphabetic
     * - Returns new string
     */
    static std::string to_lower(const std::string& str);

    /**
     * @brief Convert string to uppercase
     * @param str Input string
     * @return Uppercase string
     * 
     * @section details Implementation Details
     * - Converts all characters
     * - Preserves non-alphabetic
     * - Returns new string
     */
    static std::string to_upper(const std::string& str);

    /**
     * @brief Split string by delimiter
     * @param str String to split
     * @param delimiter Delimiter character
     * @return Vector of substrings
     * 
     * @section details Implementation Details
     * - Splits on delimiter
     * - Handles empty strings
     * - Returns vector of parts
     */
    static std::vector<std::string> split(const std::string& str, char delimiter);

    /**
     * @brief Split string by string delimiter
     * @param str String to split
     * @param delimiter Delimiter string
     * @return Vector of substrings
     * 
     * @section details Implementation Details
     * - Splits on delimiter string
     * - Handles empty strings
     * - Returns vector of parts
     */
    static std::vector<std::string> split(const std::string& str, const std::string& delimiter);

    /**
     * @brief Join strings with delimiter
     * @param strings Strings to join
     * @param delimiter Delimiter to use
     * @return Joined string
     * 
     * @section details Implementation Details
     * - Joins with delimiter
     * - Handles empty vector
     * - Returns concatenated string
     */
    static std::string join(const std::vector<std::string>& strings, const std::string& delimiter);

    /**
     * @brief Replace all occurrences of a substring
     * @param str Input string
     * @param from Substring to replace
     * @param to Replacement string
     * @return Modified string
     * 
     * @section details Implementation Details
     * - Replaces all occurrences
     * - Handles empty strings
     * - Returns new string
     */
    static std::string replace_all(const std::string& str,
                                  const std::string& from,
                                  const std::string& to);

    /**
     * @brief Check if string starts with prefix
     * @param str String to check
     * @param prefix Prefix to look for
     * @return True if starts with prefix
     * 
     * @section details Implementation Details
     * - Case-sensitive comparison
     * - Handles empty strings
     * - Returns boolean result
     */
    static bool starts_with(const std::string& str, const std::string& prefix);

    /**
     * @brief Check if string ends with suffix
     * @param str String to check
     * @param suffix Suffix to look for
     * @return True if ends with suffix
     * 
     * @section details Implementation Details
     * - Case-sensitive comparison
     * - Handles empty strings
     * - Returns boolean result
     */
    static bool ends_with(const std::string& str, const std::string& suffix);

    /**
     * @brief Check if string contains substring
     * @param str String to search in
     * @param substr Substring to find
     * @return True if contains substring
     * 
     * @section details Implementation Details
     * - Case-sensitive search
     * - Handles empty strings
     * - Returns boolean result
     */
    static bool contains(const std::string& str, const std::string& substr);

    /**
     * @brief Convert string to snake_case
     * @param str Input string
     * @return Snake case string
     * 
     * @section details Implementation Details
     * - Converts to lowercase
     * - Adds underscores
     * - Returns new string
     */
    static std::string to_snake_case(const std::string& str);

    /**
     * @brief Convert string to camelCase
     * @param str Input string
     * @return Camel case string
     * 
     * @section details Implementation Details
     * - Converts to camelCase
     * - Handles special characters
     * - Returns new string
     */
    static std::string to_camel_case(const std::string& str);

    /**
     * @brief Escape SQL string
     * @param str String to escape
     * @return Escaped string
     * 
     * @section details Implementation Details
     * - Escapes special characters
     * - Handles quotes
     * - Returns safe string
     */
    static std::string escape_sql(const std::string& str);

    /**
     * @brief Generate random string
     * @param length String length
     * @return Random string
     * 
     * @section details Implementation Details
     * - Uses secure random
     * - Alphanumeric characters
     * - Returns new string
     */
    static std::string random_string(size_t length);
};

/**
 * @brief Date and time utility functions
 * 
 * @section description Description
 * Provides functions for date and time manipulation, parsing,
 * formatting, and calculations.
 * 
 * @section features Features
 * - Date parsing/formatting
 * - Timezone conversion
 * - Duration calculations
 * - Age calculation
 * - Date validation
 */
class DateTimeUtils {
public:
    using time_point = std::chrono::system_clock::time_point;

    /**
     * @brief Parse date string to time_point
     * @param date_str Date string
     * @param format Date format
     * @return Parsed time_point
     * 
     * @section details Implementation Details
     * - Parses according to format
     * - Returns optional result
     * - Handles invalid dates
     */
    static std::optional<time_point> parse_date(const std::string& date_str,
                                               const std::string& format = "%Y-%m-%d");

    /**
     * @brief Format time_point to string
     * @param tp Time point
     * @param format Output format
     * @return Formatted string
     * 
     * @section details Implementation Details
     * - Formats according to format
     * - Handles all time components
     * - Returns formatted string
     */
    static std::string format_date(const time_point& tp,
                                  const std::string& format = "%Y-%m-%d");

    /**
     * @brief Get current timestamp string
     * @param format Timestamp format
     * @return Current timestamp
     * 
     * @section details Implementation Details
     * - Gets current time
     * - Formats according to format
     * - Returns formatted string
     */
    static std::string current_timestamp(const std::string& format = "%Y-%m-%d %H:%M:%S");

    /**
     * @brief Convert between timezones
     * @param tp Time point
     * @param from_tz Source timezone
     * @param to_tz Target timezone
     * @return Converted time point
     * 
     * @section details Implementation Details
     * - Handles timezone rules
     * - Accounts for DST
     * - Returns converted time
     */
    static time_point convert_timezone(const time_point& tp,
                                      const std::string& from_tz,
                                      const std::string& to_tz);

    /**
     * @brief Add duration to time_point
     * @param tp Time point
     * @param days Days to add
     * @param hours Hours to add
     * @param minutes Minutes to add
     * @return New time point
     * 
     * @section details Implementation Details
     * - Adds specified duration
     * - Handles overflow
     * - Returns new time
     */
    static time_point add_duration(const time_point& tp,
                                  int days = 0,
                                  int hours = 0,
                                  int minutes = 0);

    /**
     * @brief Calculate age from birth date
     * @param birth_date Birth date
     * @param as_of_date Reference date
     * @return Age in years
     * 
     * @section details Implementation Details
     * - Calculates years
     * - Accounts for leap years
     * - Returns integer age
     */
    static int calculate_age(const time_point& birth_date,
                           const time_point& as_of_date = std::chrono::system_clock::now());

    /**
     * @brief Check if date is valid
     * @param year Year
     * @param month Month
     * @param day Day
     * @return True if valid date
     * 
     * @section details Implementation Details
     * - Validates year range
     * - Checks month/day
     * - Returns boolean result
     */
    static bool is_valid_date(int year, int month, int day);

    /**
     * @brief Get ISO week number
     * @param tp Time point
     * @return Week number (1-53)
     * 
     * @section details Implementation Details
     * - Calculates ISO week
     * - Handles year boundaries
     * - Returns week number
     */
    static int get_iso_week(const time_point& tp);
};

/**
 * @brief File system utility functions
 * 
 * @section description Description
 * Provides functions for file system operations including reading,
 * writing, and managing files and directories.
 * 
 * @section features Features
 * - File I/O operations
 * - Directory management
 * - Path manipulation
 * - File information
 * - Temporary files
 */
class FileUtils {
public:
    /**
     * @brief Read entire file to string
     * @param filepath File path
     * @return File contents
     * 
     * @section details Implementation Details
     * - Reads entire file
     * - Handles errors
     * - Returns optional string
     */
    static std::optional<std::string> read_file(const std::string& filepath);

    /**
     * @brief Write string to file
     * @param filepath File path
     * @param content Content to write
     * @param append Whether to append
     * @return True if successful
     * 
     * @section details Implementation Details
     * - Creates file if needed
     * - Handles append mode
     * - Returns success status
     */
    static bool write_file(const std::string& filepath,
                          const std::string& content,
                          bool append = false);

    /**
     * @brief Check if file exists
     * @param filepath File path
     * @return True if exists
     * 
     * @section details Implementation Details
     * - Checks file existence
     * - Handles permissions
     * - Returns boolean result
     */
    static bool file_exists(const std::string& filepath);

    /**
     * @brief Get file size
     * @param filepath File path
     * @return File size in bytes
     */
    static std::optional<size_t> file_size(const std::string& filepath);

    /**
     * @brief Get file extension
     * @param filepath File path
     * @return Extension (including dot)
     */
    static std::string get_extension(const std::string& filepath);

    /**
     * @brief Get file name without extension
     * @param filepath File path
     * @return Base filename
     */
    static std::string get_basename(const std::string& filepath);

    /**
     * @brief Get directory path
     * @param filepath File path
     * @return Directory path
     */
    static std::string get_directory(const std::string& filepath);

    /**
     * @brief Create directory (recursive)
     * @param path Directory path
     * @return True if successful
     */
    static bool create_directory(const std::string& path);

    /**
     * @brief List files in directory
     * @param directory Directory path
     * @param pattern Regex pattern filter
     * @param recursive Whether to search recursively
     * @return List of file paths
     */
    static std::vector<std::string> list_files(const std::string& directory,
                                              const std::string& pattern = ".*",
                                              bool recursive = false);

    /**
     * @brief Copy file
     * @param source Source path
     * @param destination Destination path
     * @return True if successful
     */
    static bool copy_file(const std::string& source, const std::string& destination);

    /**
     * @brief Move/rename file
     * @param source Source path
     * @param destination Destination path
     * @return True if successful
     */
    static bool move_file(const std::string& source, const std::string& destination);

    /**
     * @brief Delete file
     * @param filepath File path
     * @return True if successful
     */
    static bool delete_file(const std::string& filepath);

    /**
     * @brief Get temporary directory
     * @return Temporary directory path
     */
    static std::string get_temp_directory();

    /**
     * @brief Create temporary file
     * @param prefix Filename prefix
     * @param extension File extension
     * @return Temporary file path
     */
    static std::string create_temp_file(const std::string& prefix = "tmp",
                                       const std::string& extension = ".tmp");
};

/**
 * @brief System utility functions
 * 
 * @section description Description
 * Provides functions for system-related operations including
 * environment variables, process information, and system resources.
 * 
 * @section features Features
 * - Environment variables
 * - Process information
 * - System resources
 * - Network information
 * - Security functions
 */
class SystemUtils {
public:
    /**
     * @brief Get environment variable
     * @param name Variable name
     * @return Variable value
     * 
     * @section details Implementation Details
     * - Gets from environment
     * - Handles missing
     * - Returns optional string
     */
    static std::optional<std::string> get_env(const std::string& name);

    /**
     * @brief Set environment variable
     * @param name Variable name
     * @param value Variable value
     * @return True if successful
     * 
     * @section details Implementation Details
     * - Sets in environment
     * - Handles errors
     * - Returns success status
     */
    static bool set_env(const std::string& name, const std::string& value);

    /**
     * @brief Get current directory
     * @return Current directory path
     * 
     * @section details Implementation Details
     * - Gets working directory
     * - Handles errors
     * - Returns path string
     */
    static std::string get_current_directory();

    /**
     * @brief Get home directory
     * @return Home directory path
     * 
     * @section details Implementation Details
     * - Gets user home
     * - Handles errors
     * - Returns path string
     */
    static std::string get_home_directory();

    /**
     * @brief Get CPU count
     * @return Number of CPUs
     * 
     * @section details Implementation Details
     * - Gets system info
     * - Handles errors
     * - Returns CPU count
     */
    static unsigned int get_cpu_count();

    /**
     * @brief Get available memory
     * @return Available memory in bytes
     * 
     * @section details Implementation Details
     * - Gets system info
     * - Handles errors
     * - Returns memory size
     */
    static size_t get_available_memory();

    /**
     * @brief Get process ID
     * @return Process ID
     * 
     * @section details Implementation Details
     * - Gets process info
     * - Handles errors
     * - Returns PID
     */
    static int get_process_id();

    /**
     * @brief Execute system command
     * @param command Command to execute
     * @return Command output
     * 
     * @section details Implementation Details
     * - Executes command
     * - Captures output
     * - Returns optional string
     */
    static std::optional<std::string> execute_command(const std::string& command);

    /**
     * @brief Get hostname
     * @return Hostname string
     * 
     * @section details Implementation Details
     * - Gets system info
     * - Handles errors
     * - Returns hostname
     */
    static std::string get_hostname();

    /**
     * @brief Get username
     * @return Username string
     * 
     * @section details Implementation Details
     * - Gets user info
     * - Handles errors
     * - Returns username
     */
    static std::string get_username();

    /**
     * @brief Calculate MD5 hash
     * @param data Input data
     * @return MD5 hash string
     * 
     * @section details Implementation Details
     * - Calculates hash
     * - Returns hex string
     */
    static std::string md5(const std::string& data);

    /**
     * @brief Calculate SHA-256 hash
     * @param data Input data
     * @return SHA-256 hash string
     * 
     * @section details Implementation Details
     * - Calculates hash
     * - Returns hex string
     */
    static std::string sha256(const std::string& data);

    /**
     * @brief Base64 encode data
     * @param data Input data
     * @return Encoded string
     * 
     * @section details Implementation Details
     * - Encodes data
     * - Returns string
     */
    static std::string base64_encode(const std::vector<uint8_t>& data);

    /**
     * @brief Base64 decode string
     * @param encoded Encoded string
     * @return Decoded data
     * 
     * @section details Implementation Details
     * - Decodes string
     * - Returns vector
     */
    static std::vector<uint8_t> base64_decode(const std::string& encoded);

    /**
     * @brief Generate UUID
     * @return UUID string
     * 
     * @section details Implementation Details
     * - Generates UUID
     * - Returns string
     */
    static std::string generate_uuid();

    /**
     * @brief Generate random bytes
     * @param length Number of bytes
     * @return Random bytes
     * 
     * @section details Implementation Details
     * - Uses secure random
     * - Returns vector
     */
    static std::vector<uint8_t> random_bytes(size_t length);
};

/**
 * @brief Validation utility functions
 * 
 * @section description Description
 * Provides functions for validating various data types and formats.
 * 
 * @section features Features
 * - Email validation
 * - URL validation
 * - IP address validation
 * - Phone number validation
 * - Postal code validation
 */
class ValidationUtils {
public:
    /**
     * @brief Validate email address
     * @param email Email to validate
     * @return True if valid
     * 
     * @section details Implementation Details
     * - Checks format
     * - Validates domain
     * - Returns boolean
     */
    static bool is_valid_email(const std::string& email);

    /**
     * @brief Validate URL
     * @param url URL to validate
     * @return True if valid
     * 
     * @section details Implementation Details
     * - Checks format
     * - Validates protocol
     * - Returns boolean
     */
    static bool is_valid_url(const std::string& url);

    /**
     * @brief Validate IP address
     * @param ip IP to validate
     * @return True if valid
     * 
     * @section details Implementation Details
     * - Checks format
     * - Validates range
     * - Returns boolean
     */
    static bool is_valid_ip(const std::string& ip);

    /**
     * @brief Validate phone number
     * @param phone Phone to validate
     * @param country_code Country code
     * @return True if valid
     * 
     * @section details Implementation Details
     * - Checks format
     * - Validates country
     * - Returns boolean
     */
    static bool is_valid_phone(const std::string& phone,
                              const std::string& country_code = "US");

    /**
     * @brief Validate postal code
     * @param postal_code Code to validate
     * @param country_code Country code
     * @return True if valid
     * 
     * @section details Implementation Details
     * - Checks format
     * - Validates country
     * - Returns boolean
     */
    static bool is_valid_postal_code(const std::string& postal_code,
                                    const std::string& country_code = "US");

    /**
     * @brief Validate date format
     * @param date_str Date string
     * @param format Date format
     * @return True if valid
     * 
     * @section details Implementation Details
     * - Checks format
     * - Validates date
     * - Returns boolean
     */
    static bool is_valid_date_format(const std::string& date_str,
                                    const std::string& format);

    /**
     * @brief Validate JSON string
     * @param json JSON to validate
     * @return True if valid
     * 
     * @section details Implementation Details
     * - Checks syntax
     * - Validates structure
     * - Returns boolean
     */
    static bool is_valid_json(const std::string& json);

    /**
     * @brief Validate SQL identifier
     * @param identifier Identifier to validate
     * @return True if valid
     * 
     * @section details Implementation Details
     * - Checks syntax
     * - Validates length
     * - Returns boolean
     */
    static bool is_valid_sql_identifier(const std::string& identifier);
};

/**
 * @brief Processing utility functions
 * 
 * @section description Description
 * Provides functions for ETL processing operations including
 * timing, memory tracking, and performance metrics.
 * 
 * @section features Features
 * - Performance timing
 * - Memory tracking
 * - Throughput calculation
 * - Resource monitoring
 */
class ProcessingUtils {
public:
    /**
     * @brief Timer for measuring elapsed time
     * 
     * @section description Description
     * RAII-style timer for measuring elapsed time in various units.
     * 
     * @section features Features
     * - Automatic timing
     * - Multiple units
     * - High precision
     */
    class Timer {
    public:
        /**
         * @brief Constructor
         * 
         * @section details Implementation Details
         * - Starts timing
         * - Records start time
         */
        Timer() : start_(std::chrono::high_resolution_clock::now()) {}

        /**
         * @brief Reset timer
         * 
         * @section details Implementation Details
         * - Resets start time
         * - Continues timing
         */
        void reset() {
            start_ = std::chrono::high_resolution_clock::now();
        }

        /**
         * @brief Get elapsed seconds
         * @return Elapsed time in seconds
         * 
         * @section details Implementation Details
         * - Calculates duration
         * - Returns seconds
         */
        double elapsed_seconds() const {
            auto end = std::chrono::high_resolution_clock::now();
            return std::chrono::duration<double>(end - start_).count();
        }

        /**
         * @brief Get elapsed milliseconds
         * @return Elapsed time in milliseconds
         * 
         * @section details Implementation Details
         * - Calculates duration
         * - Returns milliseconds
         */
        double elapsed_milliseconds() const {
            return elapsed_seconds() * 1000.0;
        }

    private:
        std::chrono::time_point<std::chrono::high_resolution_clock> start_; ///< Start time
    };

    /**
     * @brief Memory usage tracker
     * 
     * @section description Description
     * Tracks memory usage for a process or operation.
     * 
     * @section features Features
     * - Current usage
     * - Peak usage
     * - Usage history
     */
    class MemoryTracker {
    public:
        /**
         * @brief Constructor
         * 
         * @section details Implementation Details
         * - Initializes tracking
         * - Records initial usage
         */
        MemoryTracker() : initial_usage_(current_usage()), peak_usage_(initial_usage_) {}

        /**
         * @brief Get current memory usage
         * @return Current usage in bytes
         * 
         * @section details Implementation Details
         * - Gets current usage
         * - Updates peak
         * - Returns bytes
         */
        size_t current_usage() const;

        /**
         * @brief Get peak memory usage
         * @return Peak usage in bytes
         * 
         * @section details Implementation Details
         * - Returns peak
         * - Thread-safe
         */
        size_t peak_usage() const;

        /**
         * @brief Reset tracking
         * 
         * @section details Implementation Details
         * - Resets peak
         * - Continues tracking
         */
        void reset();

    private:
        size_t initial_usage_;    ///< Initial memory usage
        mutable size_t peak_usage_; ///< Peak memory usage
    };

    /**
     * @brief Format bytes to human readable string
     * @param bytes Number of bytes
     * @return Formatted string
     * 
     * @section details Implementation Details
     * - Converts to units
     * - Adds suffix
     * - Returns string
     */
    static std::string format_bytes(size_t bytes);

    /**
     * @brief Format duration to human readable string
     * @param seconds Duration in seconds
     * @return Formatted string
     * 
     * @section details Implementation Details
     * - Converts to units
     * - Adds labels
     * - Returns string
     */
    static std::string format_duration(double seconds);

    /**
     * @brief Calculate throughput
     * @param items Number of items
     * @param seconds Time taken
     * @return Items per second
     * 
     * @section details Implementation Details
     * - Calculates rate
     * - Handles zero time
     * - Returns double
     */
    static double calculate_throughput(size_t items, double seconds);

    /**
     * @brief Get stage name
     * @param stage Stage number
     * @return Stage name
     * 
     * @section details Implementation Details
     * - Maps number to name
     * - Returns string
     */
    static std::string stage_name(int stage);
};

} // namespace omop::utils