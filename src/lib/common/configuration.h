/**
 * @file configuration.h
 * @brief Configuration management for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header file defines the configuration system for the OMOP ETL pipeline.
 * It provides classes and utilities for managing ETL configuration, including
 * database connections, table mappings, transformation rules, and ETL settings.
 * 
 * @section design Design Principles
 * - YAML-based Configuration: Human-readable and maintainable
 * - Type Safety: Strong typing for configuration values
 * - Validation: Built-in configuration validation
 * - Extensibility: Support for custom parameters
 * - Thread Safety: Safe concurrent access
 * 
 * @section components Components
 * - TransformationRule: Column transformation rules
 * - TableMapping: Source to target table mappings
 * - DatabaseConfig: Database connection settings
 * - ConfigurationManager: Main configuration handler
 * - Config: Global configuration access
 * 
 * @section usage Usage
 * To use the configuration system:
 * 1. Load configuration from YAML
 * 2. Access global config via Config::get()
 * 3. Retrieve specific settings
 * 4. Use type-safe accessors
 * 
 * @section example Example
 * @code
 * auto& config = Config::get();
 * config.load_config("etl_config.yaml");
 * auto source_db = config.get_source_db();
 * auto mappings = config.get_table_mappings();
 * @endcode
 */

#pragma once

#include "exceptions.h"

#include <string>
#include <string_view>
#include <memory>
#include <unordered_map>
#include <vector>
#include <optional>
#include <variant>
#include <yaml-cpp/yaml.h>
#include <any>

namespace omop::common {

/**
 * @brief Represents a single transformation rule in the ETL pipeline
 * 
 * @section description Description
 * This class encapsulates the mapping rules from source columns to target columns,
 * including the transformation type and any additional parameters needed for the
 * transformation process.
 * 
 * @section features Features
 * - Multiple transformation types
 * - Parameter configuration
 * - Multi-column support
 * - Type safety
 */
class TransformationRule {
public:
    /**
     * @brief Enumeration of supported transformation types
     * 
     * @section types Types
     * - Direct: Simple column mapping
     * - DateTransform: Date format conversion
     * - VocabularyMapping: Code mapping
     * - DateCalculation: Date arithmetic
     * - NumericTransform: Number processing
     * - StringConcatenation: Text joining
     * - Conditional: Rule-based mapping
     * - Custom: User-defined logic
     */
    enum class Type {
        Direct,              ///< Direct column mapping without transformation
        DateTransform,       ///< Date format transformation
        VocabularyMapping,   ///< Map using vocabulary lookup
        DateCalculation,     ///< Calculate date based on multiple fields
        NumericTransform,    ///< Numeric value transformation
        StringConcatenation, ///< Concatenate multiple string fields
        Conditional,         ///< Conditional transformation based on rules
        Custom              ///< Custom transformation logic
    };

    /**
     * @brief Default constructor
     * 
     * @section details Implementation Details
     * - Initializes with default values
     * - Sets type to Direct
     */
    TransformationRule() = default;

    /**
     * @brief Construct from YAML node
     * @param node YAML node containing transformation configuration
     * 
     * @section details Implementation Details
     * - Parses source/target columns
     * - Sets transformation type
     * - Loads parameters
     * 
     * @throws ConfigurationError if node is invalid
     */
    explicit TransformationRule(const YAML::Node& node);

    /**
     * @brief Get source column name
     * @return std::string_view Source column name
     * 
     * @section details Implementation Details
     * - Returns single source column
     * - Thread-safe
     */
    [[nodiscard]] std::string_view source_column() const noexcept { return source_column_; }

    /**
     * @brief Get source columns for multi-column transformations
     * @return const std::vector<std::string>& Vector of source column names
     * 
     * @section details Implementation Details
     * - Returns all source columns
     * - Thread-safe
     */
    [[nodiscard]] const std::vector<std::string>& source_columns() const noexcept {
        return source_columns_;
    }

    /**
     * @brief Get target column name
     * @return std::string_view Target column name
     * 
     * @section details Implementation Details
     * - Returns target column
     * - Thread-safe
     */
    [[nodiscard]] std::string_view target_column() const noexcept { return target_column_; }

    /**
     * @brief Get transformation type
     * @return Type The transformation type
     * 
     * @section details Implementation Details
     * - Returns transformation type
     * - Thread-safe
     */
    [[nodiscard]] Type type() const noexcept { return type_; }

    /**
     * @brief Get transformation parameters
     * @return const YAML::Node& Additional parameters for the transformation
     * 
     * @section details Implementation Details
     * - Returns parameter node
     * - Thread-safe
     */
    [[nodiscard]] const YAML::Node& parameters() const noexcept { return parameters_; }

    /**
     * @brief Check if this is a multi-column transformation
     * @return bool True if multiple source columns are involved
     * 
     * @section details Implementation Details
     * - Checks source_columns_ size
     * - Thread-safe
     */
    [[nodiscard]] bool is_multi_column() const noexcept { return !source_columns_.empty(); }

private:
    std::string source_column_;              ///< Single source column name
    std::vector<std::string> source_columns_; ///< Multiple source columns
    std::string target_column_;              ///< Target column name
    Type type_{Type::Direct};                ///< Transformation type
    YAML::Node parameters_;                  ///< Additional parameters
};

/**
 * @brief Configuration for a single table mapping
 * 
 * @section description Description
 * This class represents the complete mapping configuration for transforming
 * data from a source table to an OMOP CDM target table.
 * 
 * @section features Features
 * - Source/target table mapping
 * - Transformation rules
 * - Pre/post processing SQL
 * - Filter conditions
 * - Validation rules
 */
class TableMapping {
public:
    /**
     * @brief Default constructor
     * 
     * @section details Implementation Details
     * - Initializes with default values
     */
    TableMapping() = default;

    /**
     * @brief Construct from YAML node
     * @param node YAML node containing table mapping configuration
     * 
     * @section details Implementation Details
     * - Parses table names
     * - Loads transformations
     * - Sets up SQL processing
     * 
     * @throws ConfigurationError if node is invalid
     */
    explicit TableMapping(const YAML::Node& node);

    /**
     * @brief Get source table name
     * @return std::string_view Source table name
     * 
     * @section details Implementation Details
     * - Returns source table
     * - Thread-safe
     */
    [[nodiscard]] std::string_view source_table() const noexcept { return source_table_; }

    /**
     * @brief Get target table name
     * @return std::string_view Target table name
     * 
     * @section details Implementation Details
     * - Returns target table
     * - Thread-safe
     */
    [[nodiscard]] std::string_view target_table() const noexcept { return target_table_; }

    /**
     * @brief Get transformation rules
     * @return const std::vector<TransformationRule>& Vector of transformation rules
     * 
     * @section details Implementation Details
     * - Returns all rules
     * - Thread-safe
     */
    [[nodiscard]] const std::vector<TransformationRule>& transformations() const noexcept {
        return transformations_;
    }

    /**
     * @brief Get pre-processing SQL query
     * @return std::optional<std::string> Optional pre-processing SQL
     * 
     * @section details Implementation Details
     * - Returns SQL if set
     * - Thread-safe
     */
    [[nodiscard]] const std::optional<std::string>& pre_process_sql() const noexcept {
        return pre_process_sql_;
    }

    /**
     * @brief Get post-processing SQL query
     * @return std::optional<std::string> Optional post-processing SQL
     * 
     * @section details Implementation Details
     * - Returns SQL if set
     * - Thread-safe
     */
    [[nodiscard]] const std::optional<std::string>& post_process_sql() const noexcept {
        return post_process_sql_;
    }

    /**
     * @brief Get filter conditions
     * @return const YAML::Node& Filter conditions as YAML node
     * 
     * @section details Implementation Details
     * - Returns filter node
     * - Thread-safe
     */
    [[nodiscard]] const YAML::Node& filters() const noexcept { return filters_; }

    /**
     * @brief Get validation rules
     * @return const YAML::Node& Validation rules as YAML node
     * 
     * @section details Implementation Details
     * - Returns validation node
     * - Thread-safe
     */
    [[nodiscard]] const YAML::Node& validations() const noexcept { return validations_; }

private:
    std::string source_table_;                    ///< Source table name
    std::string target_table_;                    ///< Target table name
    std::vector<TransformationRule> transformations_; ///< Transformation rules
    std::optional<std::string> pre_process_sql_;  ///< Pre-processing SQL
    std::optional<std::string> post_process_sql_; ///< Post-processing SQL
    YAML::Node filters_;                          ///< Filter conditions
    YAML::Node validations_;                      ///< Validation rules
};

/**
 * @brief Database connection configuration
 * 
 * @section description Description
 * Encapsulates database connection settings including type, credentials,
 * and connection parameters.
 * 
 * @section features Features
 * - Multiple database types
 * - Connection string support
 * - Credential management
 * - Custom parameters
 */
class DatabaseConfig {
public:
    /**
     * @brief Database types supported
     * 
     * @section types Types
     * - PostgreSQL: PostgreSQL database
     * - MySQL: MySQL database
     * - MSSQL: Microsoft SQL Server
     * - Oracle: Oracle database
     */
    enum class Type {
        PostgreSQL, ///< PostgreSQL database
        MySQL,      ///< MySQL database
        MSSQL,      ///< Microsoft SQL Server
        Oracle      ///< Oracle database
    };

    /**
     * @brief Default constructor
     * 
     * @section details Implementation Details
     * - Initializes with default values
     */
    DatabaseConfig() = default;

    /**
     * @brief Construct from YAML node
     * @param node YAML node containing database configuration
     * 
     * @section details Implementation Details
     * - Parses connection details
     * - Sets database type
     * - Loads parameters
     * 
     * @throws ConfigurationError if node is invalid
     */
    explicit DatabaseConfig(const YAML::Node& node);

    /**
     * @brief Get database type
     * @return Type Database type
     * 
     * @section details Implementation Details
     * - Returns database type
     * - Thread-safe
     */
    [[nodiscard]] Type type() const noexcept { return type_; }

    /**
     * @brief Get connection string
     * @return std::string_view Connection string
     * 
     * @section details Implementation Details
     * - Returns connection string
     * - Thread-safe
     */
    [[nodiscard]] std::string_view connection_string() const noexcept {
        return connection_string_;
    }

    /**
     * @brief Get host name
     * @return std::string_view Host name
     * 
     * @section details Implementation Details
     * - Returns host name
     * - Thread-safe
     */
    [[nodiscard]] std::string_view host() const noexcept { return host_; }

    /**
     * @brief Get port number
     * @return int Port number
     * 
     * @section details Implementation Details
     * - Returns port number
     * - Thread-safe
     */
    [[nodiscard]] int port() const noexcept { return port_; }

    /**
     * @brief Get database name
     * @return std::string_view Database name
     * 
     * @section details Implementation Details
     * - Returns database name
     * - Thread-safe
     */
    [[nodiscard]] std::string_view database() const noexcept { return database_; }

    /**
     * @brief Get username
     * @return std::string_view Username
     * 
     * @section details Implementation Details
     * - Returns username
     * - Thread-safe
     */
    [[nodiscard]] std::string_view username() const noexcept { return username_; }

    /**
     * @brief Get password
     * @return std::string_view Password
     * 
     * @section details Implementation Details
     * - Returns password
     * - Thread-safe
     */
    [[nodiscard]] std::string_view password() const noexcept { return password_; }

    /**
     * @brief Get additional connection parameters
     * @return const std::unordered_map<std::string, std::string>& Additional parameters
     * 
     * @section details Implementation Details
     * - Returns parameter map
     * - Thread-safe
     */
    [[nodiscard]] const std::unordered_map<std::string, std::string>& parameters() const noexcept {
        return parameters_;
    }

private:
    Type type_;                                  ///< Database type
    std::string connection_string_;              ///< Connection string
    std::string host_;                           ///< Host name
    int port_{0};                                ///< Port number
    std::string database_;                       ///< Database name
    std::string username_;                       ///< Username
    std::string password_;                       ///< Password
    std::unordered_map<std::string, std::string> parameters_; ///< Additional parameters
};

/**
 * @brief Main configuration manager
 * 
 * @section description Description
 * Manages the complete ETL configuration including database connections,
 * table mappings, and ETL settings.
 * 
 * @section features Features
 * - YAML configuration loading
 * - Table mapping management
 * - Database configuration
 * - Vocabulary mappings
 * - ETL settings
 * 
 * @section thread_safety Thread Safety
 * The configuration manager is thread-safe and can be used from multiple
 * threads. All public methods are synchronized appropriately.
 */
class ConfigurationManager {
public:
    /**
     * @brief Load configuration from file
     * @param filepath Path to configuration file
     * 
     * @section details Implementation Details
     * - Loads YAML file
     * - Parses configuration
     * - Validates settings
     * 
     * @throws ConfigurationError if file is invalid
     */
    void load_config(const std::string& filepath);

    /**
     * @brief Load configuration from string
     * @param yaml_content YAML configuration content
     * 
     * @section details Implementation Details
     * - Parses YAML string
     * - Validates settings
     * 
     * @throws ConfigurationError if content is invalid
     */
    void load_config_from_string(const std::string& yaml_content);

    /**
     * @brief Get source database configuration
     * @return const DatabaseConfig& Source database config
     * 
     * @section details Implementation Details
     * - Returns source config
     * - Thread-safe
     */
    [[nodiscard]] const DatabaseConfig& get_source_db() const noexcept {
        return source_db_;
    }

    /**
     * @brief Get target database configuration
     * @return const DatabaseConfig& Target database config
     * 
     * @section details Implementation Details
     * - Returns target config
     * - Thread-safe
     */
    [[nodiscard]] const DatabaseConfig& get_target_db() const noexcept {
        return target_db_;
    }

    /**
     * @brief Get configuration value with default
     * @tparam T Value type
     * @param key Configuration key
     * @param default_value Default value
     * @return T Configuration value or default
     * 
     * @section details Implementation Details
     * - Returns value if found
     * - Returns default if not found
     * - Thread-safe
     */
    template<typename T>
    [[nodiscard]] T get_value_or(const std::string& key, const T& default_value) const {
        auto value = get_value(key);
        return value ? std::any_cast<T>(*value) : default_value;
    }

    /**
     * @brief Validate configuration
     * 
     * @section details Implementation Details
     * - Checks required fields
     * - Validates relationships
     * - Ensures consistency
     * 
     * @throws ConfigurationError if validation fails
     */
    void validate_config() const;

    /**
     * @brief Check if configuration is loaded
     * @return bool True if configuration is loaded
     * 
     * @section details Implementation Details
     * - Returns load status
     * - Thread-safe
     */
    [[nodiscard]] bool is_loaded() const noexcept { return config_loaded_; }

    /**
     * @brief Get vocabulary mappings
     * @return const YAML::Node& Vocabulary mappings configuration
     * 
     * @section details Implementation Details
     * - Returns mappings node
     * - Thread-safe
     */
    [[nodiscard]] const YAML::Node& get_vocabulary_mappings() const noexcept {
        return vocabulary_mappings_;
    }

    /**
     * @brief Get ETL settings
     * @return const YAML::Node& ETL settings configuration
     *
     * @section details Implementation Details
     * - Returns settings node
     * - Thread-safe
     */
    [[nodiscard]] const YAML::Node& get_etl_settings() const noexcept {
        return etl_settings_;
    }

    /**
     * @brief Get table mapping by name
     * @param table_name Name of the table
     * @return std::optional<TableMapping> Table mapping if found
     *
     * @section details Implementation Details
     * - Searches table mappings
     * - Returns mapping if found
     * - Thread-safe
     */
    std::optional<TableMapping> get_table_mapping(const std::string& table_name) const;

    /**
     * @brief Get configuration value by key
     * @param key Configuration key
     * @return std::optional<YAML::Node> Value if found
     *
     * @section details Implementation Details
     * - Searches configuration tree
     * - Returns value if found
     * - Thread-safe
     */
    std::optional<YAML::Node> get_value(const std::string& key) const;

private:
    /**
     * @brief Parse table mappings from configuration
     * @param mappings_node YAML node containing table mappings
     * 
     * @section details Implementation Details
     * - Parses each mapping
     * - Validates structure
     * - Stores in map
     */
    void parse_table_mappings(const YAML::Node& mappings_node);

    /**
     * @brief Parse database configuration
     * @param db_node YAML node containing database configuration
     * @return DatabaseConfig Parsed database configuration
     * 
     * @section details Implementation Details
     * - Parses connection details
     * - Validates settings
     * - Returns config
     */
    DatabaseConfig parse_database_config(const YAML::Node& db_node);

    bool config_loaded_{false};                    ///< Configuration loaded flag
    YAML::Node root_config_;                       ///< Root configuration
    std::unordered_map<std::string, TableMapping> table_mappings_; ///< Table mappings
    DatabaseConfig source_db_;                     ///< Source database config
    DatabaseConfig target_db_;                     ///< Target database config
    YAML::Node vocabulary_mappings_;               ///< Vocabulary mappings
    YAML::Node etl_settings_;                      ///< ETL settings
};

/**
 * @brief Singleton accessor for global configuration
 * 
 * @section description Description
 * Provides global access to the configuration manager instance.
 * This follows the singleton pattern to ensure consistent configuration
 * across the entire application.
 * 
 * @section features Features
 * - Singleton pattern
 * - Thread-safe access
 * - Global configuration
 * - Lazy initialization
 */
class Config {
public:
    /**
     * @brief Get configuration instance
     * @return ConfigurationManager& Configuration manager instance
     * 
     * @section details Implementation Details
     * - Returns singleton instance
     * - Thread-safe
     * - Lazy initialization
     */
    static ConfigurationManager& get() {
        static ConfigurationManager instance;
        return instance;
    }

private:
    Config() = delete;
    ~Config() = delete;
    Config(const Config&) = delete;
    Config& operator=(const Config&) = delete;
};

} // namespace omop::common