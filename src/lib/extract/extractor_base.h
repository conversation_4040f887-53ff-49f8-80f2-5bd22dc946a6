/**
 * @file extractor_base.h
 * @brief Base class for data extractors in the OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 * @version 1.0
 * 
 * @section overview Overview
 * This file contains the abstract base class for all data extractors,
 * defining the interface for extracting data from various sources. It
 * provides a common foundation for implementing specific data source
 * extractors with shared functionality.
 * 
 * @section design Design Principles
 * - Extensibility: Easy to add new extractor types
 * - Consistency: Common interface for all extractors
 * - Error Handling: Robust error management
 * - Progress Tracking: Real-time extraction monitoring
 * - Resource Management: Proper cleanup and initialization
 * 
 * @section components Components
 * - ExtractionStats: Statistics tracking structure
 * - ExtractionOptions: Configuration options
 * - SourceSchema: Data source schema definition
 * - ExtractorBase: Abstract base class
 * 
 * @section usage Usage
 * To implement a new extractor:
 * 1. Inherit from ExtractorBase
 * 2. Implement pure virtual methods
 * 3. Configure extraction options
 * 4. Handle errors appropriately
 * 
 * @section example Example
 * @code
 * class MyExtractor : public ExtractorBase {
 * public:
 *     MyExtractor(const std::string& name,
 *                 std::shared_ptr<ConfigurationManager> config,
 *                 std::shared_ptr<Logger> logger)
 *         : ExtractorBase(name, config, logger) {}
 *     
 *     SourceSchema getSchema() const override {
 *         // Implement schema retrieval
 *     }
 *     
 *     ValidationResult validateSource() override {
 *         // Implement source validation
 *     }
 *     
 * protected:
 *     bool connect() override {
 *         // Implement connection logic
 *     }
 *     
 *     void disconnect() override {
 *         // Implement disconnection logic
 *     }
 *     
 *     std::vector<Record> extractBatchImpl(size_t batch_size) override {
 *         // Implement batch extraction
 *     }
 * };
 * @endcode
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>
#include <any>
#include <optional>

#include "core/interfaces.h"
#include "core/record.h"
#include "common/exceptions.h"
#include "common/logging.h"
#include "common/configuration.h"

namespace omop::extract {

/**
 * @brief Extraction statistics
 * 
 * @section description Description
 * Tracks various metrics and statistics during the extraction process.
 * 
 * @section fields Fields
 * - total_records: Total records processed
 * - successful_records: Successfully extracted records
 * - failed_records: Failed records
 * - skipped_records: Skipped records
 * - extraction_time_seconds: Total extraction time
 * - error_counts: Counts by error type
 */
struct ExtractionStats {
    size_t total_records{0};          ///< Total records processed
    size_t successful_records{0};     ///< Successfully extracted records
    size_t failed_records{0};         ///< Failed records
    size_t skipped_records{0};        ///< Skipped records
    double extraction_time_seconds{0.0}; ///< Total extraction time
    std::unordered_map<std::string, size_t> error_counts; ///< Error type counts
};

/**
 * @brief Extraction options
 * 
 * @section description Description
 * Configures the behavior of the extraction process.
 * 
 * @section fields Fields
 * - batch_size: Records per batch
 * - max_records: Maximum records to extract
 * - skip_records: Records to skip
 * - continue_on_error: Continue on errors
 * - validate_schema: Validate source schema
 * - columns: Specific columns to extract
 * - filter_expression: Filter expression
 * - custom_options: Custom extractor options
 */
struct ExtractionOptions {
    size_t batch_size{10000};         ///< Number of records per batch
    size_t max_records{0};            ///< Maximum records to extract (0 = no limit)
    size_t skip_records{0};           ///< Number of records to skip
    bool continue_on_error{true};     ///< Continue extraction on errors
    bool validate_schema{true};       ///< Validate source schema
    std::vector<std::string> columns; ///< Specific columns to extract (empty = all)
    std::string filter_expression;    ///< Filter expression (source-specific)
    std::unordered_map<std::string, std::any> custom_options; ///< Custom extractor options
};

/**
 * @brief Schema information for a data source
 * 
 * @section description Description
 * Defines the structure and metadata of a data source.
 * 
 * @section fields Fields
 * - source_name: Source identifier
 * - source_type: Source type
 * - columns: Column definitions
 * - primary_keys: Primary key columns
 * - metadata: Additional metadata
 */
struct SourceSchema {
    /**
     * @brief Column information
     * 
     * @section description Description
     * Defines the properties of a data source column.
     * 
     * @section fields Fields
     * - name: Column name
     * - data_type: Data type
     * - nullable: Nullability
     * - max_length: Maximum length
     * - default_value: Default value
     * - description: Column description
     */
    struct Column {
        std::string name;             ///< Column name
        std::string data_type;        ///< Data type
        bool nullable{true};          ///< Whether column is nullable
        std::optional<size_t> max_length; ///< Maximum length for string types
        std::optional<std::string> default_value; ///< Default value
        std::string description;      ///< Column description
    };

    std::string source_name;          ///< Source name/identifier
    std::string source_type;          ///< Source type (table, file, etc.)
    std::vector<Column> columns;      ///< Column definitions
    std::vector<std::string> primary_keys; ///< Primary key columns
    std::unordered_map<std::string, std::string> metadata; ///< Additional metadata
};

/**
 * @brief Abstract base class for data extractors
 * 
 * @section description Description
 * This class defines the interface that all concrete extractors must implement.
 * It provides common functionality for batch processing, error handling, and
 * progress tracking.
 * 
 * @section features Features
 * - Batch processing
 * - Error handling
 * - Progress tracking
 * - Schema validation
 * - Resource management
 * - Statistics collection
 */
class ExtractorBase : public core::IExtractor {
public:
    /**
     * @brief Constructor
     * @param name Extractor name
     * @param config Configuration object
     * @param logger Logger instance
     * 
     * @section details Implementation Details
     * - Initializes base class
     * - Sets up configuration
     * - Configures logging
     * - Thread-safe
     */
    ExtractorBase(const std::string& name,
                  std::shared_ptr<common::ConfigurationManager> config,
                  std::shared_ptr<common::Logger> logger);

    /**
     * @brief Virtual destructor
     * 
     * @section details Implementation Details
     * - Ensures proper cleanup
     * - Thread-safe
     */
    virtual ~ExtractorBase() = default;

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Validates configuration
     * - Sets up extractor
     * - Thread-safe
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return RecordBatch Extracted records (empty if no more data)
     * 
     * @section details Implementation Details
     * - Manages batch extraction
     * - Handles errors
     * - Updates statistics
     * - Thread-safe
     */
    core::RecordBatch extract_batch(size_t batch_size, core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     * 
     * @section details Implementation Details
     * - Checks state
     * - Thread-safe
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     * 
     * @section details Implementation Details
     * - Returns type identifier
     * - Thread-safe
     */
    std::string get_type() const override;

    /**
     * @brief Finalize extraction and clean up resources
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Cleans up resources
     * - Finalizes statistics
     * - Thread-safe
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     * 
     * @section details Implementation Details
     * - Collects statistics
     * - Thread-safe
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

    /**
     * @brief Get the schema of the data source
     * @return Source schema
     * 
     * @section details Implementation Details
     * - Pure virtual method
     * - Must be implemented
     * - Thread-safe
     */
    virtual SourceSchema getSchema() const = 0;

    /**
     * @brief Validate the data source
     * @return Validation result
     * 
     * @section details Implementation Details
     * - Pure virtual method
     * - Must be implemented
     * - Thread-safe
     */
    virtual core::ValidationResult validateSource() = 0;

    /**
     * @brief Get extraction statistics (internal)
     * @return Extraction statistics
     * 
     * @section details Implementation Details
     * - Returns internal stats
     * - Thread-safe
     */
    ExtractionStats getStatistics() const { return stats_; }

    /**
     * @brief Reset the extractor to initial state
     * 
     * @section details Implementation Details
     * - Resets state
     * - Clears statistics
     * - Thread-safe
     */
    virtual void reset();

    /**
     * @brief Close the extractor and release resources
     * 
     * @section details Implementation Details
     * - Releases resources
     * - Disconnects if needed
     * - Thread-safe
     */
    virtual void close();

    /**
     * @brief Set progress callback
     * @param callback Progress callback function
     * 
     * @section details Implementation Details
     * - Sets callback
     * - Thread-safe
     */
    void setProgressCallback(std::function<void(size_t, size_t)> callback) {
        progress_callback_ = callback;
    }

    /**
     * @brief Get extractor name
     * @return Extractor name
     * 
     * @section details Implementation Details
     * - Returns name
     * - Thread-safe
     */
    const std::string& getName() const { return name_; }

protected:
    /**
     * @brief Connect to the data source
     * @return true if connection successful
     * 
     * @section details Implementation Details
     * - Pure virtual method
     * - Must be implemented
     * - Thread-safe
     */
    virtual bool connect() = 0;

    /**
     * @brief Disconnect from the data source
     * 
     * @section details Implementation Details
     * - Pure virtual method
     * - Must be implemented
     * - Thread-safe
     */
    virtual void disconnect() = 0;

    /**
     * @brief Extract a single batch of records (implementation)
     * @param batch_size Size of batch to extract
     * @return Vector of records
     * 
     * @section details Implementation Details
     * - Pure virtual method
     * - Must be implemented
     * - Thread-safe
     */
    virtual std::vector<core::Record> extractBatchImpl(size_t batch_size) = 0;

    /**
     * @brief Convert source data to Record format
     * @param source_data Source data in native format
     * @return Converted record
     * 
     * @section details Implementation Details
     * - Pure virtual method
     * - Must be implemented
     * - Thread-safe
     */
    virtual core::Record convertToRecord(const std::any& source_data) = 0;

    /**
     * @brief Handle extraction error
     * @param error Error message
     * @param record_context Record context (if applicable)
     * 
     * @section details Implementation Details
     * - Logs error
     * - Updates statistics
     * - Thread-safe
     */
    void handleError(const std::string& error,
                    const std::optional<std::any>& record_context = std::nullopt);

    /**
     * @brief Update progress
     * @param current Current record count
     * @param total Total record count (0 if unknown)
     * 
     * @section details Implementation Details
     * - Calls callback
     * - Thread-safe
     */
    void updateProgress(size_t current, size_t total = 0);

    /**
     * @brief Apply filter to record
     * @param record Record to filter
     * @return true if record passes filter
     * 
     * @section details Implementation Details
     * - Applies filter
     * - Thread-safe
     */
    virtual bool applyFilter(const core::Record& record);

    /**
     * @brief Apply column selection to record
     * @param record Record to process
     * @return Record with selected columns
     * 
     * @section details Implementation Details
     * - Selects columns
     * - Thread-safe
     */
    virtual core::Record selectColumns(const core::Record& record);

protected:
    std::string name_;                              ///< Extractor name
    std::shared_ptr<common::ConfigurationManager> config_; ///< Configuration
    std::shared_ptr<common::Logger> logger_;        ///< Logger
    ExtractionStats stats_;                         ///< Extraction statistics
    ExtractionOptions options_;                     ///< Current extraction options
    bool is_connected_{false};                      ///< Connection status
    bool is_initialized_{false};                    ///< Initialization status
    size_t current_position_{0};                    ///< Current position in data source
    std::function<void(size_t, size_t)> progress_callback_; ///< Progress callback

private:
    /**
     * @brief Register extractor type
     * @param type Extractor type
     * @param creator Creator function
     * 
     * @section details Implementation Details
     * - Registers type
     * - Thread-safe
     */
    static void registerExtractorType(
        const std::string& type,
        std::function<std::unique_ptr<ExtractorBase>(
            const std::string&,
            std::shared_ptr<common::ConfigurationManager>,
            std::shared_ptr<common::Logger>)> creator);

    /**
     * @brief Create extractor instance
     * @param type Extractor type
     * @param name Extractor name
     * @param config Configuration
     * @param logger Logger
     * @return Extractor instance
     * 
     * @section details Implementation Details
     * - Creates extractor
     * - Thread-safe
     */
    static std::unique_ptr<ExtractorBase> createExtractor(
        const std::string& type,
        const std::string& name,
        std::shared_ptr<common::ConfigurationManager> config,
        std::shared_ptr<common::Logger> logger);

    /**
     * @brief Get registered extractor types
     * @return Vector of type names
     * 
     * @section details Implementation Details
     * - Returns types
     * - Thread-safe
     */
    static std::vector<std::string> getRegisteredTypes();
};

} // namespace omop::extract