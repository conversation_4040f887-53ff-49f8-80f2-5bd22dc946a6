/**
 * @file odbc_connector.h
 * @brief ODBC database connector interface
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file provides the ODBC implementation of the database connector
 * interface, supporting various databases through ODBC drivers.
 */

#pragma once

#include "extract/database_connector.h"
#include <sql.h>
#include <sqlext.h>
#include <memory>
#include <mutex>
#include <atomic>

namespace omop::extract {

/**
 * @brief ODBC error information
 */
struct OdbcError {
    std::string state;       ///< SQL state
    SQLINTEGER native_error; ///< Native error code
    std::string message;     ///< Error message
};

/**
 * @brief ODBC handle wrapper with RAII
 */
template<typename HandleType>
class OdbcHandle {
public:
    /**
     * @brief Constructor
     * @param handle_type SQL handle type
     * @param parent_handle Parent handle (if applicable)
     */
    OdbcHandle(SQLSMALLINT handle_type, SQLHANDLE parent_handle = SQL_NULL_HANDLE)
        : handle_type_(handle_type) {
        
        SQLRETURN ret = SQLAllocHandle(handle_type_, parent_handle, &handle_);
        if (!SQL_SUCCEEDED(ret)) {
            throw common::DatabaseException("Failed to allocate ODBC handle", "ODBC", ret);
        }
    }

    /**
     * @brief Destructor
     */
    ~OdbcHandle() {
        if (handle_ != SQL_NULL_HANDLE) {
            SQLFreeHandle(handle_type_, handle_);
        }
    }

    // Delete copy operations
    OdbcHandle(const OdbcHandle&) = delete;
    OdbcHandle& operator=(const OdbcHandle&) = delete;

    /**
     * @brief Move constructor
     */
    OdbcHandle(OdbcHandle&& other) noexcept
        : handle_(other.handle_), handle_type_(other.handle_type_) {
        other.handle_ = SQL_NULL_HANDLE;
    }

    /**
     * @brief Move assignment operator
     */
    OdbcHandle& operator=(OdbcHandle&& other) noexcept {
        if (this != &other) {
            if (handle_ != SQL_NULL_HANDLE) {
                SQLFreeHandle(handle_type_, handle_);
            }
            handle_ = other.handle_;
            handle_type_ = other.handle_type_;
            other.handle_ = SQL_NULL_HANDLE;
        }
        return *this;
    }

    /**
     * @brief Get raw handle
     * @return SQLHANDLE Raw ODBC handle
     */
    SQLHANDLE get() const { return handle_; }

    /**
     * @brief Implicit conversion to raw handle
     * @return SQLHANDLE Raw ODBC handle
     */
    operator SQLHANDLE() const { return handle_; }

private:
    SQLHANDLE handle_{SQL_NULL_HANDLE};
    SQLSMALLINT handle_type_;
};

using OdbcEnvironment = OdbcHandle<SQLHENV>;
using OdbcConnection = OdbcHandle<SQLHDBC>;
using OdbcStatement = OdbcHandle<SQLHSTMT>;

/**
 * @brief ODBC result set implementation
 *
 * This class provides access to ODBC query results through the
 * IResultSet interface, handling type conversions and NULL values.
 */
class OdbcResultSet : public ResultSetBase {
public:
    /**
     * @brief Constructor
     * @param statement ODBC statement handle
     */
    explicit OdbcResultSet(std::shared_ptr<OdbcStatement> statement);

    /**
     * @brief Destructor
     */
    ~OdbcResultSet() override;

    /**
     * @brief Move to next row
     * @return bool True if successful
     */
    bool next() override;

    /**
     * @brief Get column value by index
     * @param index Column index (0-based)
     * @return std::any Column value
     */
    std::any get_value(size_t index) const override;

    /**
     * @brief Get column value by name
     * @param column_name Column name
     * @return std::any Column value
     */
    std::any get_value(const std::string& column_name) const override;

    /**
     * @brief Check if column value is NULL
     * @param index Column index
     * @return bool True if NULL
     */
    bool is_null(size_t index) const override;

    /**
     * @brief Check if column value is NULL
     * @param column_name Column name
     * @return bool True if NULL
     */
    bool is_null(const std::string& column_name) const override;

    /**
     * @brief Get column count
     * @return size_t Number of columns
     */
    size_t column_count() const override;

    /**
     * @brief Get column name by index
     * @param index Column index
     * @return std::string Column name
     */
    std::string column_name(size_t index) const override;

    /**
     * @brief Get column type by index
     * @param index Column index
     * @return std::string Column type name
     */
    std::string column_type(size_t index) const override;

private:
    /**
     * @brief Column metadata
     */
    struct ColumnInfo {
        std::string name;
        SQLSMALLINT sql_type;
        SQLULEN size;
        SQLSMALLINT decimal_digits;
        SQLSMALLINT nullable;
    };

    /**
     * @brief Get column index by name
     * @param column_name Column name
     * @return size_t Column index
     * @throws DatabaseException if column not found
     */
    size_t get_column_index(const std::string& column_name) const;

    /**
     * @brief Convert ODBC value to appropriate type
     * @param index Column index
     * @return std::any Converted value
     */
    std::any convert_value(size_t index) const;

    /**
     * @brief Load column metadata
     */
    void load_metadata();

    std::shared_ptr<OdbcStatement> statement_;
    std::vector<ColumnInfo> columns_;
    mutable std::unordered_map<std::string, size_t> column_index_cache_;
    bool metadata_loaded_{false};
    mutable std::vector<SQLLEN> indicators_;  // For NULL checking
};

/**
 * @brief ODBC prepared statement implementation
 *
 * This class implements prepared statements for ODBC,
 * providing parameterized query execution across different databases.
 */
class OdbcPreparedStatement : public IPreparedStatement {
public:
    /**
     * @brief Constructor
     * @param connection ODBC connection handle
     * @param sql SQL query
     */
    OdbcPreparedStatement(std::shared_ptr<OdbcConnection> connection,
                         const std::string& sql);

    /**
     * @brief Destructor
     */
    ~OdbcPreparedStatement() override;

    /**
     * @brief Bind parameter by index
     * @param index Parameter index (1-based)
     * @param value Parameter value
     */
    void bind(size_t index, const std::any& value) override;

    /**
     * @brief Execute query and return result set
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query() override;

    /**
     * @brief Execute update/insert/delete
     * @return size_t Number of affected rows
     */
    size_t execute_update() override;

    /**
     * @brief Clear all bound parameters
     */
    void clear_parameters() override;

private:
    /**
     * @brief Parameter binding information
     */
    struct ParameterBinding {
        std::any value;
        SQLSMALLINT c_type;
        SQLSMALLINT sql_type;
        std::vector<char> buffer;
        SQLLEN indicator;
    };

    /**
     * @brief Bind parameter to statement
     * @param index Parameter index
     * @param binding Parameter binding info
     */
    void bind_parameter(size_t index, ParameterBinding& binding);

    std::shared_ptr<OdbcConnection> connection_;
    std::shared_ptr<OdbcStatement> statement_;
    std::string sql_;
    std::unordered_map<size_t, ParameterBinding> parameters_;
};

/**
 * @brief ODBC database connection implementation
 *
 * This class provides the concrete implementation of IDatabaseConnection
 * for ODBC databases, supporting various database systems through ODBC drivers.
 */
class OdbcDatabaseConnection : public IDatabaseConnection {
public:
    /**
     * @brief Constructor
     */
    OdbcDatabaseConnection();

    /**
     * @brief Destructor
     */
    ~OdbcDatabaseConnection() override;

    /**
     * @brief Connect to database
     * @param params Connection parameters
     */
    void connect(const ConnectionParams& params) override;

    /**
     * @brief Disconnect from database
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    bool is_connected() const override;

    /**
     * @brief Execute SQL query
     * @param sql SQL query string
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query(const std::string& sql) override;

    /**
     * @brief Execute SQL update/insert/delete
     * @param sql SQL statement
     * @return size_t Number of affected rows
     */
    size_t execute_update(const std::string& sql) override;

    /**
     * @brief Prepare SQL statement
     * @param sql SQL statement with parameter placeholders
     * @return std::unique_ptr<IPreparedStatement> Prepared statement
     */
    std::unique_ptr<IPreparedStatement> prepare_statement(const std::string& sql) override;

    /**
     * @brief Begin transaction
     */
    void begin_transaction() override;

    /**
     * @brief Commit transaction
     */
    void commit() override;

    /**
     * @brief Rollback transaction
     */
    void rollback() override;

    /**
     * @brief Get database type name
     * @return std::string Database type
     */
    std::string get_database_type() const override;

    /**
     * @brief Get database version
     * @return std::string Database version string
     */
    std::string get_version() const override;

    /**
     * @brief Set query timeout
     * @param seconds Timeout in seconds
     */
    void set_query_timeout(int seconds) override;

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return bool True if table exists
     */
    bool table_exists(const std::string& table_name,
                     const std::string& schema = "") const override;

    /**
     * @brief Get ODBC error information
     * @param handle_type Handle type
     * @param handle ODBC handle
     * @return std::vector<OdbcError> Error information
     */
    static std::vector<OdbcError> get_odbc_errors(SQLSMALLINT handle_type,
                                                  SQLHANDLE handle);

private:
    /**
     * @brief Build connection string from parameters
     * @param params Connection parameters
     * @return std::string ODBC connection string
     */
    std::string build_connection_string(const ConnectionParams& params) const;

    /**
     * @brief Build DSN connection string
     * @param params Connection parameters
     * @return std::string DSN connection string
     */
    std::string build_dsn_string(const ConnectionParams& params) const;

    /**
     * @brief Check for ODBC errors and throw if needed
     * @param ret ODBC return code
     * @param operation Operation description
     * @param handle_type Handle type
     * @param handle ODBC handle
     */
    void check_error(SQLRETURN ret, const std::string& operation,
                    SQLSMALLINT handle_type, SQLHANDLE handle) const;

public:
    /**
     * @brief Get SQL type name
     * @param sql_type SQL type code
     * @return std::string Type name
     */
    static std::string get_sql_type_name(SQLSMALLINT sql_type);

private:

    std::shared_ptr<OdbcEnvironment> environment_;
    std::shared_ptr<OdbcConnection> connection_;
    bool connected_{false};
    bool in_transaction_{false};
    std::string database_name_;
    std::string driver_name_;
    mutable std::mutex connection_mutex_;
};

/**
 * @brief ODBC-specific database extractor
 *
 * This class extends DatabaseExtractor with ODBC-specific optimizations
 * for different database systems.
 */
class OdbcExtractor : public DatabaseExtractor {
public:
    /**
     * @brief Constructor
     * @param connection ODBC connection
     */
    explicit OdbcExtractor(std::unique_ptr<IDatabaseConnection> connection)
        : DatabaseExtractor(std::move(connection)) {}

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "odbc"; }

protected:
    /**
     * @brief Build extraction query with ODBC-specific optimizations
     * @return std::string SQL query
     */
    std::string build_query() const override;
};

/**
 * @brief ODBC driver manager
 *
 * Manages ODBC driver discovery and configuration
 */
class OdbcDriverManager {
public:
    /**
     * @brief Driver information
     */
    struct DriverInfo {
        std::string name;
        std::string description;
        std::unordered_map<std::string, std::string> attributes;
    };

    /**
     * @brief Data source information
     */
    struct DataSourceInfo {
        std::string name;
        std::string description;
        std::string driver;
    };

    /**
     * @brief Get available ODBC drivers
     * @return std::vector<DriverInfo> Available drivers
     */
    static std::vector<DriverInfo> get_available_drivers();

    /**
     * @brief Get configured data sources
     * @return std::vector<DataSourceInfo> Data sources
     */
    static std::vector<DataSourceInfo> get_data_sources();

    /**
     * @brief Test ODBC connection
     * @param connection_string Connection string
     * @return std::pair<bool, std::string> Success flag and message
     */
    static std::pair<bool, std::string> test_connection(
        const std::string& connection_string);
};

} // namespace omop::extract