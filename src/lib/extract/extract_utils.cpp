/**
 * @file extract_utils.cpp
 * @brief Implementation of utility functions for the extract module
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "extract.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include <filesystem>
#include <regex>
#include <thread>
#include <future>
#include <queue>

namespace omop::extract {

// Utility function implementations

std::unique_ptr<core::IExtractor> create_extractor_auto(
    const std::string& source_path,
    const std::unordered_map<std::string, std::any>& config) {
    
    auto logger = common::Logger::get("omop-extractor-auto");
    logger->info("Auto-detecting extractor type for: {}", source_path);
    
    std::string type = detect_source_type(source_path);
    
    if (type.empty()) {
        throw common::ConfigurationException(
            std::format("Could not determine extractor type for: '{}'", source_path));
    }
    
    logger->info("Detected extractor type: {}", type);
    
    // Create configuration with source path
    auto full_config = config;
    if (type == "csv" || type == "compressed_csv" || type == "json" || type == "jsonl") {
        full_config["filepath"] = source_path;
    } else if (type == "csv_directory") {
        full_config["directory"] = source_path;
    }
    
    return create_extractor(type, full_config);
}

std::string detect_source_type(const std::string& source_path) {
    // Check if it's a file or directory
    if (std::filesystem::exists(source_path)) {
        if (std::filesystem::is_directory(source_path)) {
            // Check if directory contains CSV files
            for (const auto& entry : std::filesystem::directory_iterator(source_path)) {
                if (entry.is_regular_file()) {
                    std::string filename = entry.path().filename().string();
                    std::transform(filename.begin(), filename.end(), filename.begin(), ::tolower);
                    if (filename.find(".csv") != std::string::npos) {
                        return "csv_directory";
                    }
                }
            }
            return "";
        }
        
        // It's a file - check extension
        std::filesystem::path path(source_path);
        std::string extension = path.extension().string();
        std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
        
        // Check for compressed CSV
        std::string filename = path.filename().string();
        std::transform(filename.begin(), filename.end(), filename.begin(), ::tolower);
        
        if (filename.find(".csv.gz") != std::string::npos || 
            filename.find(".csv.zip") != std::string::npos ||
            filename.find(".csv.bz2") != std::string::npos ||
            filename.find(".csv.xz") != std::string::npos) {
            return "compressed_csv";
        }
        
        // Check regular extensions
        if (extension == ".csv") {
            return "csv";
        } else if (extension == ".json") {
            return "json";
        } else if (extension == ".jsonl" || extension == ".ndjson") {
            return "jsonl";
        } else if (extension == ".gz" || extension == ".zip" || 
                   extension == ".bz2" || extension == ".xz") {
            // Could be compressed CSV without double extension
            return "compressed_csv";
        }
    }
    
    // Check if it's a database connection string
    std::regex db_regex("^(postgresql|postgres|mysql|mariadb|odbc)://", std::regex::icase);
    if (std::regex_search(source_path, db_regex)) {
        // Extract database type from URL
        size_t pos = source_path.find("://");
        if (pos != std::string::npos) {
            std::string type = source_path.substr(0, pos);
            std::transform(type.begin(), type.end(), type.begin(), ::tolower);
            return type;
        }
    }
    
    return "";
}

std::pair<bool, std::string> validate_extractor_config(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config) {
    
    // Get extractor type info
    auto type_info = get_extractor_info();
    
    // Find the type
    auto it = std::find_if(type_info.begin(), type_info.end(),
        [&type](const ExtractorTypeInfo& info) { return info.type == type; });
    
    if (it == type_info.end()) {
        return {false, std::format("Unknown extractor type: '{}'", type)};
    }
    
    // Check required parameters
    for (const auto& param : it->required_params) {
        if (config.find(param) == config.end()) {
            return {false, std::format("Missing required parameter: '{}'", param)};
        }
    }
    
    // Type-specific validation
    if (type == "csv" || type == "compressed_csv" || type == "json" || type == "jsonl") {
        if (config.find("filepath") != config.end()) {
            std::string filepath = std::any_cast<std::string>(config.at("filepath"));
            if (!std::filesystem::exists(filepath)) {
                return {false, std::format("File not found: '{}'", filepath)};
            }
        }
    } else if (type == "csv_directory") {
        if (config.find("directory") != config.end()) {
            std::string directory = std::any_cast<std::string>(config.at("directory"));
            if (!std::filesystem::exists(directory) || !std::filesystem::is_directory(directory)) {
                return {false, std::format("Directory not found: '{}'", directory)};
            }
        }
    }
    
    return {true, ""};
}

// BatchExtractor implementation

BatchExtractor::BatchExtractor(std::unique_ptr<core::IExtractor> extractor)
    : extractor_(std::move(extractor)), config_() {

    if (!extractor_) {
        throw std::runtime_error("BatchExtractor requires a valid extractor");
    }
}

BatchExtractor::BatchExtractor(std::unique_ptr<core::IExtractor> extractor,
                             const Config& config)
    : extractor_(std::move(extractor)), config_(config) {

    if (!extractor_) {
        throw std::runtime_error("BatchExtractor requires a valid extractor");
    }
}

std::vector<core::Record> BatchExtractor::extract_all() {
    std::vector<core::Record> all_records;
    size_t total_extracted = 0;
    
    auto logger = common::Logger::get("omop-batch-extractor");
    logger->info("Starting batch extraction with batch size: {}", config_.batch_size);
    
    try {
        while (extractor_->has_more_data() && 
               (config_.max_records == 0 || total_extracted < config_.max_records)) {
            
            size_t batch_size = config_.batch_size;
            if (config_.max_records > 0) {
                size_t remaining = config_.max_records - total_extracted;
                batch_size = std::min(batch_size, remaining);
            }
            
            auto batch = extractor_->extract_batch(batch_size, context_);
            
            if (batch.empty()) {
                break;
            }
            
            for (const auto& record : batch.getRecords()) {
                all_records.push_back(record);
            }
            
            total_extracted += batch.size();
            
            if (config_.progress_callback) {
                config_.progress_callback(total_extracted, config_.max_records);
            }
        }
    } catch (const std::exception& e) {
        if (config_.error_callback) {
            config_.error_callback(e.what());
        }
        
        if (!config_.continue_on_error) {
            throw;
        }
    }
    
    logger->info("Batch extraction completed: {} records extracted", total_extracted);
    return all_records;
}

size_t BatchExtractor::extract_with_callback(
    std::function<void(const core::RecordBatch&)> processor) {
    
    size_t total_extracted = 0;
    auto logger = common::Logger::get("omop-batch-extractor");
    
    try {
        while (extractor_->has_more_data() && 
               (config_.max_records == 0 || total_extracted < config_.max_records)) {
            
            size_t batch_size = config_.batch_size;
            if (config_.max_records > 0) {
                size_t remaining = config_.max_records - total_extracted;
                batch_size = std::min(batch_size, remaining);
            }
            
            auto batch = extractor_->extract_batch(batch_size, context_);
            
            if (batch.empty()) {
                break;
            }
            
            processor(batch);
            total_extracted += batch.size();
            
            if (config_.progress_callback) {
                config_.progress_callback(total_extracted, config_.max_records);
            }
        }
    } catch (const std::exception& e) {
        if (config_.error_callback) {
            config_.error_callback(e.what());
        }
        
        if (!config_.continue_on_error) {
            throw;
        }
    }
    
    return total_extracted;
}

std::unordered_map<std::string, std::any> BatchExtractor::get_statistics() const {
    auto stats = extractor_->get_statistics();
    stats["batch_size"] = config_.batch_size;
    stats["max_records"] = config_.max_records;
    stats["continue_on_error"] = config_.continue_on_error;
    return stats;
}

// ParallelExtractor implementation

class ParallelExtractor::Impl {
public:
    Impl(const Config& config) : config_(config) {
        // Initialize thread pool
        for (size_t i = 0; i < config.num_threads; ++i) {
            workers_.emplace_back(&Impl::worker_thread, this);
        }
    }
    
    ~Impl() {
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            stop_ = true;
        }
        queue_cv_.notify_all();
        
        for (auto& worker : workers_) {
            if (worker.joinable()) {
                worker.join();
            }
        }
    }
    
    void add_task(std::function<void()> task) {
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            task_queue_.push(std::move(task));
        }
        queue_cv_.notify_one();
    }
    
    void wait_for_completion() {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        completion_cv_.wait(lock, [this] {
            return task_queue_.empty() && active_tasks_ == 0;
        });
    }
    
private:
    void worker_thread() {
        while (true) {
            std::function<void()> task;
            
            {
                std::unique_lock<std::mutex> lock(queue_mutex_);
                queue_cv_.wait(lock, [this] {
                    return stop_ || !task_queue_.empty();
                });
                
                if (stop_ && task_queue_.empty()) {
                    return;
                }
                
                task = std::move(task_queue_.front());
                task_queue_.pop();
                active_tasks_++;
            }
            
            task();
            
            {
                std::unique_lock<std::mutex> lock(queue_mutex_);
                active_tasks_--;
                if (task_queue_.empty() && active_tasks_ == 0) {
                    completion_cv_.notify_all();
                }
            }
        }
    }
    
    Config config_;
    std::vector<std::thread> workers_;
    std::queue<std::function<void()>> task_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    std::condition_variable completion_cv_;
    std::atomic<bool> stop_{false};
    std::atomic<size_t> active_tasks_{0};
};

ParallelExtractor::ParallelExtractor()
    : config_(), impl_(std::make_unique<Impl>(config_)) {
}

ParallelExtractor::ParallelExtractor(const Config& config)
    : config_(config), impl_(std::make_unique<Impl>(config)) {
}

void ParallelExtractor::add_extractor(std::unique_ptr<core::IExtractor> extractor,
                                    const std::string& name) {
    std::string extractor_name = name.empty() ? 
        std::format("extractor_{}", extractors_.size()) : name;
    
    extractors_.emplace_back(extractor_name, std::move(extractor));
}

std::vector<core::Record> ParallelExtractor::extract_all() {
    std::vector<core::Record> all_records;
    std::mutex records_mutex;
    
    auto logger = common::Logger::get("omop-parallel-extractor");
    logger->info("Starting parallel extraction with {} extractors", extractors_.size());
    
    // Create tasks for each extractor
    for (auto& [name, extractor] : extractors_) {
        impl_->add_task([this, &name, &extractor, &all_records, &records_mutex]() {
            try {
                BatchExtractor batch_extractor(std::move(extractor));
                auto records = batch_extractor.extract_all();
                
                std::unique_lock<std::mutex> lock(records_mutex);
                all_records.insert(all_records.end(), records.begin(), records.end());
                
            } catch (const std::exception& e) {
                auto logger = common::Logger::get("omop-parallel-extractor");
                logger->error("Extractor '{}' failed: {}", name, e.what());
            }
        });
    }
    
    impl_->wait_for_completion();
    
    logger->info("Parallel extraction completed: {} total records", all_records.size());
    return all_records;
}

void ParallelExtractor::extract_streaming(
    std::function<void(const core::RecordBatch&, const std::string&)> processor) {
    
    auto logger = common::Logger::get("omop-parallel-extractor");
    logger->info("Starting parallel streaming extraction");
    
    // Create tasks for each extractor
    for (auto& [name, extractor] : extractors_) {
        impl_->add_task([this, &name, &extractor, &processor]() {
            try {
                core::ProcessingContext context;
                
                while (extractor->has_more_data()) {
                    auto batch = extractor->extract_batch(10000, context);
                    if (!batch.empty()) {
                        processor(batch, name);
                    }
                }
                
            } catch (const std::exception& e) {
                auto logger = common::Logger::get("omop-parallel-extractor");
                logger->error("Streaming extractor '{}' failed: {}", name, e.what());
            }
        });
    }
    
    impl_->wait_for_completion();
}

std::unordered_map<std::string, std::unordered_map<std::string, std::any>>
ParallelExtractor::get_all_statistics() const {
    std::unordered_map<std::string, std::unordered_map<std::string, std::any>> stats;
    
    for (const auto& [name, extractor] : extractors_) {
        stats[name] = extractor->get_statistics();
    }
    
    return stats;
}

// Utility namespace implementations

namespace utils {

std::vector<core::Record> extract_csv(const std::string& filepath,
                                    const CsvOptions& options) {
    auto extractor = std::make_unique<CsvExtractor>();
    
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["delimiter"] = options.delimiter;
    config["quote_char"] = options.quote_char;
    config["has_header"] = options.has_header;
    config["encoding"] = options.encoding;
    
    core::ProcessingContext context;
    extractor->initialize(config, context);
    
    BatchExtractor batch_extractor(std::move(extractor));
    return batch_extractor.extract_all();
}

std::vector<core::Record> extract_json(const std::string& filepath,
                                     const JsonOptions& options) {
    auto extractor = std::make_unique<JsonExtractor>();
    
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["root_path"] = options.root_path;
    config["flatten_nested"] = options.flatten_nested;
    config["array_delimiter"] = options.array_delimiter;
    config["parse_dates"] = options.parse_dates;
    
    core::ProcessingContext context;
    extractor->initialize(config, context);
    
    BatchExtractor batch_extractor(std::move(extractor));
    return batch_extractor.extract_all();
}

std::vector<core::Record> extract_table(std::unique_ptr<IDatabaseConnection> connection,
                                      const std::string& table_name,
                                      const std::string& filter) {
    auto extractor = std::make_unique<DatabaseExtractor>(std::move(connection));
    
    std::unordered_map<std::string, std::any> config;
    config["table"] = table_name;
    if (!filter.empty()) {
        config["filter"] = filter;
    }
    
    core::ProcessingContext context;
    extractor->initialize(config, context);
    
    BatchExtractor batch_extractor(std::move(extractor));
    return batch_extractor.extract_all();
}

std::unique_ptr<IDatabaseConnection> create_connection_from_url(const std::string& url) {
    // Parse database URL format: type://username:password@host:port/database
    std::regex url_regex(R"((\w+)://(?:([^:]+):([^@]+)@)?([^:/]+)(?::(\d+))?/(.+))");
    std::smatch matches;
    
    if (!std::regex_match(url, matches, url_regex)) {
        throw common::ConfigurationException(
            std::format("Invalid database URL format: '{}'", url));
    }
    
    std::string type = matches[1].str();
    std::string username = matches[2].str();
    std::string password = matches[3].str();
    std::string host = matches[4].str();
    std::string port_str = matches[5].str();
    std::string database = matches[6].str();
    
    int port = 0;
    if (!port_str.empty()) {
        port = std::stoi(port_str);
    } else {
        // Default ports
        if (type == "postgresql" || type == "postgres") {
            port = 5432;
        } else if (type == "mysql" || type == "mariadb") {
            port = 3306;
        }
    }
    
    IDatabaseConnection::ConnectionParams params;
    params.host = host;
    params.port = port;
    params.database = database;
    params.username = username;
    params.password = password;

    auto connection = DatabaseConnectionFactory::instance().create(type, params);

    return connection;
}

} // namespace utils

} // namespace omop::extract