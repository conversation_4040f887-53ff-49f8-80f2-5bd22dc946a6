/**
 * @file extractor_base.cpp
 * @brief Implementation of base class for data extractors
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "extractor_base.h"
#include <chrono>
#include <algorithm>

namespace omop::extract {

// Static factory registry
std::unordered_map<std::string,
    std::function<std::unique_ptr<ExtractorBase>(
        const std::string&,
        std::shared_ptr<common::ConfigurationManager>,
        std::shared_ptr<common::Logger>)>> ExtractorFactory::creators_;

ExtractorBase::ExtractorBase(const std::string& name,
                           std::shared_ptr<common::ConfigurationManager> config,
                           std::shared_ptr<common::Logger> logger)
    : name_(name), config_(config), logger_(logger) {

    if (!logger_) {
        logger_ = common::Logger::get("ExtractorBase");
    }
}

void ExtractorBase::initialize(const std::unordered_map<std::string, std::any>& config,
                              core::ProcessingContext& context) {
    if (is_initialized_) {
        logger_->warn("Extractor {} already initialized", name_);
        return;
    }

    logger_->info("Initializing extractor: {}", name_);
    start_time_ = std::chrono::steady_clock::now();

    try {
        // Update options from config
        if (auto it = config.find("batch_size"); it != config.end()) {
            options_.batch_size = std::any_cast<size_t>(it->second);
        }
        if (auto it = config.find("max_records"); it != config.end()) {
            options_.max_records = std::any_cast<size_t>(it->second);
        }
        if (auto it = config.find("continue_on_error"); it != config.end()) {
            options_.continue_on_error = std::any_cast<bool>(it->second);
        }
        if (auto it = config.find("validate_schema"); it != config.end()) {
            options_.validate_schema = std::any_cast<bool>(it->second);
        }

        // Connect to data source
        if (!connect()) {
            logger_->error("Failed to connect to data source for extractor: {}", name_);
            throw std::runtime_error("Failed to connect to data source");
        }

        is_connected_ = true;

        // Validate source schema if enabled
        if (options_.validate_schema) {
            auto validation_result = validateSource();
            if (!validation_result.is_valid()) {
                std::string error_msg = "Unknown error";
                if (!validation_result.errors().empty()) {
                    error_msg = validation_result.errors()[0].error_message;
                }
                logger_->error("Source validation failed for extractor {}: {}", name_, error_msg);
                throw std::runtime_error("Source validation failed");
            }
        }

        is_initialized_ = true;
        logger_->info("Extractor {} initialized successfully", name_);

    } catch (const std::exception& e) {
        logger_->error("Exception during extractor initialization: {}", e.what());
        throw;
    }
}

core::RecordBatch ExtractorBase::extract_batch(size_t batch_size, core::ProcessingContext& context) {
    core::RecordBatch batch;

    if (!is_initialized_) {
        logger_->error("Extractor not initialized");
        return batch;
    }

    try {
        // Extract raw batch from source
        auto raw_batch = extractBatchImpl(batch_size);

        // Process each record
        for (const auto& raw_record : raw_batch) {
            try {
                stats_.total_records++;

                // Convert to standard record format
                auto record = convertToRecord(raw_record);

                // Apply column selection if specified
                if (!options_.columns.empty()) {
                    record = selectColumns(record);
                }

                // Apply filter if specified
                if (!options_.filter_expression.empty() && !applyFilter(record)) {
                    stats_.skipped_records++;
                    continue;
                }

                batch.addRecord(std::move(record));
                stats_.successful_records++;

            } catch (const std::exception& e) {
                stats_.failed_records++;
                handleError(std::string("Record processing error: ") + e.what(), raw_record);

                if (!options_.continue_on_error) {
                    throw;
                }
            }
        }

        current_position_ += raw_batch.size();

    } catch (const std::exception& e) {
        logger_->error("Batch extraction error: {}", e.what());
        if (!options_.continue_on_error) {
            throw;
        }
    }

    return batch;
}

bool ExtractorBase::has_more_data() const {
    // Default implementation - derived classes should override
    return is_connected_ && current_position_ < stats_.total_records;
}

std::string ExtractorBase::get_type() const {
    return "base";  // Derived classes should override
}

void ExtractorBase::finalize(core::ProcessingContext& context) {
    logger_->info("Finalizing extractor: {}", name_);

    // Calculate final statistics
    auto end_time = std::chrono::steady_clock::now();
    std::chrono::duration<double> elapsed = end_time - start_time_;
    stats_.extraction_time_seconds = elapsed.count();

    logger_->info("Extraction completed: {} records extracted, {} failed, {:.2f} seconds",
                 stats_.successful_records, stats_.failed_records, stats_.extraction_time_seconds);

    close();
}

std::unordered_map<std::string, std::any> ExtractorBase::get_statistics() const {
    std::unordered_map<std::string, std::any> stats;
    stats["total_records"] = stats_.total_records;
    stats["successful_records"] = stats_.successful_records;
    stats["failed_records"] = stats_.failed_records;
    stats["skipped_records"] = stats_.skipped_records;
    stats["extraction_time_seconds"] = stats_.extraction_time_seconds;
    return stats;
}

void ExtractorBase::reset() {
    logger_->info("Resetting extractor: {}", name_);

    current_position_ = 0;
    stats_ = ExtractionStats();

    // Disconnect and reconnect
    if (is_connected_) {
        disconnect();
        is_connected_ = false;
    }

    if (is_initialized_) {
        connect();
        is_connected_ = true;
    }
}

void ExtractorBase::close() {
    logger_->info("Closing extractor: {}", name_);

    if (is_connected_) {
        disconnect();
        is_connected_ = false;
    }

    is_initialized_ = false;
}

void ExtractorBase::handleError(const std::string& error,
                               const std::optional<std::any>& record_context) {
    logger_->error("Extraction error in {}: {}", name_, error);

    // Count error types
    size_t colon_pos = error.find(':');
    std::string error_type = (colon_pos != std::string::npos)
        ? error.substr(0, colon_pos)
        : "Unknown";

    stats_.error_counts[error_type]++;

    // Log record context if available
    if (record_context.has_value()) {
        logger_->debug("Error context: record at position {}", current_position_);
    }
}

void ExtractorBase::updateProgress(size_t current, size_t total) {
    if (progress_callback_) {
        progress_callback_(current, total);
    }

    // Log progress periodically
    if (current % 10000 == 0 || current == total) {
        if (total > 0) {
            double percent = (static_cast<double>(current) / total) * 100.0;
            logger_->info("Extraction progress: {}/{} ({:.1f}%)", current, total, percent);
        } else {
            logger_->info("Extraction progress: {} records", current);
        }
    }
}

bool ExtractorBase::applyFilter(const core::Record& record) {
    // Default implementation - always pass
    // Derived classes should implement specific filter logic
    return true;
}

core::Record ExtractorBase::selectColumns(const core::Record& record) {
    if (options_.columns.empty()) {
        return record;
    }

    core::Record filtered_record;

    // Copy only selected columns
    for (const auto& column : options_.columns) {
        if (record.hasField(column)) {
            filtered_record.setField(column, record.getField(column));
        }
    }

    // Preserve metadata
    filtered_record.setMetadata(record.getMetadata());

    return filtered_record;
}

// ExtractorFactory implementation
void ExtractorFactory::registerExtractor(
    const std::string& type,
    std::function<std::unique_ptr<ExtractorBase>(
        const std::string&,
        std::shared_ptr<common::ConfigurationManager>,
        std::shared_ptr<common::Logger>)> creator) {

    creators_[type] = creator;
}

std::unique_ptr<ExtractorBase> ExtractorFactory::createExtractor(
    const std::string& type,
    const std::string& name,
    std::shared_ptr<common::ConfigurationManager> config,
    std::shared_ptr<common::Logger> logger) {

    auto it = creators_.find(type);
    if (it == creators_.end()) {
        throw common::ConfigurationException("Unknown extractor type: " + type);
    }

    return it->second(name, config, logger);
}

std::vector<std::string> ExtractorFactory::getRegisteredTypes() {
    std::vector<std::string> types;
    types.reserve(creators_.size());

    for (const auto& [type, _] : creators_) {
        types.push_back(type);
    }

    return types;
}

} // namespace omop::extract