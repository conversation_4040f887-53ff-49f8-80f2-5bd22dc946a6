/**
 * @file database_connector.h
 * @brief Database connectivity and query execution for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header defines the database connectivity layer for the OMOP ETL pipeline,
 * providing interfaces and implementations for database operations. It supports
 * multiple database systems through a unified API.
 * 
 * @section design Design Principles
 * - Database Agnostic: Unified interface for different databases
 * - Resource Management: Connection pooling and cleanup
 * - Error Handling: Robust error management
 * - Performance: Efficient query execution
 * - Thread Safety: Safe concurrent access
 * 
 * @section components Components
 * - IResultSet: Query result access interface
 * - IPreparedStatement: Parameterized query interface
 * - IDatabaseConnection: Database connection interface
 * - ResultSetBase: Common result set functionality
 * - DatabaseExtractor: Database-specific data extraction
 * - ConnectionPool: Connection pooling management
 * 
 * @section usage Usage
 * To use the database connectivity:
 * 1. Create database connection
 * 2. Execute queries or prepare statements
 * 3. Process results
 * 4. Manage transactions
 * 
 * @section example Example
 * @code
 * auto connection = DatabaseConnectionFactory::instance()
 *     .create_connection("postgresql", params);
 * connection->connect();
 * auto result = connection->execute_query("SELECT * FROM patients");
 * while (result->next()) {
 *     auto record = result->to_record();
 *     // Process record
 * }
 * @endcode
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <optional>
#include <any>
#include <functional>
#include <unordered_map>
#include "core/interfaces.h"
#include "common/exceptions.h"

namespace omop::extract {

/**
 * @brief Result set interface for database queries
 * 
 * @section description Description
 * This interface provides access to query results in a database-agnostic manner.
 * Implementations handle the specifics of each database system.
 * 
 * @section features Features
 * - Row navigation
 * - Column access
 * - NULL handling
 * - Type conversion
 * - Record conversion
 */
class IResultSet {
public:
    /**
     * @brief Virtual destructor
     * 
     * @section details Implementation Details
     * - Ensures proper cleanup
     * - Thread-safe
     */
    virtual ~IResultSet() = default;

    /**
     * @brief Move to next row
     * @return bool True if successful, false if no more rows
     * 
     * @section details Implementation Details
     * - Advances cursor
     * - Thread-safe
     */
    virtual bool next() = 0;

    /**
     * @brief Get column value by index
     * @param index Column index (0-based)
     * @return std::any Column value
     * 
     * @section details Implementation Details
     * - Retrieves value
     * - Converts type
     * - Thread-safe
     */
    virtual std::any get_value(size_t index) const = 0;

    /**
     * @brief Get column value by name
     * @param column_name Column name
     * @return std::any Column value
     * 
     * @section details Implementation Details
     * - Looks up column
     * - Retrieves value
     * - Thread-safe
     */
    virtual std::any get_value(const std::string& column_name) const = 0;

    /**
     * @brief Check if column value is NULL
     * @param index Column index
     * @return bool True if NULL
     * 
     * @section details Implementation Details
     * - Checks nullity
     * - Thread-safe
     */
    virtual bool is_null(size_t index) const = 0;

    /**
     * @brief Check if column value is NULL
     * @param column_name Column name
     * @return bool True if NULL
     * 
     * @section details Implementation Details
     * - Looks up column
     * - Checks nullity
     * - Thread-safe
     */
    virtual bool is_null(const std::string& column_name) const = 0;

    /**
     * @brief Get column count
     * @return size_t Number of columns
     * 
     * @section details Implementation Details
     * - Returns count
     * - Thread-safe
     */
    virtual size_t column_count() const = 0;

    /**
     * @brief Get column name by index
     * @param index Column index
     * @return std::string Column name
     * 
     * @section details Implementation Details
     * - Returns name
     * - Thread-safe
     */
    virtual std::string column_name(size_t index) const = 0;

    /**
     * @brief Get column type by index
     * @param index Column index
     * @return std::string Column type name
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    virtual std::string column_type(size_t index) const = 0;

    /**
     * @brief Convert current row to Record
     * @return core::Record Record representation of current row
     * 
     * @section details Implementation Details
     * - Converts row
     * - Handles types
     * - Thread-safe
     */
    virtual core::Record to_record() const = 0;
};

/**
 * @brief Prepared statement interface
 * 
 * @section description Description
 * This interface provides parameterized query execution capabilities
 * for safe and efficient database operations.
 * 
 * @section features Features
 * - Parameter binding
 * - Query execution
 * - Update operations
 * - Parameter clearing
 */
class IPreparedStatement {
public:
    /**
     * @brief Virtual destructor
     * 
     * @section details Implementation Details
     * - Ensures cleanup
     * - Thread-safe
     */
    virtual ~IPreparedStatement() = default;

    /**
     * @brief Bind parameter by index
     * @param index Parameter index (1-based)
     * @param value Parameter value
     * 
     * @section details Implementation Details
     * - Binds value
     * - Converts type
     * - Thread-safe
     */
    virtual void bind(size_t index, const std::any& value) = 0;

    /**
     * @brief Execute query and return result set
     * @return std::unique_ptr<IResultSet> Query results
     * 
     * @section details Implementation Details
     * - Executes query
     * - Returns results
     * - Thread-safe
     */
    virtual std::unique_ptr<IResultSet> execute_query() = 0;

    /**
     * @brief Execute update/insert/delete
     * @return size_t Number of affected rows
     * 
     * @section details Implementation Details
     * - Executes update
     * - Returns count
     * - Thread-safe
     */
    virtual size_t execute_update() = 0;

    /**
     * @brief Clear all bound parameters
     * 
     * @section details Implementation Details
     * - Clears parameters
     * - Thread-safe
     */
    virtual void clear_parameters() = 0;
};

/**
 * @brief Database connection interface
 * 
 * @section description Description
 * This interface defines the contract for database connections,
 * providing a unified API for different database systems.
 * 
 * @section features Features
 * - Connection management
 * - Query execution
 * - Transaction control
 * - Metadata access
 * - Timeout handling
 */
class IDatabaseConnection {
public:
    /**
     * @brief Connection parameters
     * 
     * @section description Description
     * Defines the parameters required for database connection.
     * 
     * @section fields Fields
     * - host: Server hostname
     * - port: Server port
     * - database: Database name
     * - username: User credentials
     * - password: User credentials
     * - options: Additional options
     */
    struct ConnectionParams {
        std::string host;  ///< Server hostname
        int port;         ///< Server port
        std::string database;  ///< Database name
        std::string username;  ///< User credentials
        std::string password;  ///< User credentials
        std::unordered_map<std::string, std::string> options;  ///< Additional options
    };

    /**
     * @brief Virtual destructor
     * 
     * @section details Implementation Details
     * - Ensures cleanup
     * - Thread-safe
     */
    virtual ~IDatabaseConnection() = default;

    /**
     * @brief Connect to database
     * @param params Connection parameters
     * 
     * @section details Implementation Details
     * - Establishes connection
     * - Validates parameters
     * - Thread-safe
     */
    virtual void connect(const ConnectionParams& params) = 0;

    /**
     * @brief Disconnect from database
     * 
     * @section details Implementation Details
     * - Closes connection
     * - Cleans up resources
     * - Thread-safe
     */
    virtual void disconnect() = 0;

    /**
     * @brief Check if connected
     * @return bool True if connected
     * 
     * @section details Implementation Details
     * - Checks state
     * - Thread-safe
     */
    virtual bool is_connected() const = 0;

    /**
     * @brief Execute SQL query
     * @param sql SQL query string
     * @return std::unique_ptr<IResultSet> Query results
     * 
     * @section details Implementation Details
     * - Executes query
     * - Returns results
     * - Thread-safe
     */
    virtual std::unique_ptr<IResultSet> execute_query(const std::string& sql) = 0;

    /**
     * @brief Execute SQL update/insert/delete
     * @param sql SQL statement
     * @return size_t Number of affected rows
     * 
     * @section details Implementation Details
     * - Executes update
     * - Returns count
     * - Thread-safe
     */
    virtual size_t execute_update(const std::string& sql) = 0;

    /**
     * @brief Prepare SQL statement
     * @param sql SQL statement with parameter placeholders
     * @return std::unique_ptr<IPreparedStatement> Prepared statement
     * 
     * @section details Implementation Details
     * - Prepares statement
     * - Returns handle
     * - Thread-safe
     */
    virtual std::unique_ptr<IPreparedStatement> prepare_statement(const std::string& sql) = 0;

    /**
     * @brief Begin transaction
     * 
     * @section details Implementation Details
     * - Starts transaction
     * - Thread-safe
     */
    virtual void begin_transaction() = 0;

    /**
     * @brief Commit transaction
     * 
     * @section details Implementation Details
     * - Commits changes
     * - Thread-safe
     */
    virtual void commit() = 0;

    /**
     * @brief Rollback transaction
     * 
     * @section details Implementation Details
     * - Reverts changes
     * - Thread-safe
     */
    virtual void rollback() = 0;

    /**
     * @brief Get database type name
     * @return std::string Database type
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    virtual std::string get_database_type() const = 0;

    /**
     * @brief Get database version
     * @return std::string Database version string
     * 
     * @section details Implementation Details
     * - Returns version
     * - Thread-safe
     */
    virtual std::string get_version() const = 0;

    /**
     * @brief Set query timeout
     * @param seconds Timeout in seconds
     * 
     * @section details Implementation Details
     * - Sets timeout
     * - Thread-safe
     */
    virtual void set_query_timeout(int seconds) = 0;

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return bool True if table exists
     * 
     * @section details Implementation Details
     * - Checks existence
     * - Thread-safe
     */
    virtual bool table_exists(const std::string& table_name,
                            const std::string& schema = "") const = 0;
};

/**
 * @brief Base implementation of IResultSet with common functionality
 * 
 * @section description Description
 * Provides a base implementation of the IResultSet interface with
 * common functionality for converting rows to records.
 * 
 * @section features Features
 * - Record conversion
 * - NULL handling
 * - Type conversion
 */
class ResultSetBase : public IResultSet {
public:
    /**
     * @brief Convert current row to Record
     * @return core::Record Record representation
     * 
     * @section details Implementation Details
     * - Converts row
     * - Handles NULLs
     * - Thread-safe
     */
    core::Record to_record() const override {
        core::Record record;

        for (size_t i = 0; i < column_count(); ++i) {
            if (!is_null(i)) {
                std::string col_name = column_name(i);
                record.setField(col_name, get_value(i));
            }
        }

        return record;
    }
};

/**
 * @brief Database-specific data extractor
 * 
 * @section description Description
 * Extracts data from database tables using the database connection
 * interface.
 * 
 * @section features Features
 * - Table extraction
 * - Filter support
 * - Batch processing
 * - Error handling
 */
class DatabaseExtractor : public core::IExtractor {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     * 
     * @section details Implementation Details
     * - Takes ownership
     * - Initializes state
     * - Thread-safe
     */
    explicit DatabaseExtractor(std::unique_ptr<IDatabaseConnection> connection);

    /**
     * @brief Initialize extractor
     * @param config Configuration
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Validates config
     * - Sets up state
     * - Thread-safe
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract batch of records
     * @param batch_size Batch size
     * @param context Processing context
     * @return Record batch
     * 
     * @section details Implementation Details
     * - Executes query
     * - Processes results
     * - Thread-safe
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                  core::ProcessingContext& context) override;

    /**
     * @brief Check for more data
     * @return bool True if more data
     * 
     * @section details Implementation Details
     * - Checks state
     * - Thread-safe
     */
    bool has_more_data() const override { return has_more_data_; }

    /**
     * @brief Get extractor type
     * @return Type name
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override;

    /**
     * @brief Finalize extraction
     * @param context Processing context
     *
     * @section details Implementation Details
     * - Cleans up
     * - Thread-safe
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return Statistics map
     *
     * @section details Implementation Details
     * - Returns extraction metrics
     * - Thread-safe
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Build extraction query
     * @return SQL query string
     *
     * @section details Implementation Details
     * - Builds database-specific query
     * - Can be overridden by derived classes
     */
    virtual std::string build_query() const;

    /**
     * @brief Apply filters to query
     * @param base_query Base query
     * @return Modified query
     *
     * @section details Implementation Details
     * - Applies filters
     * - Thread-safe
     */
    virtual std::string apply_filters(const std::string& base_query) const;

private:
    std::unique_ptr<IDatabaseConnection> connection_;  ///< Database connection
    std::string table_name_;  ///< Target table
    std::string schema_name_;  ///< Schema name
    bool has_more_data_{true};  ///< More data flag
    size_t current_offset_{0};  ///< Current offset

    // Additional member variables for extraction state
    std::vector<std::string> columns_;  ///< Column names to extract
    std::string filter_condition_;  ///< WHERE clause filter
    std::string order_by_;  ///< ORDER BY clause
    std::unique_ptr<IResultSet> current_result_set_;  ///< Current result set
    std::chrono::steady_clock::time_point start_time_;  ///< Extraction start time
    size_t total_extracted_{0};  ///< Total records extracted
    size_t batch_count_{0};  ///< Number of batches processed
};

/**
 * @brief Connection pool for database connections
 * 
 * @section description Description
 * Manages a pool of database connections for efficient reuse.
 * 
 * @section features Features
 * - Connection pooling
 * - Resource management
 * - Statistics tracking
 * - Connection validation
 */
class ConnectionPool {
public:
    /**
     * @brief Constructor
     * @param min_connections Minimum pool size
     * @param max_connections Maximum pool size
     * @param connection_factory Factory function for creating connections
     *
     * @section details Implementation Details
     * - Initializes pool
     * - Thread-safe
     */
    ConnectionPool(size_t min_connections, size_t max_connections,
                  std::function<std::unique_ptr<IDatabaseConnection>()> connection_factory);

    /**
     * @brief Virtual destructor
     * 
     * @section details Implementation Details
     * - Cleans up
     * - Thread-safe
     */
    virtual ~ConnectionPool();

    /**
     * @brief Acquire connection
     * @param timeout_ms Timeout in milliseconds
     * @return Connection handle
     *
     * @section details Implementation Details
     * - Gets connection
     * - Thread-safe
     */
    std::unique_ptr<IDatabaseConnection> acquire(int timeout_ms = 5000);

    /**
     * @brief Release connection
     * @param connection Connection to release
     * 
     * @section details Implementation Details
     * - Returns connection
     * - Thread-safe
     */
    void release(std::unique_ptr<IDatabaseConnection> connection);

    /**
     * @brief Pool statistics
     * 
     * @section description Description
     * Tracks pool usage and performance metrics.
     * 
     * @section fields Fields
     * - total_connections: Total connections
     * - active_connections: Active connections
     * - idle_connections: Idle connections
     * - total_acquisitions: Total acquisitions
     * - total_releases: Total releases
     * - wait_count: Wait count
     * - avg_wait_time: Average wait time
     */
    struct PoolStats {
        size_t total_connections;  ///< Total connections
        size_t active_connections;  ///< Active connections
        size_t idle_connections;  ///< Idle connections
        size_t total_acquisitions;  ///< Total acquisitions
        size_t total_releases;  ///< Total releases
        size_t wait_count;  ///< Wait count
        std::chrono::milliseconds avg_wait_time;  ///< Average wait time
    };

    /**
     * @brief Get pool statistics
     * @return Pool statistics
     * 
     * @section details Implementation Details
     * - Collects stats
     * - Thread-safe
     */
    [[nodiscard]] PoolStats get_statistics() const;

    /**
     * @brief Clear idle connections
     * 
     * @section details Implementation Details
     * - Removes idle
     * - Thread-safe
     */
    void clear_idle_connections();

    /**
     * @brief Validate connections
     * @return Number of invalid connections
     * 
     * @section details Implementation Details
     * - Validates connections
     * - Removes invalid
     * - Thread-safe
     */
    size_t validate_connections();

private:
    class Impl;  ///< Implementation details
    std::unique_ptr<Impl> impl_;  ///< Implementation instance
};

/**
 * @brief Factory for database connections
 * 
 * @section description Description
 * Creates database connections for different database types.
 * 
 * @section features Features
 * - Connection creation
 * - Type registration
 * - Singleton access
 */
class DatabaseConnectionFactory {
public:
    /**
     * @brief Creator function type
     */
    using Creator = std::function<std::unique_ptr<IDatabaseConnection>(
        const IDatabaseConnection::ConnectionParams&)>;

    /**
     * @brief Get factory instance
     * @return Factory instance
     *
     * @section details Implementation Details
     * - Returns singleton
     * - Thread-safe
     */
    static DatabaseConnectionFactory& instance() {
        static DatabaseConnectionFactory instance;
        return instance;
    }

    /**
     * @brief Register connection type
     * @param type Connection type
     * @param creator Creator function
     * 
     * @section details Implementation Details
     * - Registers type
     * - Thread-safe
     */
    void register_type(const std::string& type, Creator creator) {
        creators_[type] = std::move(creator);
    }

    /**
     * @brief Create connection by type
     * @param type Database type
     * @param params Connection parameters
     * @return std::unique_ptr<IDatabaseConnection> Database connection
     */
    [[nodiscard]] std::unique_ptr<IDatabaseConnection> create(const std::string& type,
                                                             const IDatabaseConnection::ConnectionParams& params) {
        auto it = creators_.find(type);
        if (it != creators_.end()) {
            return it->second(params);
        }
        throw common::DatabaseException(
            std::format("Unknown database type: '{}'", type), type, 0);
    }

    /**
     * @brief Create connection from configuration
     * @param config Database configuration
     * @return std::unique_ptr<IDatabaseConnection> Database connection
     */
    [[nodiscard]] std::unique_ptr<IDatabaseConnection> create_from_config(
        const std::unordered_map<std::string, std::any>& config);

private:
    std::unordered_map<std::string, Creator> creators_;  ///< Type creators
};

} // namespace omop::extract