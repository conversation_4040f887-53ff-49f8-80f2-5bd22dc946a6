/**
 * @file mysql_connector.h
 * @brief MySQL database connector interface
 * <AUTHOR> ETL Team
 * @date 2024
 *
 * This file provides the MySQL implementation of the database connector
 * interface, supporting MySQL and MariaDB databases.
 */

#pragma once

#include "extract/database_connector.h"
#include <mysql.h>
#include <memory>
#include <mutex>
#include <vector>

namespace omop::extract {

/**
 * @brief RAII wrapper for MySQL statement
 */
class MySQLStatement {
public:
    /**
     * @brief Constructor
     * @param mysql MySQL connection handle
     */
    explicit MySQLStatement(MYSQL* mysql) {
        stmt_ = mysql_stmt_init(mysql);
        if (!stmt_) {
            throw common::DatabaseException("Failed to initialize MySQL statement", "MySQL", 0);
        }
    }

    /**
     * @brief Destructor
     */
    ~MySQLStatement() {
        if (stmt_) {
            mysql_stmt_close(stmt_);
        }
    }

    // Delete copy operations
    MySQLStatement(const MySQLStatement&) = delete;
    MySQLStatement& operator=(const MySQLStatement&) = delete;

    /**
     * @brief Get raw statement handle
     * @return MYSQL_STMT* Statement handle
     */
    MYSQL_STMT* get() { return stmt_; }

private:
    MYSQL_STMT* stmt_;
};

/**
 * @brief MySQL result set implementation
 *
 * This class provides access to MySQL query results through the
 * IResultSet interface, handling type conversions and NULL values.
 */
class MySQLResultSet : public ResultSetBase {
public:
    /**
     * @brief Constructor
     * @param statement MySQL statement
     */
    explicit MySQLResultSet(std::shared_ptr<MySQLStatement> statement);

    /**
     * @brief Destructor
     */
    ~MySQLResultSet() override;

    /**
     * @brief Move to next row
     * @return bool True if successful
     */
    bool next() override;

    /**
     * @brief Get column value by index
     * @param index Column index (0-based)
     * @return std::any Column value
     */
    std::any get_value(size_t index) const override;

    /**
     * @brief Get column value by name
     * @param column_name Column name
     * @return std::any Column value
     */
    std::any get_value(const std::string& column_name) const override;

    /**
     * @brief Check if column value is NULL
     * @param index Column index
     * @return bool True if NULL
     */
    bool is_null(size_t index) const override;

    /**
     * @brief Check if column value is NULL
     * @param column_name Column name
     * @return bool True if NULL
     */
    bool is_null(const std::string& column_name) const override;

    /**
     * @brief Get column count
     * @return size_t Number of columns
     */
    size_t column_count() const override;

    /**
     * @brief Get column name by index
     * @param index Column index
     * @return std::string Column name
     */
    std::string column_name(size_t index) const override;

    /**
     * @brief Get column type by index
     * @param index Column index
     * @return std::string Column type name
     */
    std::string column_type(size_t index) const override;

private:
    /**
     * @brief Column metadata
     */
    struct ColumnInfo {
        std::string name;
        enum_field_types type;
        unsigned long length;
        unsigned int flags;
        unsigned int decimals;
    };

    /**
     * @brief MySQL bind buffer
     */
    struct BindBuffer {
        enum_field_types buffer_type;
        void* buffer;
        unsigned long buffer_length;
        bool is_null_value;
        unsigned long length_value;
        bool error_value;
        bool* is_null;
        unsigned long* length;
        bool* error;
    };

    /**
     * @brief Get column index by name
     * @param column_name Column name
     * @return size_t Column index
     * @throws DatabaseException if column not found
     */
    size_t get_column_index(const std::string& column_name) const;

    /**
     * @brief Convert MySQL value to appropriate type
     * @param index Column index
     * @return std::any Converted value
     */
    std::any convert_value(size_t index) const;

    /**
     * @brief Load column metadata
     */
    void load_metadata();

    std::shared_ptr<MySQLStatement> statement_;
    MYSQL_RES* result_;
    std::vector<ColumnInfo> columns_;
    std::vector<MYSQL_BIND> bind_buffers_;
    mutable std::unordered_map<std::string, size_t> column_index_cache_;
    int current_row_{-1};
    my_ulonglong row_count_{0};
    unsigned int column_count_{0};
};

/**
 * @brief MySQL prepared statement implementation
 *
 * This class implements prepared statements for MySQL,
 * providing parameterized query execution.
 */
class MySQLPreparedStatement : public IPreparedStatement {
public:
    /**
     * @brief Constructor
     * @param mysql MySQL connection
     * @param sql SQL query
     */
    MySQLPreparedStatement(MYSQL* mysql, const std::string& sql);

    /**
     * @brief Destructor
     */
    ~MySQLPreparedStatement() override;

    /**
     * @brief Bind parameter by index
     * @param index Parameter index (1-based)
     * @param value Parameter value
     */
    void bind(size_t index, const std::any& value) override;

    /**
     * @brief Execute query and return result set
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query() override;

    /**
     * @brief Execute update/insert/delete
     * @return size_t Number of affected rows
     */
    size_t execute_update() override;

    /**
     * @brief Clear all bound parameters
     */
    void clear_parameters() override;

private:
    /**
     * @brief Parameter binding information
     */
    struct ParameterBinding {
        std::any value;
        MYSQL_BIND bind;
        std::vector<char> buffer;
        bool is_null;
        unsigned long length;
    };

    /**
     * @brief Bind all parameters
     */
    void bind_parameters();

    /**
     * @brief Set up parameter binding
     * @param binding Parameter binding
     * @param value Parameter value
     */
    void setup_parameter_binding(ParameterBinding& binding, const std::any& value);

    std::shared_ptr<MySQLStatement> statement_;
    std::string sql_;
    std::vector<ParameterBinding> parameters_;
    size_t param_count_{0};
};

/**
 * @brief MySQL database connection implementation
 *
 * This class provides the concrete implementation of IDatabaseConnection
 * for MySQL databases, using the MySQL C API.
 */
class MySQLConnection : public IDatabaseConnection {
public:
    /**
     * @brief Constructor
     */
    MySQLConnection();

    /**
     * @brief Destructor
     */
    ~MySQLConnection() override;

    /**
     * @brief Connect to database
     * @param params Connection parameters
     */
    void connect(const ConnectionParams& params) override;

    /**
     * @brief Disconnect from database
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    bool is_connected() const override;

    /**
     * @brief Execute SQL query
     * @param sql SQL query string
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query(const std::string& sql) override;

    /**
     * @brief Execute SQL update/insert/delete
     * @param sql SQL statement
     * @return size_t Number of affected rows
     */
    size_t execute_update(const std::string& sql) override;

    /**
     * @brief Prepare SQL statement
     * @param sql SQL statement with parameter placeholders
     * @return std::unique_ptr<IPreparedStatement> Prepared statement
     */
    std::unique_ptr<IPreparedStatement> prepare_statement(const std::string& sql) override;

    /**
     * @brief Begin transaction
     */
    void begin_transaction() override;

    /**
     * @brief Commit transaction
     */
    void commit() override;

    /**
     * @brief Rollback transaction
     */
    void rollback() override;

    /**
     * @brief Get database type name
     * @return std::string Database type
     */
    std::string get_database_type() const override { return "MySQL"; }

    /**
     * @brief Get database version
     * @return std::string Database version string
     */
    std::string get_version() const override;

    /**
     * @brief Set query timeout
     * @param seconds Timeout in seconds
     */
    void set_query_timeout(int seconds) override;

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return bool True if table exists
     */
    bool table_exists(const std::string& table_name,
                     const std::string& schema = "") const override;

    /**
     * @brief Get raw MySQL connection handle
     * @return MYSQL* Connection handle (for internal use)
     */
    MYSQL* get_raw_connection() { return mysql_; }

private:
    /**
     * @brief Check for MySQL errors and throw if needed
     * @param operation Operation description
     */
    void check_error(const std::string& operation) const;

    /**
     * @brief Set connection options from parameters
     * @param params Connection parameters
     */
    void set_connection_options(const ConnectionParams& params);

    MYSQL* mysql_;
    bool connected_{false};
    bool in_transaction_{false};
    mutable std::mutex connection_mutex_;
    int query_timeout_{0};
};

/**
 * @brief MySQL-specific database extractor
 *
 * This class extends DatabaseExtractor with MySQL-specific optimizations.
 */
class MySQLExtractor : public DatabaseExtractor {
public:
    /**
     * @brief Constructor
     * @param connection MySQL connection
     */
    explicit MySQLExtractor(std::unique_ptr<IDatabaseConnection> connection)
        : DatabaseExtractor(std::move(connection)) {}

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "mysql"; }

protected:
    /**
     * @brief Build extraction query with MySQL-specific optimizations
     * @return std::string SQL query
     */
    std::string build_query() const override;
};

/**
 * @brief Registration helper for MySQL components
 */
class MySQLRegistrar {
public:
    /**
     * @brief Register all MySQL components
     */
    static void register_components() {
        DatabaseConnectionFactory::instance().register_type(
            "mysql",
            []() { return std::make_unique<MySQLConnection>(); }
        );
        
        DatabaseConnectionFactory::instance().register_type(
            "mariadb",  // Alias for MariaDB
            []() { return std::make_unique<MySQLConnection>(); }
        );
    }

private:
    MySQLRegistrar() = default;
};

} // namespace omop::extract