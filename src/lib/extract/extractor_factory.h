/**
 * @file extractor_factory.h
 * @brief Extractor factory and registry interface
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file provides the factory registry for creating and managing
 * different types of data extractors in the OMOP ETL pipeline.
 */

#pragma once

#include "core/interfaces.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>
#include <mutex>
#include <any>

namespace omop::extract {

/**
 * @brief Central registry for extractor types
 *
 * This class maintains a registry of all available extractor types
 * and provides factory methods for creating instances.
 */
class ExtractorFactoryRegistry {
public:
    /**
     * @brief Register an extractor type
     * @param type Extractor type identifier
     * @param creator Factory function
     */
    static void register_type(const std::string& type,
                            std::function<std::unique_ptr<core::IExtractor>()> creator);

    /**
     * @brief Create an extractor instance
     * @param type Extractor type identifier
     * @return std::unique_ptr<core::IExtractor> Extractor instance
     * @throws ConfigurationException if type is not registered
     */
    static std::unique_ptr<core::IExtractor> create(const std::string& type);

    /**
     * @brief Get list of registered extractor types
     * @return std::vector<std::string> Sorted list of type identifiers
     */
    static std::vector<std::string> get_registered_types();

    /**
     * @brief Check if a type is registered
     * @param type Extractor type identifier
     * @return bool True if type is registered
     */
    static bool is_type_registered(const std::string& type);

    /**
     * @brief Clear all registered types (for testing)
     */
    static void clear();

private:
    static std::unordered_map<std::string,
        std::function<std::unique_ptr<core::IExtractor>()>> creators_;
    static std::mutex mutex_;
};

/**
 * @brief Initialize all built-in extractors
 *
 * This function registers all built-in extractor types with the factory.
 * It is called automatically when creating extractors, but can be called
 * manually to ensure all types are available.
 */
void initialize_extractors();

/**
 * @brief Create and initialize an extractor
 * @param type Extractor type identifier
 * @param config Configuration parameters
 * @return std::unique_ptr<core::IExtractor> Initialized extractor
 * @throws ConfigurationException if type is invalid or initialization fails
 */
std::unique_ptr<core::IExtractor> create_extractor(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config = {});

/**
 * @brief Extractor configuration builder
 *
 * Helper class for building extractor configurations with type safety
 * and validation.
 */
class ExtractorConfigBuilder {
public:
    /**
     * @brief Constructor
     * @param type Extractor type
     */
    explicit ExtractorConfigBuilder(const std::string& type) : type_(type) {}

    /**
     * @brief Set configuration parameter
     * @param key Parameter name
     * @param value Parameter value
     * @return ExtractorConfigBuilder& Builder instance for chaining
     */
    template<typename T>
    ExtractorConfigBuilder& set(const std::string& key, T&& value) {
        config_[key] = std::forward<T>(value);
        return *this;
    }

    /**
     * @brief Set file path
     * @param path File path
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_file(const std::string& path) {
        return set("filepath", path);
    }

    /**
     * @brief Set multiple files
     * @param files File paths
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_files(const std::vector<std::string>& files) {
        return set("files", files);
    }

    /**
     * @brief Set directory path
     * @param path Directory path
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_directory(const std::string& path) {
        return set("directory", path);
    }

    /**
     * @brief Set database connection parameters
     * @param host Database host
     * @param port Database port
     * @param database Database name
     * @param username Username
     * @param password Password
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_database(const std::string& host,
                                         int port,
                                         const std::string& database,
                                         const std::string& username,
                                         const std::string& password) {
        return set("host", host)
              .set("port", port)
              .set("database", database)
              .set("username", username)
              .set("password", password);
    }

    /**
     * @brief Set table name
     * @param table Table name
     * @param schema Schema name (optional)
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_table(const std::string& table,
                                      const std::string& schema = "") {
        set("table", table);
        if (!schema.empty()) {
            set("schema", schema);
        }
        return *this;
    }

    /**
     * @brief Set columns to extract
     * @param columns Column names
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_columns(const std::vector<std::string>& columns) {
        return set("columns", columns);
    }

    /**
     * @brief Set filter condition
     * @param filter SQL WHERE clause or filter expression
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_filter(const std::string& filter) {
        return set("filter", filter);
    }

    /**
     * @brief Build and create extractor
     * @return std::unique_ptr<core::IExtractor> Configured extractor
     */
    std::unique_ptr<core::IExtractor> build() {
        return create_extractor(type_, config_);
    }

    /**
     * @brief Get configuration map
     * @return std::unordered_map<std::string, std::any> Configuration
     */
    const std::unordered_map<std::string, std::any>& get_config() const {
        return config_;
    }

private:
    std::string type_;
    std::unordered_map<std::string, std::any> config_;
};

/**
 * @brief Extractor type information
 */
struct ExtractorTypeInfo {
    std::string type;                              ///< Type identifier
    std::string description;                       ///< Description
    std::vector<std::string> required_params;      ///< Required parameters
    std::vector<std::string> optional_params;      ///< Optional parameters
    std::string example_config;                    ///< Example configuration JSON
};

/**
 * @brief Get information about all extractor types
 * @return std::vector<ExtractorTypeInfo> Type information
 */
std::vector<ExtractorTypeInfo> get_extractor_info();

/**
 * @brief Print extractor type information
 * @param stream Output stream
 */
void print_extractor_info(std::ostream& stream = std::cout);

} // namespace omop::extract