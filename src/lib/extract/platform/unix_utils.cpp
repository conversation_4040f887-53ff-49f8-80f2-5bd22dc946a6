/**
 * @file unix_utils.cpp
 * @brief Unix/Linux-specific utility functions implementation
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#ifndef _WIN32

#include "extract/platform/unix_utils.h"
#include "common/exceptions.h"
#include <cstring>
#include <cerrno>
#include <format>
#include <sys/mman.h>
#include <sys/resource.h>
#ifdef __linux__
#include <sys/sysinfo.h>
#endif
#include <sys/statvfs.h>
#include <fstream>
#include <sstream>
#include <random>
#include <limits.h>
#include <stdlib.h>
#include <time.h>

#ifdef __linux__
#include <sched.h>
#endif

namespace omop::extract::platform {

// MemoryMappedFile implementation

MemoryMappedFile::~MemoryMappedFile() {
    unmap();
}

MemoryMappedFile::MemoryMappedFile(MemoryMappedFile&& other) noexcept
    : data_(other.data_), size_(other.size_), fd_(std::move(other.fd_)) {
    other.data_ = nullptr;
    other.size_ = 0;
}

MemoryMappedFile& MemoryMappedFile::operator=(MemoryMappedFile&& other) noexcept {
    if (this != &other) {
        unmap();
        data_ = other.data_;
        size_ = other.size_;
        fd_ = std::move(other.fd_);
        other.data_ = nullptr;
        other.size_ = 0;
    }
    return *this;
}

bool MemoryMappedFile::map_file(const std::string& filepath, bool read_only) {
    // Clean up any existing mapping
    unmap();
    
    // Open file
    int flags = read_only ? O_RDONLY : O_RDWR;
    fd_ = UnixFileDescriptor(open(filepath.c_str(), flags));
    
    if (!fd_.is_valid()) {
        return false;
    }
    
    // Get file size
    struct stat st;
    if (fstat(fd_.get(), &st) != 0) {
        return false;
    }
    
    size_ = st.st_size;
    if (size_ == 0) {
        return false;
    }
    
    // Map file
    int prot = read_only ? PROT_READ : (PROT_READ | PROT_WRITE);
    data_ = mmap(nullptr, size_, prot, MAP_PRIVATE, fd_.get(), 0);
    
    if (data_ == MAP_FAILED) {
        data_ = nullptr;
        size_ = 0;
        return false;
    }
    
    // Advise kernel about access pattern
    madvise(data_, size_, MADV_SEQUENTIAL);
    
    return true;
}

void MemoryMappedFile::unmap() {
    if (data_ != nullptr) {
        munmap(data_, size_);
        data_ = nullptr;
        size_ = 0;
    }
}

// Utility functions

std::string get_system_error_message(int error_code) {
    if (error_code == 0) {
        error_code = errno;
    }
    
    char buffer[256];
    // Use thread-safe version
    if (strerror_r(error_code, buffer, sizeof(buffer)) == 0) {
        return std::string(buffer);
    }
    
    return std::format("Unknown error code: {}", error_code);
}

size_t get_file_size(const std::string& filepath) {
    struct stat st;
    if (stat(filepath.c_str(), &st) != 0) {
        throw std::runtime_error(
            std::format("Failed to get file size '{}': {}", 
                       filepath, get_system_error_message()));
    }
    
    return static_cast<size_t>(st.st_size);
}

time_t get_file_mtime(const std::string& filepath) {
    struct stat st;
    if (stat(filepath.c_str(), &st) != 0) {
        throw std::runtime_error(
            std::format("Failed to get file mtime '{}': {}",
                       filepath, get_system_error_message()));
    }
    
    return st.st_mtime;
}

bool is_symbolic_link(const std::string& path) {
    struct stat st;
    if (lstat(path.c_str(), &st) != 0) {
        return false;
    }
    
    return S_ISLNK(st.st_mode);
}

std::string resolve_symbolic_link(const std::string& path) {
    char buffer[PATH_MAX];
    ssize_t len = readlink(path.c_str(), buffer, sizeof(buffer) - 1);
    
    if (len < 0) {
        throw std::runtime_error(
            std::format("Failed to resolve symbolic link '{}': {}",
                       path, get_system_error_message()));
    }
    
    buffer[len] = '\0';
    return std::string(buffer);
}

std::string get_real_path(const std::string& path) {
    char* resolved = realpath(path.c_str(), nullptr);
    if (!resolved) {
        throw std::runtime_error(
            std::format("Failed to get real path '{}': {}",
                       path, get_system_error_message()));
    }
    
    std::string result(resolved);
    free(resolved);
    return result;
}

bool is_network_path(const std::string& path) {
    struct statvfs vfs;
    if (statvfs(path.c_str(), &vfs) != 0) {
        return false;
    }
    
    // Check filesystem type
    // This is a simplified check - in practice, you might want to
    // check the filesystem type more thoroughly
    std::string real_path;
    try {
        real_path = get_real_path(path);
    } catch (...) {
        real_path = path;
    }
    
    // Check common network filesystem mount points
    return real_path.find("/mnt/") == 0 || 
           real_path.find("/media/") == 0 ||
           real_path.find("/net/") == 0;
}

std::vector<std::string> get_mounted_filesystems() {
    std::vector<std::string> filesystems;
    
    std::ifstream mounts("/proc/mounts");
    if (!mounts.is_open()) {
        return filesystems;
    }
    
    std::string line;
    while (std::getline(mounts, line)) {
        std::istringstream iss(line);
        std::string device, mount_point, fs_type;
        iss >> device >> mount_point >> fs_type;
        
        if (!mount_point.empty() && mount_point[0] == '/') {
            filesystems.push_back(mount_point);
        }
    }
    
    return filesystems;
}

std::string get_temp_directory() {
    // Try environment variables first
    const char* temp = getenv("TMPDIR");
    if (!temp) temp = getenv("TMP");
    if (!temp) temp = getenv("TEMP");
    if (!temp) temp = "/tmp";
    
    std::string temp_dir(temp);
    if (!temp_dir.empty() && temp_dir.back() != '/') {
        temp_dir += '/';
    }
    
    return temp_dir;
}

std::string create_temp_file(const std::string& prefix, const std::string& extension) {
    std::string temp_dir = get_temp_directory();
    
    // Generate random suffix
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(100000, 999999);
    
    std::string filename = std::format("{}{}{}", prefix, dis(gen), extension);
    std::string full_path = temp_dir + filename;
    
    // Create the file
    int fd = open(full_path.c_str(), O_CREAT | O_EXCL | O_WRONLY, 0600);
    if (fd < 0) {
        // Try again with different random number
        filename = std::format("{}{}{}", prefix, dis(gen), extension);
        full_path = temp_dir + filename;
        
        fd = open(full_path.c_str(), O_CREAT | O_EXCL | O_WRONLY, 0600);
        if (fd < 0) {
            throw std::runtime_error(
                std::format("Failed to create temp file: {}",
                           get_system_error_message()));
        }
    }
    
    close(fd);
    return full_path;
}

bool set_file_permissions(const std::string& filepath, mode_t mode) {
    return chmod(filepath.c_str(), mode) == 0;
}

mode_t get_file_permissions(const std::string& filepath) {
    struct stat st;
    if (stat(filepath.c_str(), &st) != 0) {
        throw std::runtime_error(
            std::format("Failed to get file permissions '{}': {}",
                       filepath, get_system_error_message()));
    }
    
    return st.st_mode & 0777;  // Return only permission bits
}

// UnixHighResTimer implementation

UnixHighResTimer::UnixHighResTimer() {
    reset();
}

void UnixHighResTimer::reset() {
    clock_gettime(CLOCK_MONOTONIC, &start_time_);
}

double UnixHighResTimer::elapsed_seconds() const {
    struct timespec current_time;
    clock_gettime(CLOCK_MONOTONIC, &current_time);
    
    double elapsed = (current_time.tv_sec - start_time_.tv_sec) +
                    (current_time.tv_nsec - start_time_.tv_nsec) / 1e9;
    return elapsed;
}

double UnixHighResTimer::elapsed_milliseconds() const {
    return elapsed_seconds() * 1000.0;
}

// Memory and process functions

MemoryInfo get_memory_info() {
    MemoryInfo info{};
    
#ifdef __linux__
    struct sysinfo si;
    if (sysinfo(&si) == 0) {
        info.total_physical = si.totalram * si.mem_unit;
        info.available_physical = si.freeram * si.mem_unit;
        info.total_swap = si.totalswap * si.mem_unit;
        info.available_swap = si.freeswap * si.mem_unit;
    }
#else
    // For other Unix systems, try to parse from system files or commands
    // This is a simplified implementation
    std::ifstream meminfo("/proc/meminfo");
    if (meminfo.is_open()) {
        std::string line;
        while (std::getline(meminfo, line)) {
            std::istringstream iss(line);
            std::string key;
            size_t value;
            std::string unit;
            
            iss >> key >> value >> unit;
            
            if (key == "MemTotal:") {
                info.total_physical = value * 1024;  // Convert from KB
            } else if (key == "MemAvailable:") {
                info.available_physical = value * 1024;
            } else if (key == "SwapTotal:") {
                info.total_swap = value * 1024;
            } else if (key == "SwapFree:") {
                info.available_swap = value * 1024;
            }
        }
    }
#endif
    
    return info;
}

bool set_process_priority(int priority) {
    return setpriority(PRIO_PROCESS, 0, priority) == 0;
}

int get_process_priority() {
    errno = 0;
    int priority = getpriority(PRIO_PROCESS, 0);
    
    if (priority == -1 && errno != 0) {
        throw std::runtime_error(
            std::format("Failed to get process priority: {}",
                       get_system_error_message()));
    }
    
    return priority;
}

bool lock_memory(void* addr, size_t size) {
    return mlock(addr, size) == 0;
}

bool unlock_memory(void* addr, size_t size) {
    return munlock(addr, size) == 0;
}

bool advise_memory_usage(void* addr, size_t size, int advice) {
    return madvise(addr, size, advice) == 0;
}

size_t get_cpu_count() {
    long count = sysconf(_SC_NPROCESSORS_ONLN);
    return (count > 0) ? static_cast<size_t>(count) : 1;
}

bool set_thread_affinity(const std::vector<int>& cpu_set) {
#ifdef __linux__
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    
    for (int cpu : cpu_set) {
        CPU_SET(cpu, &cpuset);
    }
    
    return pthread_setaffinity_np(pthread_self(), sizeof(cpuset), &cpuset) == 0;
#else
    // Not supported on non-Linux Unix systems
    return false;
#endif
}

} // namespace omop::extract::platform

#endif // !_WIN32