/**
 * @file unix_utils.h
 * @brief Unix/Linux-specific utility functions for the extract module
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains Unix/Linux-specific implementations for file handling,
 * path operations, and system interactions.
 */

#pragma once

#ifndef _WIN32

#include <string>
#include <vector>
#include <memory>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>

namespace omop::extract::platform {

/**
 * @brief Unix file descriptor wrapper with RAII
 */
class UnixFileDescriptor {
public:
    /**
     * @brief Constructor
     * @param fd File descriptor
     */
    explicit UnixFileDescriptor(int fd = -1) : fd_(fd) {}
    
    /**
     * @brief Destructor
     */
    ~UnixFileDescriptor() {
        if (fd_ >= 0) {
            close(fd_);
        }
    }
    
    // Delete copy operations
    UnixFileDescriptor(const UnixFileDescriptor&) = delete;
    UnixFileDescriptor& operator=(const UnixFileDescriptor&) = delete;
    
    // Move operations
    UnixFileDescriptor(UnixFileDescriptor&& other) noexcept : fd_(other.fd_) {
        other.fd_ = -1;
    }
    
    UnixFileDescriptor& operator=(UnixFileDescriptor&& other) noexcept {
        if (this != &other) {
            if (fd_ >= 0) {
                close(fd_);
            }
            fd_ = other.fd_;
            other.fd_ = -1;
        }
        return *this;
    }
    
    /**
     * @brief Get raw file descriptor
     * @return int File descriptor
     */
    int get() const { return fd_; }
    
    /**
     * @brief Check if descriptor is valid
     * @return bool True if valid
     */
    bool is_valid() const { return fd_ >= 0; }
    
private:
    int fd_;
};

/**
 * @brief Memory mapped file wrapper
 */
class MemoryMappedFile {
public:
    /**
     * @brief Constructor
     */
    MemoryMappedFile() = default;
    
    /**
     * @brief Destructor
     */
    ~MemoryMappedFile();
    
    // Delete copy operations
    MemoryMappedFile(const MemoryMappedFile&) = delete;
    MemoryMappedFile& operator=(const MemoryMappedFile&) = delete;
    
    // Move operations
    MemoryMappedFile(MemoryMappedFile&& other) noexcept;
    MemoryMappedFile& operator=(MemoryMappedFile&& other) noexcept;
    
    /**
     * @brief Map file into memory
     * @param filepath File path
     * @param read_only Map as read-only
     * @return bool True if successful
     */
    bool map_file(const std::string& filepath, bool read_only = true);
    
    /**
     * @brief Unmap file from memory
     */
    void unmap();
    
    /**
     * @brief Get mapped memory pointer
     * @return void* Memory pointer
     */
    void* data() const { return data_; }
    
    /**
     * @brief Get mapped size
     * @return size_t Mapped size
     */
    size_t size() const { return size_; }
    
    /**
     * @brief Check if file is mapped
     * @return bool True if mapped
     */
    bool is_mapped() const { return data_ != nullptr; }
    
private:
    void* data_{nullptr};
    size_t size_{0};
    UnixFileDescriptor fd_;
};

/**
 * @brief Get system error message
 * @param error_code Error code (default = errno)
 * @return std::string Error message
 */
std::string get_system_error_message(int error_code = 0);

/**
 * @brief Get file size
 * @param filepath File path
 * @return size_t File size in bytes
 */
size_t get_file_size(const std::string& filepath);

/**
 * @brief Get file modification time
 * @param filepath File path
 * @return time_t Modification time
 */
time_t get_file_mtime(const std::string& filepath);

/**
 * @brief Check if path is a symbolic link
 * @param path File path
 * @return bool True if symbolic link
 */
bool is_symbolic_link(const std::string& path);

/**
 * @brief Resolve symbolic link
 * @param path Symbolic link path
 * @return std::string Resolved path
 */
std::string resolve_symbolic_link(const std::string& path);

/**
 * @brief Get real path (resolving all symbolic links)
 * @param path File path
 * @return std::string Real path
 */
std::string get_real_path(const std::string& path);

/**
 * @brief Check if path is on a network filesystem
 * @param path File path
 * @return bool True if on network filesystem
 */
bool is_network_path(const std::string& path);

/**
 * @brief Get mounted filesystems
 * @return std::vector<std::string> Mounted filesystem paths
 */
std::vector<std::string> get_mounted_filesystems();

/**
 * @brief Get temporary directory path
 * @return std::string Temporary directory path
 */
std::string get_temp_directory();

/**
 * @brief Create unique temporary file
 * @param prefix File name prefix
 * @param extension File extension
 * @return std::string Temporary file path
 */
std::string create_temp_file(const std::string& prefix = "omop_", 
                           const std::string& extension = ".tmp");

/**
 * @brief Set file permissions
 * @param filepath File path
 * @param mode Permission mode (e.g., 0644)
 * @return bool True if successful
 */
bool set_file_permissions(const std::string& filepath, mode_t mode);

/**
 * @brief Get file permissions
 * @param filepath File path
 * @return mode_t Permission mode
 */
mode_t get_file_permissions(const std::string& filepath);

/**
 * @brief High-resolution timer using clock_gettime
 */
class UnixHighResTimer {
public:
    /**
     * @brief Constructor - starts timer
     */
    UnixHighResTimer();
    
    /**
     * @brief Reset timer
     */
    void reset();
    
    /**
     * @brief Get elapsed time in seconds
     * @return double Elapsed time
     */
    double elapsed_seconds() const;
    
    /**
     * @brief Get elapsed time in milliseconds
     * @return double Elapsed time
     */
    double elapsed_milliseconds() const;
    
private:
    struct timespec start_time_;
};

/**
 * @brief Get system memory information
 */
struct MemoryInfo {
    size_t total_physical;     ///< Total physical memory
    size_t available_physical; ///< Available physical memory
    size_t total_swap;         ///< Total swap space
    size_t available_swap;     ///< Available swap space
};

/**
 * @brief Get system memory information
 * @return MemoryInfo Memory statistics
 */
MemoryInfo get_memory_info();

/**
 * @brief Set process priority (nice value)
 * @param priority Nice value (-20 to 19)
 * @return bool True if successful
 */
bool set_process_priority(int priority);

/**
 * @brief Get current process priority
 * @return int Nice value
 */
int get_process_priority();

/**
 * @brief Lock memory pages to prevent swapping
 * @param addr Memory address
 * @param size Memory size
 * @return bool True if successful
 */
bool lock_memory(void* addr, size_t size);

/**
 * @brief Unlock memory pages
 * @param addr Memory address
 * @param size Memory size
 * @return bool True if successful
 */
bool unlock_memory(void* addr, size_t size);

/**
 * @brief Advise kernel about memory usage pattern
 * @param addr Memory address
 * @param size Memory size
 * @param advice Advice flag (e.g., MADV_SEQUENTIAL)
 * @return bool True if successful
 */
bool advise_memory_usage(void* addr, size_t size, int advice);

/**
 * @brief Get number of CPU cores
 * @return size_t Number of CPU cores
 */
size_t get_cpu_count();

/**
 * @brief Set CPU affinity for current thread
 * @param cpu_set CPU set mask
 * @return bool True if successful
 */
bool set_thread_affinity(const std::vector<int>& cpu_set);

} // namespace omop::extract::platform

#endif // !_WIN32