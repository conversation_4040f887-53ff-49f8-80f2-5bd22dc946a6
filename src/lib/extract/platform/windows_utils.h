/**
 * @file windows_utils.h
 * @brief Windows-specific utility functions for the extract module
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains Windows-specific implementations for file handling,
 * path operations, and system interactions.
 */

#pragma once

#ifdef _WIN32

#include <string>
#include <vector>
#include <memory>
#include <windows.h>

namespace omop::extract::platform {

/**
 * @brief Windows file handle wrapper with RAII
 */
class WindowsFileHandle {
public:
    /**
     * @brief Constructor
     * @param handle Windows file handle
     */
    explicit WindowsFileHandle(HANDLE handle = INVALID_HANDLE_VALUE) : handle_(handle) {}
    
    /**
     * @brief Destructor
     */
    ~WindowsFileHandle() {
        if (handle_ != INVALID_HANDLE_VALUE) {
            CloseHandle(handle_);
        }
    }
    
    // Delete copy operations
    WindowsFileHandle(const WindowsFileHandle&) = delete;
    WindowsFileHandle& operator=(const WindowsFileHandle&) = delete;
    
    // Move operations
    WindowsFileHandle(WindowsFileHandle&& other) noexcept : handle_(other.handle_) {
        other.handle_ = INVALID_HANDLE_VALUE;
    }
    
    WindowsFileHandle& operator=(WindowsFileHandle&& other) noexcept {
        if (this != &other) {
            if (handle_ != INVALID_HANDLE_VALUE) {
                CloseHandle(handle_);
            }
            handle_ = other.handle_;
            other.handle_ = INVALID_HANDLE_VALUE;
        }
        return *this;
    }
    
    /**
     * @brief Get raw handle
     * @return HANDLE Windows file handle
     */
    HANDLE get() const { return handle_; }
    
    /**
     * @brief Check if handle is valid
     * @return bool True if valid
     */
    bool is_valid() const { return handle_ != INVALID_HANDLE_VALUE; }
    
private:
    HANDLE handle_;
};

/**
 * @brief Convert UTF-8 string to wide string
 * @param utf8_str UTF-8 encoded string
 * @return std::wstring Wide string
 */
std::wstring utf8_to_wide(const std::string& utf8_str);

/**
 * @brief Convert wide string to UTF-8 string
 * @param wide_str Wide string
 * @return std::string UTF-8 encoded string
 */
std::string wide_to_utf8(const std::wstring& wide_str);

/**
 * @brief Get Windows error message
 * @param error_code Windows error code (default = GetLastError())
 * @return std::string Error message
 */
std::string get_windows_error_message(DWORD error_code = 0);

/**
 * @brief Create a memory-mapped file for reading
 * @param filepath File path
 * @return std::pair<WindowsFileHandle, WindowsFileHandle> File and mapping handles
 */
std::pair<WindowsFileHandle, WindowsFileHandle> create_file_mapping(const std::string& filepath);

/**
 * @brief Map file view into memory
 * @param mapping_handle Mapping handle
 * @param offset File offset
 * @param size Size to map (0 = entire file)
 * @return void* Mapped memory pointer
 */
void* map_view_of_file(HANDLE mapping_handle, size_t offset = 0, size_t size = 0);

/**
 * @brief Unmap file view from memory
 * @param view Mapped memory pointer
 * @return bool True if successful
 */
bool unmap_view_of_file(void* view);

/**
 * @brief Get file size
 * @param filepath File path
 * @return size_t File size in bytes
 */
size_t get_file_size(const std::string& filepath);

/**
 * @brief Check if path is a network path
 * @param path File path
 * @return bool True if network path
 */
bool is_network_path(const std::string& path);

/**
 * @brief Get available drive letters
 * @return std::vector<char> Available drive letters
 */
std::vector<char> get_available_drives();

/**
 * @brief Get temporary directory path
 * @return std::string Temporary directory path
 */
std::string get_temp_directory();

/**
 * @brief Create unique temporary file
 * @param prefix File name prefix
 * @param extension File extension
 * @return std::string Temporary file path
 */
std::string create_temp_file(const std::string& prefix = "omop_", 
                           const std::string& extension = ".tmp");

/**
 * @brief Set file attributes
 * @param filepath File path
 * @param attributes File attributes (FILE_ATTRIBUTE_*)
 * @return bool True if successful
 */
bool set_file_attributes(const std::string& filepath, DWORD attributes);

/**
 * @brief Get file attributes
 * @param filepath File path
 * @return DWORD File attributes (INVALID_FILE_ATTRIBUTES on error)
 */
DWORD get_file_attributes(const std::string& filepath);

/**
 * @brief High-resolution timer using Windows performance counter
 */
class WindowsHighResTimer {
public:
    /**
     * @brief Constructor - starts timer
     */
    WindowsHighResTimer();
    
    /**
     * @brief Reset timer
     */
    void reset();
    
    /**
     * @brief Get elapsed time in seconds
     * @return double Elapsed time
     */
    double elapsed_seconds() const;
    
    /**
     * @brief Get elapsed time in milliseconds
     * @return double Elapsed time
     */
    double elapsed_milliseconds() const;
    
private:
    LARGE_INTEGER start_time_;
    LARGE_INTEGER frequency_;
};

/**
 * @brief Get system memory information
 */
struct MemoryInfo {
    size_t total_physical;     ///< Total physical memory
    size_t available_physical; ///< Available physical memory
    size_t total_virtual;      ///< Total virtual memory
    size_t available_virtual;  ///< Available virtual memory
};

/**
 * @brief Get system memory information
 * @return MemoryInfo Memory statistics
 */
MemoryInfo get_memory_info();

/**
 * @brief Set process priority
 * @param priority Priority class (e.g., NORMAL_PRIORITY_CLASS)
 * @return bool True if successful
 */
bool set_process_priority(DWORD priority);

/**
 * @brief Enable large page support for process
 * @return bool True if successful
 */
bool enable_large_pages();

} // namespace omop::extract::platform

#endif // _WIN32