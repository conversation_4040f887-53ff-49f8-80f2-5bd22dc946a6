/**
 * @file extract.h
 * @brief Comprehensive header for the OMOP ETL extract module
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header provides convenient access to all extraction functionality
 * within the OMOP ETL pipeline, including various data source extractors
 * and supporting utilities. It serves as the main entry point for data
 * extraction operations.
 * 
 * @section design Design Principles
 * - Modularity: Separate extractors for different data sources
 * - Extensibility: Easy to add new extractor types
 * - Performance: Efficient data extraction with batching
 * - Error Handling: Robust error management
 * - Progress Tracking: Real-time extraction monitoring
 * 
 * @section components Components
 * - Core Interfaces: Base extraction interfaces
 * - File Extractors: CSV and JSON data extraction
 * - Database Extractors: PostgreSQL, MySQL, and ODBC support
 * - Batch Processing: Efficient record batch handling
 * - Parallel Extraction: Multi-threaded data extraction
 * 
 * @section usage Usage
 * To use the extraction module:
 * 1. Create appropriate extractor
 * 2. Configure extraction parameters
 * 3. Execute extraction process
 * 4. Process extracted records
 * 
 * @section example Example
 * @code
 * auto extractor = create_extractor_auto("data.csv");
 * BatchExtractor batch_extractor(std::move(extractor));
 * auto records = batch_extractor.extract_all();
 * @endcode
 */

#pragma once

// Core interfaces
#include "core/interfaces.h"

// Base extractor functionality
#include "extract/extractor_base.h"
#include "extract/extractor_factory.h"

// File-based extractors
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"

// Database extractors
#include "extract/database_connector.h"
#include "extract/postgresql_connector.h"
#ifdef OMOP_HAS_MYSQL
#include "extract/mysql_connector.h"
#endif
#ifdef OMOP_HAS_ODBC
#include "extract/odbc_connector.h"
#endif

// Utilities
#include "common/exceptions.h"
#include "common/logging.h"

/**
 * @namespace omop::extract
 * @brief Data extraction functionality for the OMOP ETL pipeline
 * 
 * @section description Description
 * The extract namespace contains all components responsible for extracting
 * data from various sources including files (CSV, JSON) and databases
 * (PostgreSQL, MySQL, ODBC). The module provides a unified interface for
 * data extraction with support for batch processing, error handling, and
 * progress monitoring.
 * 
 * @section features Features
 * - Multiple data source support
 * - Batch processing capabilities
 * - Parallel extraction
 * - Progress monitoring
 * - Error handling
 * - Configuration validation
 */
namespace omop::extract {

/**
 * @brief Simplified extractor creation with automatic type detection
 * @param source_path Path to data source (file or connection string)
 * @param config Additional configuration parameters
 * @return std::unique_ptr<core::IExtractor> Configured extractor instance
 * @throws ConfigurationException if source type cannot be determined
 * 
 * @section details Implementation Details
 * - Detects source type automatically
 * - Creates appropriate extractor
 * - Applies configuration
 * - Thread-safe
 */
std::unique_ptr<core::IExtractor> create_extractor_auto(
    const std::string& source_path,
    const std::unordered_map<std::string, std::any>& config = {});

/**
 * @brief Extract data source type from path or configuration
 * @param source_path Path to data source
 * @return std::string Extractor type identifier
 * 
 * @section details Implementation Details
 * - Analyzes path/configuration
 * - Determines source type
 * - Thread-safe
 */
std::string detect_source_type(const std::string& source_path);

/**
 * @brief Validate extractor configuration
 * @param type Extractor type
 * @param config Configuration parameters
 * @return std::pair<bool, std::string> Validation result and error message
 * 
 * @section details Implementation Details
 * - Validates parameters
 * - Checks compatibility
 * - Thread-safe
 */
std::pair<bool, std::string> validate_extractor_config(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config);

/**
 * @brief Convenience class for batch extraction operations
 * 
 * @section description Description
 * This class provides high-level functionality for extracting data
 * from various sources with automatic error handling and progress tracking.
 * 
 * @section features Features
 * - Batch processing
 * - Progress monitoring
 * - Error handling
 * - Statistics tracking
 * - Callback support
 */
class BatchExtractor {
public:
    /**
     * @brief Configuration for batch extraction
     * 
     * @section description Description
     * Defines parameters for controlling batch extraction behavior.
     * 
     * @section fields Fields
     * - batch_size: Number of records per batch
     * - max_records: Maximum records to extract (0 = unlimited)
     * - continue_on_error: Whether to continue on errors
     * - progress_callback: Progress reporting function
     * - error_callback: Error handling function
     */
    struct Config {
        size_t batch_size = 10000;  ///< Records per batch
        size_t max_records = 0;     ///< Maximum records (0 = unlimited)
        bool continue_on_error = true;  ///< Continue on errors
        std::function<void(size_t, size_t)> progress_callback;  ///< Progress callback
        std::function<void(const std::string&)> error_callback;  ///< Error callback
    };

    /**
     * @brief Constructor with default config
     * @param extractor Extractor instance
     * 
     * @section details Implementation Details
     * - Takes ownership of extractor
     * - Initializes with defaults
     * - Thread-safe
     */
    explicit BatchExtractor(std::unique_ptr<core::IExtractor> extractor);

    /**
     * @brief Constructor with custom config
     * @param extractor Extractor instance
     * @param config Batch extraction configuration
     * 
     * @section details Implementation Details
     * - Takes ownership of extractor
     * - Applies configuration
     * - Thread-safe
     */
    BatchExtractor(std::unique_ptr<core::IExtractor> extractor,
                  const Config& config);

    /**
     * @brief Extract all records
     * @return std::vector<core::Record> All extracted records
     * 
     * @section details Implementation Details
     * - Processes in batches
     * - Handles errors
     * - Reports progress
     * - Thread-safe
     */
    std::vector<core::Record> extract_all();

    /**
     * @brief Extract records with callback processing
     * @param processor Callback function for processing each batch
     * @return size_t Total number of records processed
     * 
     * @section details Implementation Details
     * - Streams batches to callback
     * - Handles errors
     * - Reports progress
     * - Thread-safe
     */
    size_t extract_with_callback(
        std::function<void(const core::RecordBatch&)> processor);

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     * 
     * @section details Implementation Details
     * - Collects metrics
     * - Thread-safe
     */
    std::unordered_map<std::string, std::any> get_statistics() const;

private:
    std::unique_ptr<core::IExtractor> extractor_;  ///< Extractor instance
    Config config_;  ///< Extraction configuration
    core::ProcessingContext context_;  ///< Processing context
};

/**
 * @brief Parallel extraction coordinator
 * 
 * @section description Description
 * This class manages parallel extraction from multiple sources,
 * coordinating thread pools and aggregating results.
 * 
 * @section features Features
 * - Multi-threaded extraction
 * - Result aggregation
 * - Order preservation
 * - Progress tracking
 * - Error handling
 */
class ParallelExtractor {
public:
    /**
     * @brief Configuration for parallel extraction
     * 
     * @section description Description
     * Defines parameters for controlling parallel extraction behavior.
     * 
     * @section fields Fields
     * - num_threads: Number of worker threads
     * - queue_size: Size of result queue
     * - preserve_order: Whether to preserve record order
     */
    struct Config {
        size_t num_threads = 4;  ///< Worker thread count
        size_t queue_size = 100;  ///< Result queue size
        bool preserve_order = false;  ///< Preserve record order
    };

    /**
     * @brief Constructor
     * @param config Parallel extraction configuration
     * 
     * @section details Implementation Details
     * - Initializes thread pool
     * - Sets up queues
     * - Thread-safe
     */
    explicit ParallelExtractor(const Config& config);

    /**
     * @brief Add extractor to parallel processing
     * @param extractor Extractor instance
     * @param name Optional name for the extractor
     * 
     * @section details Implementation Details
     * - Takes ownership
     * - Assigns worker
     * - Thread-safe
     */
    void add_extractor(std::unique_ptr<core::IExtractor> extractor,
                      const std::string& name = "");

    /**
     * @brief Extract records with streaming callback
     * @param processor Callback for processing batches
     * 
     * @section details Implementation Details
     * - Coordinates workers
     * - Streams results
     * - Handles errors
     * - Thread-safe
     */
    void extract_streaming(
        std::function<void(const core::RecordBatch&, const std::string&)> processor);

private:
    Config config_;  ///< Parallel extraction configuration
    std::vector<std::unique_ptr<core::IExtractor>> extractors_;  ///< Extractor instances
    std::vector<std::string> extractor_names_;  ///< Extractor names
};

/**
 * @brief Utility functions for common extraction patterns
 * 
 * @section description Description
 * Provides convenience functions for common data extraction scenarios.
 * 
 * @section features Features
 * - File extraction
 * - Database extraction
 * - Connection management
 * - Error handling
 */
namespace utils {

/**
 * @brief Extract records from CSV file
 * @param filepath Path to CSV file
 * @param options Additional CSV options
 * @return std::vector<core::Record> Extracted records
 * 
 * @section details Implementation Details
 * - Creates CSV extractor
 * - Processes file
 * - Handles errors
 * - Thread-safe
 */
std::vector<core::Record> extract_csv(
    const std::string& filepath,
    const CsvOptions& options = {});

/**
 * @brief Extract records from JSON file
 * @param filepath Path to JSON file
 * @param options Additional JSON options
 * @return std::vector<core::Record> Extracted records
 * 
 * @section details Implementation Details
 * - Creates JSON extractor
 * - Processes file
 * - Handles errors
 * - Thread-safe
 */
std::vector<core::Record> extract_json(
    const std::string& filepath,
    const JsonOptions& options = {});

/**
 * @brief Extract records from database table
 * @param connection Database connection
 * @param table_name Table name
 * @param filter Optional WHERE clause
 * @return std::vector<core::Record> Extracted records
 * 
 * @section details Implementation Details
 * - Creates table extractor
 * - Executes query
 * - Handles errors
 * - Thread-safe
 */
std::vector<core::Record> extract_table(
    std::unique_ptr<IDatabaseConnection> connection,
    const std::string& table_name,
    const std::string& filter = "");

/**
 * @brief Create database connection from URL
 * @param url Database URL (e.g., "postgresql://user:pass@host:port/db")
 * @return std::unique_ptr<IDatabaseConnection> Database connection
 * 
 * @section details Implementation Details
 * - Parses URL
 * - Creates connection
 * - Handles errors
 * - Thread-safe
 */
std::unique_ptr<IDatabaseConnection> create_connection_from_url(
    const std::string& url);

} // namespace utils

} // namespace omop::extract