/**
 * @file transformation_engine.h
 * @brief Core transformation engine and field transformation interfaces
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header defines the core transformation engine and field transformation
 * interfaces for the OMOP ETL pipeline. It provides a flexible framework for
 * implementing and applying various types of data transformations.
 * 
 * @section design Design Principles
 * - Extensibility: Custom transformation support
 * - Composability: Chain multiple transformations
 * - Validation: Input validation and error handling
 * - Configuration: Flexible parameter configuration
 * - Thread Safety: Safe concurrent transformations
 * 
 * @section components Components
 * - FieldTransformation: Base interface for field transformations
 * - DirectTransformation: No-op transformation
 * - DateTransformation: Date/time format conversion
 * - VocabularyTransformation: Concept mapping
 * - NumericTransformation: Numeric operations
 * - StringConcatenationTransformation: String combination
 * - ConditionalTransformation: Rule-based transformation
 * - TransformationEngine: Main transformation orchestrator
 * 
 * @section usage Usage
 * To use the transformation system:
 * 1. Create transformation instances
 * 2. Configure transformations
 * 3. Apply transformations to data
 * 4. Handle transformation results
 * 
 * @section example Example
 * @code
 * auto engine = TransformationEngine::create_for_table("patients", config);
 * engine->initialize(config, context);
 * auto result = engine->transform_record(record, context);
 * @endcode
 */

#pragma once

#include "core/interfaces.h"
#include "common/configuration.h"
#include "common/exceptions.h"
#include <memory>
#include <string>
#include <unordered_map>
#include <functional>
#include <regex>
#include <chrono>

namespace omop::transform {

/**
 * @brief Base class for field transformations
 * 
 * @section description Description
 * This abstract class provides the interface for all field-level transformations
 * in the ETL pipeline. Concrete implementations handle specific transformation types.
 * 
 * @section features Features
 * - Value transformation
 * - Input validation
 * - Type identification
 * - Configuration support
 */
class FieldTransformation {
public:
    /**
     * @brief Virtual destructor
     * 
     * @section details Implementation Details
     * - Ensures cleanup
     * - Thread-safe
     */
    virtual ~FieldTransformation() = default;

    /**
     * @brief Apply transformation to a field value
     * @param input Input value
     * @param context Processing context
     * @return std::any Transformed value
     * 
     * @section details Implementation Details
     * - Transforms value
     * - Thread-safe
     */
    virtual std::any transform(const std::any& input,
                              core::ProcessingContext& context) = 0;

    /**
     * @brief Validate input value before transformation
     * @param input Input value
     * @return bool True if valid
     * 
     * @section details Implementation Details
     * - Validates input
     * - Thread-safe
     */
    virtual bool validate_input(const std::any& input) const = 0;

    /**
     * @brief Get transformation type name
     * @return std::string Type identifier
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Configure transformation with parameters
     * @param params Configuration parameters
     * 
     * @section details Implementation Details
     * - Configures transformation
     * - Thread-safe
     */
    virtual void configure(const YAML::Node& params) = 0;
};

/**
 * @brief Direct field mapping (no transformation)
 * 
 * @section description Description
 * Implements a pass-through transformation that returns the input value
 * unchanged. Useful for direct field mappings.
 * 
 * @section features Features
 * - No transformation
 * - Input validation
 * - Simple configuration
 */
class DirectTransformation : public FieldTransformation {
public:
    /**
     * @brief Transform implementation
     * @param input Input value
     * @param context Processing context
     * @return std::any Input value unchanged
     * 
     * @section details Implementation Details
     * - Returns input
     * - Thread-safe
     */
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override {
        return input;
    }

    /**
     * @brief Validate input
     * @param input Input value
     * @return bool True if input has value
     * 
     * @section details Implementation Details
     * - Checks value
     * - Thread-safe
     */
    bool validate_input(const std::any& input) const override {
        return input.has_value();
    }

    /**
     * @brief Get type
     * @return std::string "direct"
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "direct"; }

    /**
     * @brief Configure transformation
     * @param params Configuration parameters
     * 
     * @section details Implementation Details
     * - No configuration needed
     * - Thread-safe
     */
    void configure(const YAML::Node& params) override {
        // No configuration needed for direct mapping
    }
};

/**
 * @brief Date format transformation
 * 
 * @section description Description
 * Converts date/time values between different formats and handles
 * timezone conversions for OMOP CDM compliance.
 * 
 * @section features Features
 * - Format conversion
 * - Timezone handling
 * - Default time
 * - Format validation
 */
class DateTransformation : public FieldTransformation {
public:
    /**
     * @brief Constructor
     * 
     * @section details Implementation Details
     * - Initializes defaults
     * - Thread-safe
     */
    DateTransformation() = default;

    /**
     * @brief Transform implementation
     * @param input Input value
     * @param context Processing context
     * @return std::any Transformed date
     * 
     * @section details Implementation Details
     * - Converts format
     * - Handles timezone
     * - Thread-safe
     */
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    /**
     * @brief Validate input
     * @param input Input value
     * @return bool True if valid date
     * 
     * @section details Implementation Details
     * - Validates format
     * - Thread-safe
     */
    bool validate_input(const std::any& input) const override;

    /**
     * @brief Get type
     * @return std::string "date_transform"
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "date_transform"; }

    /**
     * @brief Configure transformation
     * @param params Configuration parameters
     * 
     * @section details Implementation Details
     * - Sets formats
     * - Sets timezone
     * - Thread-safe
     */
    void configure(const YAML::Node& params) override;

private:
    std::string input_format_{"%Y-%m-%d"};  ///< Input date format
    std::string output_format_{"%Y-%m-%d %H:%M:%S"};  ///< Output date format
    std::string timezone_{"UTC"};  ///< Timezone
    bool add_time_{false};  ///< Add time flag
    std::string default_time_{"00:00:00"};  ///< Default time
};

/**
 * @brief Vocabulary mapping transformation
 * 
 * @section description Description
 * Maps source values to OMOP concept IDs using vocabulary lookups.
 * 
 * @section features Features
 * - Concept mapping
 * - Vocabulary lookup
 * - Default handling
 * - Case sensitivity
 */
class VocabularyTransformation : public FieldTransformation {
public:
    /**
     * @brief Constructor
     * @param vocabulary_service Reference to vocabulary service
     * 
     * @section details Implementation Details
     * - Stores service
     * - Thread-safe
     */
    explicit VocabularyTransformation(class VocabularyService& vocabulary_service);

    /**
     * @brief Transform implementation
     * @param input Input value
     * @param context Processing context
     * @return std::any Mapped concept ID
     * 
     * @section details Implementation Details
     * - Looks up concept
     * - Handles defaults
     * - Thread-safe
     */
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    /**
     * @brief Validate input
     * @param input Input value
     * @return bool True if valid
     * 
     * @section details Implementation Details
     * - Validates input
     * - Thread-safe
     */
    bool validate_input(const std::any& input) const override;

    /**
     * @brief Get type
     * @return std::string "vocabulary_mapping"
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "vocabulary_mapping"; }

    /**
     * @brief Configure transformation
     * @param params Configuration parameters
     * 
     * @section details Implementation Details
     * - Sets vocabularies
     * - Sets defaults
     * - Thread-safe
     */
    void configure(const YAML::Node& params) override;

private:
    VocabularyService& vocabulary_service_;  ///< Vocabulary service
    std::string vocabulary_name_;  ///< Vocabulary name
    std::string source_vocabulary_;  ///< Source vocabulary
    std::string target_vocabulary_{"OMOP"};  ///< Target vocabulary
    int default_concept_id_{0};  ///< Default concept ID
    bool case_sensitive_{false};  ///< Case sensitivity flag
};

/**
 * @brief Numeric value transformation
 * 
 * @section description Description
 * Handles numeric conversions, unit conversions, and calculations.
 * 
 * @section features Features
 * - Arithmetic operations
 * - Unit conversion
 * - Range validation
 * - Precision control
 */
class NumericTransformation : public FieldTransformation {
public:
    /**
     * @brief Operation types
     */
    enum class Operation {
        None,      ///< No operation
        Multiply,  ///< Multiplication
        Divide,    ///< Division
        Add,       ///< Addition
        Subtract,  ///< Subtraction
        Round,     ///< Rounding
        Floor,     ///< Floor
        Ceiling,   ///< Ceiling
        Absolute   ///< Absolute value
    };

    /**
     * @brief Transform implementation
     * @param input Input value
     * @param context Processing context
     * @return std::any Transformed number
     * 
     * @section details Implementation Details
     * - Applies operation
     * - Converts units
     * - Thread-safe
     */
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    /**
     * @brief Validate input
     * @param input Input value
     * @return bool True if valid number
     * 
     * @section details Implementation Details
     * - Validates number
     * - Thread-safe
     */
    bool validate_input(const std::any& input) const override;

    /**
     * @brief Get type
     * @return std::string "numeric_transform"
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "numeric_transform"; }

    /**
     * @brief Configure transformation
     * @param params Configuration parameters
     * 
     * @section details Implementation Details
     * - Sets operation
     * - Sets ranges
     * - Thread-safe
     */
    void configure(const YAML::Node& params) override;

private:
    Operation operation_{Operation::None};  ///< Operation type
    double operand_{1.0};  ///< Operation operand
    int precision_{2};  ///< Decimal precision
    std::optional<double> min_value_;  ///< Minimum value
    std::optional<double> max_value_;  ///< Maximum value
    std::string unit_conversion_;  ///< Unit conversion
};

/**
 * @brief String concatenation transformation
 * 
 * @section description Description
 * Combines multiple fields into a single string value.
 * 
 * @section features Features
 * - Field combination
 * - Separator control
 * - Empty handling
 * - Prefix/suffix
 */
class StringConcatenationTransformation : public FieldTransformation {
public:
    /**
     * @brief Transform implementation
     * @param input Input value
     * @param context Processing context
     * @return std::any Concatenated string
     * 
     * @section details Implementation Details
     * - Combines fields
     * - Thread-safe
     */
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    /**
     * @brief Validate input
     * @param input Input value
     * @return bool True if valid
     * 
     * @section details Implementation Details
     * - Validates input
     * - Thread-safe
     */
    bool validate_input(const std::any& input) const override;

    /**
     * @brief Get type
     * @return std::string "string_concatenation"
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "string_concatenation"; }

    /**
     * @brief Configure transformation
     * @param params Configuration parameters
     * 
     * @section details Implementation Details
     * - Sets fields
     * - Sets options
     * - Thread-safe
     */
    void configure(const YAML::Node& params) override;

    /**
     * @brief Set source fields for concatenation
     * @param fields Vector of field names
     * 
     * @section details Implementation Details
     * - Sets fields
     * - Thread-safe
     */
    void set_source_fields(const std::vector<std::string>& fields) {
        source_fields_ = fields;
    }

    /**
     * @brief Transform multiple values
     * @param values Map of field names to values
     * @param context Processing context
     * @return std::any Concatenated string
     * 
     * @section details Implementation Details
     * - Combines values
     * - Thread-safe
     */
    std::any transform_multiple(const std::unordered_map<std::string, std::any>& values,
                               core::ProcessingContext& context);

private:
    std::vector<std::string> source_fields_;  ///< Source fields
    std::string separator_{" "};  ///< Field separator
    bool skip_empty_{true};  ///< Skip empty flag
    std::string prefix_;  ///< String prefix
    std::string suffix_;  ///< String suffix
};

/**
 * @brief Conditional transformation based on rules
 * 
 * @section description Description
 * Applies different transformations based on conditions.
 * 
 * @section features Features
 * - Rule evaluation
 * - Multiple conditions
 * - Default handling
 * - Value comparison
 */
class ConditionalTransformation : public FieldTransformation {
public:
    /**
     * @brief Condition structure
     */
    struct Condition {
        std::string field;  ///< Field name
        std::string operator_type;  ///< Operator type
        std::any value;  ///< Comparison value
        std::string then_value;  ///< Value if true
        std::optional<std::string> else_value;  ///< Value if false
    };

    /**
     * @brief Transform implementation
     * @param input Input value
     * @param context Processing context
     * @return std::any Conditional result
     * 
     * @section details Implementation Details
     * - Evaluates conditions
     * - Returns result
     * - Thread-safe
     */
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    /**
     * @brief Validate input
     * @param input Input value
     * @return bool True if valid
     * 
     * @section details Implementation Details
     * - Validates input
     * - Thread-safe
     */
    bool validate_input(const std::any& input) const override;

    /**
     * @brief Get type
     * @return std::string "conditional"
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "conditional"; }

    /**
     * @brief Configure transformation
     * @param params Configuration parameters
     * 
     * @section details Implementation Details
     * - Sets conditions
     * - Sets defaults
     * - Thread-safe
     */
    void configure(const YAML::Node& params) override;

private:
    std::vector<Condition> conditions_;  ///< Condition list
    std::string default_value_;  ///< Default value

    /**
     * @brief Evaluate condition
     * @param condition Condition to evaluate
     * @param value Value to check
     * @return bool True if condition met
     * 
     * @section details Implementation Details
     * - Evaluates condition
     * - Thread-safe
     */
    bool evaluate_condition(const Condition& condition,
                          const std::any& value) const;
};

/**
 * @brief Main transformation engine
 * 
 * @section description Description
 * Orchestrates the application of transformations to records and fields.
 * 
 * @section features Features
 * - Record transformation
 * - Field mapping
 * - Filter application
 * - Error handling
 */
class TransformationEngine : public core::ITransformer {
public:
    /**
     * @brief Initialize engine
     * @param config Configuration
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Sets up engine
     * - Thread-safe
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Register transformation
     * @param type Transformation type
     * @param factory Factory function
     * 
     * @section details Implementation Details
     * - Registers type
     * - Thread-safe
     */
    void register_transformation(const std::string& type,
        std::function<std::unique_ptr<FieldTransformation>()> factory);

    /**
     * @brief Apply filters
     * @param record Record to filter
     * @param filters Filter string
     * @return bool True if passes filters
     * 
     * @section details Implementation Details
     * - Evaluates filters
     * - Thread-safe
     */
    bool apply_filters(const core::Record& record,
                      const std::string& filters) const;

    /**
     * @brief Create engine for table
     * @param table_name Table name
     * @param config Configuration
     * @return std::unique_ptr<TransformationEngine> Engine instance
     * 
     * @section details Implementation Details
     * - Creates engine
     * - Thread-safe
     */
    static std::unique_ptr<TransformationEngine> create_for_table(
        const std::string& table_name,
        const common::ConfigurationManager& config);

    /**
     * @brief Register engine factory
     * @param table_name Table name
     * @param factory Factory function
     * 
     * @section details Implementation Details
     * - Registers factory
     * - Thread-safe
     */
    static void register_engine(const std::string& table_name,
        std::function<std::unique_ptr<TransformationEngine>()> factory);

private:
    std::unordered_map<std::string, 
        std::function<std::unique_ptr<FieldTransformation>()>> factories_;  ///< Transformation factories
};

} // namespace omop::transform