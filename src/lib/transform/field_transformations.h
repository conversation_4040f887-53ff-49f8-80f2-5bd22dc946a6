/**
 * @file field_transformations.h
 * @brief Field transformation definitions for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header provides field-level transformation utilities and helper classes
 * for the transform module. It defines structures and classes for managing
 * field mappings, transformation chains, and batch field transformations.
 * 
 * @section design Design Principles
 * - Fluent Interface: Builder pattern for field mappings
 * - Chain of Responsibility: Sequential transformations
 * - Batch Processing: Efficient field transformations
 * - Error Handling: Robust error management
 * - Performance: Caching and optimization
 * 
 * @section components Components
 * - FieldMapping: Field mapping metadata
 * - TransformationChain: Sequential transformations
 * - FieldTransformationBuilder: Fluent builder interface
 * - BatchFieldTransformer: Batch field processing
 * 
 * @section usage Usage
 * To use the field transformation system:
 * 1. Create field mappings
 * 2. Build transformation chains
 * 3. Apply transformations
 * 4. Handle results
 * 
 * @section example Example
 * @code
 * auto mapping = FieldTransformationBuilder()
 *     .from_field("source_field")
 *     .to_field("target_field")
 *     .with_transformation("date_transform")
 *     .required()
 *     .build();
 * @endcode
 */

#pragma once

/**
 * @brief Field mapping information
 * 
 * @section description Description
 * Contains metadata about how a field should be mapped from source to target,
 * including transformation rules, validation requirements, and default values.
 * 
 * @section features Features
 * - Source/target mapping
 * - Transformation rules
 * - Validation rules
 * - Default values
 * - Required flags
 */
struct FieldMapping {
    std::string source_field;  ///< Source field name
    std::string target_field;  ///< Target field name
    std::string transformation_type;  ///< Transformation type
    YAML::Node transformation_params;  ///< Transformation parameters
    bool is_required{false};  ///< Required flag
    std::string default_value;  ///< Default value
    std::vector<std::string> validation_rules;  ///< Validation rules
};

/**
 * @brief Transformation chain
 * 
 * @section description Description
 * Represents a sequence of transformations to apply to a field,
 * executed in order with error handling and logging.
 * 
 * @section features Features
 * - Sequential execution
 * - Error handling
 * - Logging
 * - Chain management
 */
class TransformationChain {
public:
    /**
     * @brief Add transformation to the chain
     * @param transformation Transformation to add
     * 
     * @section details Implementation Details
     * - Adds transformation
     * - Thread-safe
     */
    void add_transformation(std::unique_ptr<FieldTransformation> transformation) {
        transformations_.push_back(std::move(transformation));
    }

    /**
     * @brief Apply all transformations in sequence
     * @param input Input value
     * @param context Processing context
     * @return std::any Transformed value
     * 
     * @section details Implementation Details
     * - Applies transformations
     * - Handles errors
     * - Thread-safe
     */
    std::any apply(const std::any& input, core::ProcessingContext& context) {
        std::any result = input;
        
        for (const auto& transformation : transformations_) {
            try {
                result = transformation->transform(result, context);
            } catch (const std::exception& e) {
                context.log("error", 
                    std::format("Transformation {} failed: {}", 
                              transformation->get_type(), e.what()));
                throw;
            }
        }
        
        return result;
    }

    /**
     * @brief Get number of transformations in chain
     * @return size_t Number of transformations
     * 
     * @section details Implementation Details
     * - Returns count
     * - Thread-safe
     */
    size_t size() const { return transformations_.size(); }

    /**
     * @brief Check if chain is empty
     * @return bool True if empty
     * 
     * @section details Implementation Details
     * - Checks emptiness
     * - Thread-safe
     */
    bool empty() const { return transformations_.empty(); }

    /**
     * @brief Clear all transformations
     * 
     * @section details Implementation Details
     * - Clears chain
     * - Thread-safe
     */
    void clear() { transformations_.clear(); }

private:
    std::vector<std::unique_ptr<FieldTransformation>> transformations_;  ///< Transformation list
};

/**
 * @brief Field transformation builder
 * 
 * @section description Description
 * Fluent interface for building field transformations with a
 * chainable API for configuring field mappings.
 * 
 * @section features Features
 * - Fluent interface
 * - Field configuration
 * - Transformation setup
 * - Validation rules
 * - Default values
 */
class FieldTransformationBuilder {
public:
    /**
     * @brief Set source field
     * @param field Source field name
     * @return FieldTransformationBuilder& Builder reference
     * 
     * @section details Implementation Details
     * - Sets source
     * - Thread-safe
     */
    FieldTransformationBuilder& from_field(const std::string& field) {
        mapping_.source_field = field;
        return *this;
    }

    /**
     * @brief Set target field
     * @param field Target field name
     * @return FieldTransformationBuilder& Builder reference
     * 
     * @section details Implementation Details
     * - Sets target
     * - Thread-safe
     */
    FieldTransformationBuilder& to_field(const std::string& field) {
        mapping_.target_field = field;
        return *this;
    }

    /**
     * @brief Add transformation
     * @param type Transformation type
     * @param params Transformation parameters
     * @return FieldTransformationBuilder& Builder reference
     * 
     * @section details Implementation Details
     * - Sets type
     * - Sets params
     * - Thread-safe
     */
    FieldTransformationBuilder& with_transformation(const std::string& type,
                                                   const YAML::Node& params = {}) {
        mapping_.transformation_type = type;
        mapping_.transformation_params = params;
        return *this;
    }

    /**
     * @brief Mark field as required
     * @param required Whether field is required
     * @return FieldTransformationBuilder& Builder reference
     * 
     * @section details Implementation Details
     * - Sets required
     * - Thread-safe
     */
    FieldTransformationBuilder& required(bool required = true) {
        mapping_.is_required = required;
        return *this;
    }

    /**
     * @brief Set default value
     * @param value Default value
     * @return FieldTransformationBuilder& Builder reference
     * 
     * @section details Implementation Details
     * - Sets default
     * - Thread-safe
     */
    FieldTransformationBuilder& with_default(const std::string& value) {
        mapping_.default_value = value;
        return *this;
    }

    /**
     * @brief Add validation rule
     * @param rule Validation rule name
     * @return FieldTransformationBuilder& Builder reference
     * 
     * @section details Implementation Details
     * - Adds rule
     * - Thread-safe
     */
    FieldTransformationBuilder& validate_with(const std::string& rule) {
        mapping_.validation_rules.push_back(rule);
        return *this;
    }

    /**
     * @brief Build the field mapping
     * @return FieldMapping Completed field mapping
     * 
     * @section details Implementation Details
     * - Returns mapping
     * - Thread-safe
     */
    FieldMapping build() const {
        return mapping_;
    }

private:
    FieldMapping mapping_;  ///< Field mapping
};

/**
 * @brief Batch field transformer
 * 
 * @section description Description
 * Applies transformations to multiple fields in a record with
 * error handling, logging, and performance tracking.
 * 
 * @section features Features
 * - Batch processing
 * - Error handling
 * - Field copying
 * - Performance tracking
 * - Statistics collection
 */
class BatchFieldTransformer {
public:
    /**
     * @brief Add field mapping
     * @param mapping Field mapping to add
     * 
     * @section details Implementation Details
     * - Adds mapping
     * - Thread-safe
     */
    void add_mapping(const FieldMapping& mapping) {
        mappings_.push_back(mapping);
    }

    /**
     * @brief Transform all fields in a record
     * @param input_record Input record
     * @param context Processing context
     * @return core::Record Transformed record
     * 
     * @section details Implementation Details
     * - Transforms fields
     * - Handles errors
     * - Copies unmapped
     * - Thread-safe
     */
    core::Record transform(const core::Record& input_record,
                         core::ProcessingContext& context) {
        core::Record output_record;

        for (const auto& mapping : mappings_) {
            try {
                // Get source value
                auto source_value = input_record.getField(mapping.source_field);
                
                if (source_value.type() == typeid(void) && mapping.is_required) {
                    if (!mapping.default_value.empty()) {
                        source_value = mapping.default_value;
                    } else {
                        context.log("error", 
                            std::format("Required field '{}' is missing", 
                                      mapping.source_field));
                        continue;
                    }
                }

                if (source_value.type() != typeid(void)) {
                    // Apply transformation
                    auto& registry = TransformationRegistry::instance();
                    auto transformation = registry.create_transformation(
                        mapping.transformation_type);
                    
                    transformation->configure(mapping.transformation_params);
                    auto result = transformation->transform(source_value, context);
                    
                    // Set in output record
                    output_record.setField(mapping.target_field, result);
                }

            } catch (const std::exception& e) {
                context.log("error", 
                    std::format("Failed to transform field '{}': {}", 
                              mapping.source_field, e.what()));
                context.increment_errors();
            }
        }

        // Copy unmapped fields if configured
        if (copy_unmapped_fields_) {
            for (const auto& field_name : input_record.getFieldNames()) {
                if (!output_record.hasField(field_name)) {
                    auto value = input_record.getField(field_name);
                    if (value.type() != typeid(void)) {
                        output_record.setField(field_name, value);
                    }
                }
            }
        }

        return output_record;
    }

    /**
     * @brief Clear all mappings
     * 
     * @section details Implementation Details
     * - Clears mappings
     * - Thread-safe
     */
    void clear() {
        mappings_.clear();
    }

    /**
     * @brief Cache statistics
     * 
     * @section description Description
     * Tracks cache performance metrics.
     * 
     * @section fields Fields
     * - size: Cache size
     * - hits: Cache hits
     * - misses: Cache misses
     * - hit_rate: Hit rate
     */
    struct CacheStats {
        size_t size;  ///< Cache size
        size_t hits;  ///< Cache hits
        size_t misses;  ///< Cache misses
        double hit_rate;  ///< Hit rate
    };

    /**
     * @brief Get cache statistics
     * @return CacheStats Cache statistics
     * 
     * @section details Implementation Details
     * - Returns stats
     * - Thread-safe
     */
    CacheStats get_stats() const {
        return cache_stats_;
    }

    /**
     * @brief Record transformation execution
     * @param field_name Field name
     * @param transformation_type Transformation type
     * @param duration Execution duration
     * @param success Success flag
     * 
     * @section details Implementation Details
     * - Records metrics
     * - Thread-safe
     */
    void record_execution(const std::string& field_name,
                         const std::string& transformation_type,
                         std::chrono::duration<double> duration,
                         bool success) {
        std::lock_guard<std::mutex> lock(mutex_);
        // Record metrics
    }

    /**
     * @brief Field statistics
     * 
     * @section description Description
     * Tracks field-level performance metrics.
     * 
     * @section fields Fields
     * - total_count: Total transformations
     * - success_count: Successful transformations
     * - error_count: Failed transformations
     * - total_duration: Total duration
     */
    struct FieldStats {
        size_t total_count{0};  ///< Total transformations
        size_t success_count{0};  ///< Successful transformations
        size_t error_count{0};  ///< Failed transformations
        std::chrono::duration<double> total_duration{0};  ///< Total duration

        /**
         * @brief Calculate average duration
         * @return double Average duration
         * 
         * @section details Implementation Details
         * - Calculates average
         * - Thread-safe
         */
        double average_duration() const {
            return total_count > 0 ? 
                total_duration.count() / total_count : 0.0;
        }

        /**
         * @brief Calculate success rate
         * @return double Success rate
         * 
         * @section details Implementation Details
         * - Calculates rate
         * - Thread-safe
         */
        double success_rate() const {
            return total_count > 0 ? 
                static_cast<double>(success_count) / total_count : 0.0;
        }
    };

    /**
     * @brief Get field statistics
     * @param field_name Field name
     * @return FieldStats Field statistics
     * 
     * @section details Implementation Details
     * - Returns stats
     * - Thread-safe
     */
    FieldStats get_field_stats(const std::string& field_name) const {
        std::lock_guard<std::mutex> lock(mutex_);
        return field_metrics_.at(field_name);
    }

    /**
     * @brief Get transformation statistics
     * @param transformation_type Transformation type
     * @return FieldStats Transformation statistics
     * 
     * @section details Implementation Details
     * - Returns stats
     * - Thread-safe
     */
    FieldStats get_transformation_stats(const std::string& transformation_type) const {
        std::lock_guard<std::mutex> lock(mutex_);
        return transformation_metrics_.at(transformation_type);
    }

    /**
     * @brief Get all field names
     * @return std::vector<std::string> Field names
     * 
     * @section details Implementation Details
     * - Returns names
     * - Thread-safe
     */
    std::vector<std::string> get_field_names() const {
        std::lock_guard<std::mutex> lock(mutex_);
        std::vector<std::string> names;
        for (const auto& [name, _] : field_metrics_) {
            names.push_back(name);
        }
        return names;
    }

    /**
     * @brief Reset all statistics
     * 
     * @section details Implementation Details
     * - Clears stats
     * - Thread-safe
     */
    void reset() {
        std::lock_guard<std::mutex> lock(mutex_);
        field_metrics_.clear();
        transformation_metrics_.clear();
        cache_stats_ = CacheStats{};
    }

private:
    std::vector<FieldMapping> mappings_;  ///< Field mappings
    bool copy_unmapped_fields_{false};  ///< Copy unmapped flag
    std::unordered_map<std::string, FieldStats> field_metrics_;  ///< Field metrics
    std::unordered_map<std::string, FieldStats> transformation_metrics_;  ///< Transformation metrics
    CacheStats cache_stats_;  ///< Cache statistics
    mutable std::mutex mutex_;  ///< Mutex for thread safety
};

} // namespace omop::transform