/**
 * @file transformations.h
 * @brief Common header for all transformation types in the OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header provides a centralized include point for all transformation
 * classes and utilities used in the transform module. It defines common
 * transformation utilities, result structures, and base classes for
 * implementing complex transformations.
 * 
 * @section design Design Principles
 * - Modularity: Independent transformation components
 * - Extensibility: Custom transformation support
 * - Error Handling: Detailed transformation results
 * - Performance: Efficient data processing
 * - Thread Safety: Safe concurrent transformations
 * 
 * @section components Components
 * - TransformationUtils: Common utility functions
 * - TransformationResult: Result structure with metadata
 * - ComplexTransformation: Base class for complex transformations
 * - TransformationRegistry: Registry for custom transformations
 * 
 * @section usage Usage
 * To use the transformation system:
 * 1. Include this header
 * 2. Use utility functions as needed
 * 3. Implement custom transformations
 * 4. Register transformations
 * 
 * @section example Example
 * @code
 * auto result = TransformationUtils::parse_date("2024-01-01", 
 *     {constants::DEFAULT_DATE_FORMAT});
 * if (result) {
 *     auto transformed = TransformationUtils::format_date(
 *         *result, constants::DEFAULT_DATETIME_FORMAT);
 * }
 * @endcode
 */

#pragma once

/**
 * @file transformations.h
 * @brief Common header for all transformation types in the OMOP ETL pipeline
 * 
 * This header provides a centralized include point for all transformation
 * classes and utilities used in the transform module.
 */

#include "transform/transformation_engine.h"
#include "transform/vocabulary_service.h"
#include "common/exceptions.h"
#include "common/logging.h"
#include "core/interfaces.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <optional>
#include <regex>
#include <algorithm>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <cmath>

namespace omop::transform {

/**
 * @brief Transform configuration constants
 * 
 * @section description Description
 * Defines common constants used throughout the transformation system.
 * 
 * @section constants Constants
 * - DEFAULT_BATCH_SIZE: Default batch size for processing
 * - MAX_VALIDATION_ERRORS: Maximum allowed validation errors
 * - NUMERIC_EPSILON: Epsilon for numeric comparisons
 * - DEFAULT_DATE_FORMAT: Default date format string
 * - DEFAULT_DATETIME_FORMAT: Default datetime format string
 * - DEFAULT_TIME_FORMAT: Default time format string
 */
namespace constants {
    constexpr size_t DEFAULT_BATCH_SIZE = 1000;  ///< Default batch size
    constexpr size_t MAX_VALIDATION_ERRORS = 100;  ///< Max validation errors
    constexpr double NUMERIC_EPSILON = 0.0001;  ///< Numeric comparison epsilon
    constexpr char DEFAULT_DATE_FORMAT[] = "%Y-%m-%d";  ///< Default date format
    constexpr char DEFAULT_DATETIME_FORMAT[] = "%Y-%m-%d %H:%M:%S";  ///< Default datetime format
    constexpr char DEFAULT_TIME_FORMAT[] = "%H:%M:%S";  ///< Default time format
}

/**
 * @brief Common transformation utilities
 * 
 * @section description Description
 * Provides utility functions for common transformation operations
 * such as date parsing, string normalization, and numeric conversions.
 * 
 * @section features Features
 * - Date/time handling
 * - String manipulation
 * - Numeric conversions
 * - Pattern matching
 * - Unit conversions
 */
class TransformationUtils {
public:
    /**
     * @brief Parse date string with multiple format attempts
     * @param date_str Date string to parse
     * @param formats Vector of format strings to try
     * @return std::optional<std::chrono::system_clock::time_point> Parsed date
     * 
     * @section details Implementation Details
     * - Tries each format in sequence
     * - Returns first successful parse
     * - Thread-safe
     */
    static std::optional<std::chrono::system_clock::time_point> parse_date(
        const std::string& date_str,
        const std::vector<std::string>& formats);

    /**
     * @brief Format date to string
     * @param time_point Time point to format
     * @param format Format string
     * @return std::string Formatted date string
     * 
     * @section details Implementation Details
     * - Uses strftime formatting
     * - Thread-safe
     */
    static std::string format_date(
        const std::chrono::system_clock::time_point& time_point,
        const std::string& format);

    /**
     * @brief Convert numeric value with unit conversion
     * @param value Input value
     * @param from_unit Source unit
     * @param to_unit Target unit
     * @return double Converted value
     * 
     * @section details Implementation Details
     * - Uses conversion factors
     * - Thread-safe
     */
    static double convert_units(
        double value,
        const std::string& from_unit,
        const std::string& to_unit);

    /**
     * @brief Normalize string value
     * @param value Input string
     * @param case_sensitive Whether to preserve case
     * @param trim_whitespace Whether to trim whitespace
     * @return std::string Normalized string
     * 
     * @section details Implementation Details
     * - Case conversion
     * - Whitespace handling
     * - Thread-safe
     */
    static std::string normalize_string(
        const std::string& value,
        bool case_sensitive = false,
        bool trim_whitespace = true);

    /**
     * @brief Validate numeric range
     * @param value Value to validate
     * @param min_value Minimum allowed value
     * @param max_value Maximum allowed value
     * @return bool True if within range
     * 
     * @section details Implementation Details
     * - Range checking
     * - Optional bounds
     * - Thread-safe
     */
    static bool validate_numeric_range(
        double value,
        std::optional<double> min_value,
        std::optional<double> max_value);

    /**
     * @brief Extract numeric value from string
     * @param str String containing numeric value
     * @param default_value Default if extraction fails
     * @return double Extracted value
     * 
     * @section details Implementation Details
     * - Regex extraction
     * - Default handling
     * - Thread-safe
     */
    static double extract_numeric(
        const std::string& str,
        double default_value = 0.0);

    /**
     * @brief Check if string matches pattern
     * @param value String to check
     * @param pattern Regex pattern
     * @return bool True if matches
     * 
     * @section details Implementation Details
     * - Regex matching
     * - Thread-safe
     */
    static bool matches_pattern(
        const std::string& value,
        const std::string& pattern);

    /**
     * @brief Split string by delimiter
     * @param str String to split
     * @param delimiter Delimiter character
     * @return std::vector<std::string> Split parts
     * 
     * @section details Implementation Details
     * - String splitting
     * - Empty handling
     * - Thread-safe
     */
    static std::vector<std::string> split_string(
        const std::string& str,
        char delimiter);

    /**
     * @brief Join strings with delimiter
     * @param parts String parts
     * @param delimiter Delimiter string
     * @return std::string Joined string
     * 
     * @section details Implementation Details
     * - String joining
     * - Empty handling
     * - Thread-safe
     */
    static std::string join_strings(
        const std::vector<std::string>& parts,
        const std::string& delimiter);

    /**
     * @brief Calculate age from birthdate
     * @param birthdate Birth date
     * @param reference_date Reference date (default: now)
     * @return int Age in years
     * 
     * @section details Implementation Details
     * - Age calculation
     * - Date handling
     * - Thread-safe
     */
    static int calculate_age(
        const std::chrono::system_clock::time_point& birthdate,
        const std::chrono::system_clock::time_point& reference_date = 
            std::chrono::system_clock::now());

    /**
     * @brief Calculate date difference
     * @param start_date Start date
     * @param end_date End date
     * @param unit Unit of difference (days, months, years)
     * @return int Difference in specified unit
     * 
     * @section details Implementation Details
     * - Date arithmetic
     * - Unit conversion
     * - Thread-safe
     */
    static int calculate_date_difference(
        const std::chrono::system_clock::time_point& start_date,
        const std::chrono::system_clock::time_point& end_date,
        const std::string& unit = "days");

private:
    static std::unordered_map<std::string, double> unit_conversion_factors_;  ///< Unit conversion factors
};

/**
 * @brief Transformation result with metadata
 * 
 * @section description Description
 * Structure for holding transformation results with success status,
 * warnings, errors, and additional metadata.
 * 
 * @section features Features
 * - Success tracking
 * - Warning collection
 * - Error handling
 * - Metadata storage
 */
struct TransformationResult {
    std::any value;  ///< Transformed value
    bool success{true};  ///< Success flag
    std::vector<std::string> warnings;  ///< Warning messages
    std::optional<std::string> error_message;  ///< Error message
    std::unordered_map<std::string, std::any> metadata;  ///< Additional metadata

    /**
     * @brief Check if transformation succeeded
     * @return bool True if successful
     * 
     * @section details Implementation Details
     * - Checks success flag
     * - Verifies no error
     * - Thread-safe
     */
    bool is_success() const { return success && !error_message.has_value(); }

    /**
     * @brief Add warning message
     * @param warning Warning message
     * 
     * @section details Implementation Details
     * - Adds warning
     * - Thread-safe
     */
    void add_warning(const std::string& warning) {
        warnings.push_back(warning);
    }

    /**
     * @brief Set error state
     * @param error Error message
     * 
     * @section details Implementation Details
     * - Sets error
     * - Updates success
     * - Thread-safe
     */
    void set_error(const std::string& error) {
        success = false;
        error_message = error;
    }
};

/**
 * @brief Base class for complex transformations
 * 
 * @section description Description
 * Abstract base class for implementing complex transformations
 * with detailed result handling.
 * 
 * @section features Features
 * - Detailed results
 * - Error handling
 * - Context awareness
 * - Standard interface
 */
class ComplexTransformation : public FieldTransformation {
public:
    /**
     * @brief Transform with detailed result
     * @param input Input value
     * @param context Processing context
     * @return TransformationResult Detailed result
     * 
     * @section details Implementation Details
     * - Performs transformation
     * - Collects metadata
     * - Thread-safe
     */
    virtual TransformationResult transform_detailed(
        const std::any& input,
        core::ProcessingContext& context) = 0;

    /**
     * @brief Standard transform implementation
     * @param input Input value
     * @param context Processing context
     * @return std::any Transformed value
     * 
     * @section details Implementation Details
     * - Uses detailed transform
     * - Handles errors
     * - Thread-safe
     */
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override {
        auto result = transform_detailed(input, context);
        if (!result.is_success()) {
            throw common::TransformationException(
                result.error_message.value_or("Unknown transformation error"),
                get_type(), "transform");
        }
        return result.value;
    }
};

/**
 * @brief Registry for custom transformations
 * 
 * @section description Description
 * Singleton registry for managing custom transformation types
 * and their factory functions.
 * 
 * @section features Features
 * - Type registration
 * - Factory management
 * - Singleton access
 * - Thread safety
 */
class TransformationRegistry {
public:
    /**
     * @brief Get singleton instance
     * @return TransformationRegistry& Registry instance
     * 
     * @section details Implementation Details
     * - Returns singleton
     * - Thread-safe
     */
    static TransformationRegistry& instance() {
        static TransformationRegistry instance;
        return instance;
    }

    /**
     * @brief Register transformation factory
     * @param type_name Transformation type name
     * @param factory Factory function
     * 
     * @section details Implementation Details
     * - Registers factory
     * - Thread-safe
     */
    void register_transformation(
        const std::string& type_name,
        std::function<std::unique_ptr<FieldTransformation>(
            const std::unordered_map<std::string, std::any>&)> factory);

    /**
     * @brief Check if transformation type exists
     * @param type_name Transformation type name
     * @return bool True if exists
     * 
     * @section details Implementation Details
     * - Checks registry
     * - Thread-safe
     */
    bool has_transformation(const std::string& type_name) const;

private:
    std::unordered_map<std::string, 
        std::function<std::unique_ptr<FieldTransformation>(
            const std::unordered_map<std::string, std::any>&)>> factories_;  ///< Factory functions
};

} // namespace omop::transform