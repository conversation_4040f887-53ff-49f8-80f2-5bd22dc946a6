/**
 * @file component_name.h
 * @brief Brief description of the component
 * 
 * @c4component
 * @c4name ComponentName
 * @c4description Detailed description of the component's purpose and role in the system
 * @c4responsibilities
 *   - Primary responsibility 1
 *   - Primary responsibility 2
 *   - Primary responsibility 3
 * @c4dependencies
 *   - Dependency1: Purpose of dependency
 *   - Dependency2: Purpose of dependency
 * @c4config ComponentConfig
 * @c4container ContainerName
 * @c4context ContextName
 */

#ifndef COMPONENT_NAME_H
#define COMPONENT_NAME_H

#include <string>
#include <memory>
#include <vector>

namespace omop {
namespace etl {

/**
 * @brief Configuration structure for the component
 * 
 * @c4config
 * @c4name ComponentConfig
 * @c4description Configuration options for the component
 * @c4fields
 *   - field1: Description of field1
 *   - field2: Description of field2
 */
struct ComponentConfig {
    std::string field1;
    int field2;
};

/**
 * @brief Interface for the component
 * 
 * @c4interface
 * @c4name IComponent
 * @c4description Interface defining the component's contract
 * @c4methods
 *   - method1: Description of method1
 *   - method2: Description of method2
 */
class IComponent {
public:
    virtual ~IComponent() = default;
    
    /**
     * @brief Description of method1
     * @param param1 Description of param1
     * @return Description of return value
     */
    virtual bool method1(const std::string& param1) = 0;
    
    /**
     * @brief Description of method2
     * @param param1 Description of param1
     * @param param2 Description of param2
     */
    virtual void method2(int param1, const std::string& param2) = 0;
};

/**
 * @brief Implementation of the component
 * 
 * @c4implementation
 * @c4name ComponentImpl
 * @c4description Concrete implementation of the component
 * @c4implements IComponent
 * @c4uses
 *   - Dependency1: How it's used
 *   - Dependency2: How it's used
 */
class ComponentImpl : public IComponent {
public:
    /**
     * @brief Constructor
     * @param config Component configuration
     */
    explicit ComponentImpl(const ComponentConfig& config);
    
    bool method1(const std::string& param1) override;
    void method2(int param1, const std::string& param2) override;

private:
    ComponentConfig config_;
    // Add private members with @c4member tags
};

} // namespace etl
} // namespace omop

#endif // COMPONENT_NAME_H 