# @c4config ComponentConfig
# @c4component ComponentName
# @c4description Configuration for the ComponentName component
# @c4fields
#   - name: Component name
#   - version: Component version
#   - settings: Component settings
#   - dependencies: Component dependencies

# Component identification
name: "ComponentName"
version: "1.0.0"

# @c4config ComponentSettings
# @c4description Component-specific settings
settings:
  # @c4field
  # @c4name field1
  # @c4description Description of field1
  # @c4type string
  # @c4required true
  field1: "value1"
  
  # @c4field
  # @c4name field2
  # @c4description Description of field2
  # @c4type integer
  # @c4required false
  # @c4default 0
  field2: 42

# @c4config ComponentDependencies
# @c4description Component dependencies
dependencies:
  # @c4dependency
  # @c4name Dependency1
  # @c4description Description of Dependency1
  # @c4required true
  dependency1:
    name: "Dependency1"
    version: "1.0.0"
    config:
      setting1: "value1"
      setting2: "value2"
  
  # @c4dependency
  # @c4name Dependency2
  # @c4description Description of Dependency2
  # @c4required false
  dependency2:
    name: "Dependency2"
    version: "2.0.0"
    config:
      setting1: "value1"
      setting2: "value2"

# @c4config ComponentValidation
# @c4description Validation rules for component configuration
validation:
  # @c4validation
  # @c4name field1_validation
  # @c4description Validation rules for field1
  # @c4field field1
  field1:
    required: true
    type: "string"
    min_length: 1
    max_length: 100
  
  # @c4validation
  # @c4name field2_validation
  # @c4description Validation rules for field2
  # @c4field field2
  field2:
    required: false
    type: "integer"
    min_value: 0
    max_value: 100

# @c4config ComponentEnvironment
# @c4description Environment-specific configuration
environment:
  # @c4env
  # @c4name development
  # @c4description Development environment settings
  development:
    debug: true
    log_level: "DEBUG"
    timeout: 30
  
  # @c4env
  # @c4name production
  # @c4description Production environment settings
  production:
    debug: false
    log_level: "INFO"
    timeout: 60 