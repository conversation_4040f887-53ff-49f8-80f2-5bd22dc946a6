# Extract Library Documentation

## Overview
The Extract Library provides data extraction capabilities for the OMOP ETL Pipeline. It implements various extractors for different data sources and formats, including CSV, JSON, and database connections.

## Components

### Base Extractor
The foundation for all extractor implementations.

#### Class: ExtractorBase
```cpp
/**
 * @brief Base class for all data extractors
 * 
 * Provides common functionality for:
 * - Data source connection
 * - Record extraction
 * - Error handling
 * - Progress tracking
 */
class ExtractorBase {
public:
    /**
     * @brief Initializes the extractor
     * @param config Extractor configuration
     */
    virtual void initialize(const ExtractorConfig& config) = 0;

    /**
     * @brief Extracts next record from source
     * @return Extracted record
     */
    virtual Record extractNext() = 0;

    /**
     * @brief Checks if more records are available
     * @return true if more records exist
     */
    virtual bool hasNext() const = 0;

protected:
    /**
     * @brief Validates extractor configuration
     * @param config Configuration to validate
     * @return true if valid
     */
    virtual bool validateConfig(const ExtractorConfig& config) = 0;

    /**
     * @brief Handles extraction errors
     * @param error Error information
     */
    virtual void handleError(const ExtractionError& error) = 0;
};
```

### CSV Extractor
Handles CSV file extraction.

#### Class: CsvExtractor
```cpp
/**
 * @brief Extracts data from CSV files
 * 
 * Features:
 * - CSV parsing
 * - Header handling
 * - Delimiter configuration
 * - Encoding support
 */
class CsvExtractor : public ExtractorBase {
public:
    /**
     * @brief Sets CSV parsing options
     * @param options Parsing options
     */
    void setParsingOptions(const CsvParsingOptions& options);

    /**
     * @brief Gets CSV header information
     * @return Header information
     */
    CsvHeader getHeader() const;

private:
    /**
     * @brief Parses CSV line
     * @param line Line to parse
     * @return Parsed fields
     */
    std::vector<std::string> parseLine(const std::string& line);

    /**
     * @brief Validates CSV format
     * @return true if valid
     */
    bool validateFormat();
};
```

### JSON Extractor
Handles JSON data extraction.

#### Class: JsonExtractor
```cpp
/**
 * @brief Extracts data from JSON sources
 * 
 * Features:
 * - JSON parsing
 * - Path-based extraction
 * - Schema validation
 * - Type conversion
 */
class JsonExtractor : public ExtractorBase {
public:
    /**
     * @brief Sets JSON extraction path
     * @param path JSON path expression
     */
    void setExtractionPath(const std::string& path);

    /**
     * @brief Validates JSON schema
     * @param schema Schema to validate against
     * @return true if valid
     */
    bool validateSchema(const JsonSchema& schema);

private:
    /**
     * @brief Extracts value from JSON
     * @param json JSON document
     * @param path Path to extract
     * @return Extracted value
     */
    JsonValue extractValue(const JsonDocument& json, const std::string& path);
};
```

### Database Connectors
Database-specific connection and extraction implementations.

#### Class: DatabaseConnector
```cpp
/**
 * @brief Base class for database connectors
 * 
 * Provides:
 * - Connection management
 * - Query execution
 * - Result set handling
 * - Transaction support
 */
class DatabaseConnector {
public:
    /**
     * @brief Connects to database
     * @param config Connection configuration
     */
    virtual void connect(const DbConnectionConfig& config) = 0;

    /**
     * @brief Executes query
     * @param query Query to execute
     * @return Query result
     */
    virtual QueryResult executeQuery(const std::string& query) = 0;

    /**
     * @brief Begins transaction
     */
    virtual void beginTransaction() = 0;

    /**
     * @brief Commits transaction
     */
    virtual void commitTransaction() = 0;

protected:
    /**
     * @brief Validates connection configuration
     * @param config Configuration to validate
     * @return true if valid
     */
    virtual bool validateConnectionConfig(const DbConnectionConfig& config) = 0;
};
```

### Connection Pool
Manages database connection pooling.

#### Class: ConnectionPool
```cpp
/**
 * @brief Manages database connection pool
 * 
 * Features:
 * - Connection pooling
 * - Load balancing
 * - Connection lifecycle
 * - Resource management
 */
class ConnectionPool {
public:
    /**
     * @brief Gets connection from pool
     * @return Database connection
     */
    std::shared_ptr<DatabaseConnector> getConnection();

    /**
     * @brief Returns connection to pool
     * @param connection Connection to return
     */
    void releaseConnection(std::shared_ptr<DatabaseConnector> connection);

private:
    /**
     * @brief Creates new connection
     * @return New connection
     */
    std::shared_ptr<DatabaseConnector> createConnection();

    /**
     * @brief Validates connection
     * @param connection Connection to validate
     * @return true if valid
     */
    bool validateConnection(std::shared_ptr<DatabaseConnector> connection);
};
```

## Configuration

### Extractor Configuration
```yaml
extract:
  # General settings
  type: "csv"
  batch_size: 1000
  error_handling: "strict"

  # CSV configuration
  csv:
    path: "/data/source.csv"
    delimiter: ","
    has_header: true
    encoding: "UTF-8"
    quote_char: "\""
    escape_char: "\\"

  # JSON configuration
  json:
    path: "/data/source.json"
    extraction_path: "$.records[*]"
    schema: "schema.json"
    date_format: "ISO8601"

  # Database configuration
  database:
    type: "postgresql"
    host: "localhost"
    port: 5432
    database: "source_db"
    schema: "public"
    username: "user"
    password: "****"
    connection_pool:
      min_size: 5
      max_size: 20
      timeout_seconds: 30
```

### Connection Pool Configuration
```yaml
connection_pool:
  # Pool settings
  min_connections: 5
  max_connections: 20
  idle_timeout: 300
  connection_timeout: 30

  # Load balancing
  load_balancing:
    strategy: "round_robin"
    health_check_interval: 60

  # Connection validation
  validation:
    query: "SELECT 1"
    timeout: 5
    retry_attempts: 3
```

## Error Handling

### Extraction Errors
```cpp
/**
 * @brief Extraction error types
 */
enum class ExtractionErrorType {
    CONNECTION_ERROR,     ///< Connection related errors
    PARSING_ERROR,        ///< Data parsing errors
    VALIDATION_ERROR,     ///< Data validation errors
    FORMAT_ERROR,         ///< Format related errors
    RESOURCE_ERROR        ///< Resource related errors
};

/**
 * @brief Extraction error information
 */
struct ExtractionError {
    ExtractionErrorType type;    ///< Error type
    std::string source;          ///< Error source
    std::string message;         ///< Error message
    std::time_t timestamp;       ///< Error timestamp
};
```

## Performance Optimization

### Extraction Caching
```cpp
/**
 * @brief Extraction cache configuration
 */
struct ExtractionCacheConfig {
    size_t max_size;           ///< Maximum cache size
    size_t ttl_seconds;        ///< Time to live in seconds
    bool enable_compression;   ///< Enable cache compression
};

/**
 * @brief Extraction cache metrics
 */
struct ExtractionCacheMetrics {
    size_t hits;               ///< Cache hits
    size_t misses;             ///< Cache misses
    size_t evictions;          ///< Cache evictions
    double hit_ratio;          ///< Cache hit ratio
};
```

### Batch Processing
```cpp
/**
 * @brief Batch processing configuration
 */
struct BatchConfig {
    size_t batch_size;         ///< Number of records per batch
    size_t max_retries;        ///< Maximum retry attempts
    size_t retry_delay_ms;     ///< Delay between retries
    bool parallel_processing;  ///< Enable parallel processing
};
``` 