# CDM Library Documentation

## Overview
The CDM Library provides the OMOP Common Data Model (CDM) implementation for the ETL Pipeline. It defines the data model, table structures, and relationships according to the OMOP CDM specification.

## Components

### Table Definitions
Core table definitions and schema management.

#### Class: TableDefinitions
```cpp
/**
 * @brief Manages OMOP CDM table definitions
 * 
 * Provides:
 * - Table schema definitions
 * - Field definitions
 * - Relationship management
 * - Constraint definitions
 */
class TableDefinitions {
public:
    /**
     * @brief Gets table definition
     * @param table_name Table name
     * @return Table definition
     */
    TableDefinition getTableDefinition(const std::string& table_name);

    /**
     * @brief Gets field definition
     * @param table_name Table name
     * @param field_name Field name
     * @return Field definition
     */
    FieldDefinition getFieldDefinition(const std::string& table_name, const std::string& field_name);

    /**
     * @brief Validates table schema
     * @param table_name Table name
     * @return true if valid
     */
    bool validateTableSchema(const std::string& table_name);

private:
    /**
     * @brief Loads table definitions
     */
    void loadTableDefinitions();

    /**
     * @brief Validates field definitions
     * @param table_name Table name
     * @return true if valid
     */
    bool validateFieldDefinitions(const std::string& table_name);
};
```

### OMOP Tables
Implementation of OMOP CDM tables.

#### Class: OmopTables
```cpp
/**
 * @brief Implements OMOP CDM tables
 * 
 * Features:
 * - Table creation
 * - Data validation
 * - Relationship management
 * - Constraint enforcement
 */
class OmopTables {
public:
    /**
     * @brief Creates OMOP tables
     * @param connection Database connection
     */
    void createTables(std::shared_ptr<DatabaseConnector> connection);

    /**
     * @brief Validates table data
     * @param table_name Table name
     * @return Validation result
     */
    ValidationResult validateTableData(const std::string& table_name);

    /**
     * @brief Gets table relationships
     * @param table_name Table name
     * @return Table relationships
     */
    std::vector<TableRelationship> getTableRelationships(const std::string& table_name);

private:
    /**
     * @brief Creates table constraints
     * @param table_name Table name
     */
    void createTableConstraints(const std::string& table_name);

    /**
     * @brief Validates table relationships
     * @param table_name Table name
     * @return true if valid
     */
    bool validateTableRelationships(const std::string& table_name);
};
```

## Table Definitions

### Person Table
```cpp
/**
 * @brief Person table definition
 */
struct PersonTable {
    // Primary key
    int64_t person_id;              ///< Unique identifier for each person
    
    // Demographics
    int gender_concept_id;          ///< Gender concept identifier
    int year_of_birth;              ///< Year of birth
    int month_of_birth;             ///< Month of birth
    int day_of_birth;               ///< Day of birth
    int birth_datetime;             ///< Birth datetime
    int race_concept_id;            ///< Race concept identifier
    int ethnicity_concept_id;       ///< Ethnicity concept identifier
    int location_id;                ///< Location identifier
    int provider_id;                ///< Provider identifier
    int care_site_id;               ///< Care site identifier
    
    // Additional fields
    std::string person_source_value; ///< Source value for person
    int gender_source_concept_id;    ///< Source concept for gender
    int race_source_concept_id;      ///< Source concept for race
    int ethnicity_source_concept_id; ///< Source concept for ethnicity
};
```

### Observation Period Table
```cpp
/**
 * @brief Observation period table definition
 */
struct ObservationPeriodTable {
    // Primary key
    int64_t observation_period_id;   ///< Unique identifier for observation period
    
    // Foreign keys
    int64_t person_id;              ///< Person identifier
    int period_type_concept_id;     ///< Period type concept identifier
    
    // Period information
    Date start_date;                ///< Start date of observation period
    Date end_date;                  ///< End date of observation period
};
```

### Visit Occurrence Table
```cpp
/**
 * @brief Visit occurrence table definition
 */
struct VisitOccurrenceTable {
    // Primary key
    int64_t visit_occurrence_id;    ///< Unique identifier for visit
    
    // Foreign keys
    int64_t person_id;              ///< Person identifier
    int visit_concept_id;           ///< Visit concept identifier
    int visit_type_concept_id;      ///< Visit type concept identifier
    int provider_id;                ///< Provider identifier
    int care_site_id;               ///< Care site identifier
    
    // Visit information
    Date visit_start_date;          ///< Visit start date
    DateTime visit_start_datetime;   ///< Visit start datetime
    Date visit_end_date;            ///< Visit end date
    DateTime visit_end_datetime;     ///< Visit end datetime
    
    // Source information
    std::string visit_source_value; ///< Source value for visit
    int visit_source_concept_id;    ///< Source concept for visit
};
```

## Configuration

### CDM Configuration
```yaml
cdm:
  # General settings
  version: "5.4"
  schema: "omop_cdm"
  
  # Table configuration
  tables:
    person:
      enabled: true
      constraints:
        primary_key: ["person_id"]
        foreign_keys:
          - field: "gender_concept_id"
            reference: "concept.concept_id"
          - field: "race_concept_id"
            reference: "concept.concept_id"
    
    observation_period:
      enabled: true
      constraints:
        primary_key: ["observation_period_id"]
        foreign_keys:
          - field: "person_id"
            reference: "person.person_id"
    
    visit_occurrence:
      enabled: true
      constraints:
        primary_key: ["visit_occurrence_id"]
        foreign_keys:
          - field: "person_id"
            reference: "person.person_id"
          - field: "visit_concept_id"
            reference: "concept.concept_id"
```

### Validation Configuration
```yaml
validation:
  # General validation settings
  mode: "strict"
  error_threshold: 0.01
  
  # Table validation rules
  tables:
    person:
      required_fields:
        - "person_id"
        - "gender_concept_id"
        - "year_of_birth"
      value_ranges:
        year_of_birth:
          min: 1900
          max: "current"
    
    observation_period:
      required_fields:
        - "observation_period_id"
        - "person_id"
        - "start_date"
        - "end_date"
      date_rules:
        - rule: "start_date <= end_date"
    
    visit_occurrence:
      required_fields:
        - "visit_occurrence_id"
        - "person_id"
        - "visit_concept_id"
        - "visit_start_date"
      date_rules:
        - rule: "visit_start_date <= visit_end_date"
```

## Error Handling

### CDM Errors
```cpp
/**
 * @brief CDM error types
 */
enum class CdmErrorType {
    SCHEMA_ERROR,        ///< Schema related errors
    CONSTRAINT_ERROR,    ///< Constraint violation errors
    RELATIONSHIP_ERROR,  ///< Relationship violation errors
    DATA_ERROR,          ///< Data validation errors
    INTEGRITY_ERROR      ///< Data integrity errors
};

/**
 * @brief CDM error information
 */
struct CdmError {
    CdmErrorType type;       ///< Error type
    std::string table;       ///< Table where error occurred
    std::string field;       ///< Field where error occurred
    std::string message;     ///< Error message
    std::time_t timestamp;   ///< Error timestamp
};
```

## Performance Optimization

### Table Indexing
```cpp
/**
 * @brief Table index configuration
 */
struct TableIndexConfig {
    std::string table_name;          ///< Table name
    std::vector<std::string> fields; ///< Fields to index
    bool unique;                     ///< Whether index is unique
    std::string index_type;          ///< Index type
};

/**
 * @brief Index metrics
 */
struct IndexMetrics {
    size_t total_indexes;            ///< Total number of indexes
    size_t index_size_bytes;         ///< Total index size
    double index_usage_ratio;        ///< Index usage ratio
    size_t index_scans;              ///< Number of index scans
};
```

### Data Partitioning
```cpp
/**
 * @brief Partition configuration
 */
struct PartitionConfig {
    std::string table_name;          ///< Table name
    std::string partition_key;       ///< Partition key
    std::string partition_type;      ///< Partition type
    size_t partition_size;           ///< Partition size
};

/**
 * @brief Partition metrics
 */
struct PartitionMetrics {
    size_t total_partitions;         ///< Total number of partitions
    size_t partition_size_bytes;     ///< Total partition size
    double partition_usage_ratio;    ///< Partition usage ratio
    size_t partition_scans;          ///< Number of partition scans
};
``` 