# Load Library Documentation

## Overview
The Load Library provides data loading capabilities for the OMOP ETL Pipeline. It implements various loaders for different target systems, with support for batch processing, transaction management, and error handling.

## Components

### Base Loader
The foundation for all loader implementations.

#### Class: LoaderBase
```cpp
/**
 * @brief Base class for all data loaders
 * 
 * Provides common functionality for:
 * - Target system connection
 * - Record loading
 * - Batch processing
 * - Error handling
 */
class LoaderBase {
public:
    /**
     * @brief Initializes the loader
     * @param config Loader configuration
     */
    virtual void initialize(const LoaderConfig& config) = 0;

    /**
     * @brief Loads a record into target system
     * @param record Record to load
     * @return Load result
     */
    virtual LoadResult loadRecord(const Record& record) = 0;

    /**
     * @brief Loads a batch of records
     * @param records Records to load
     * @return Batch load result
     */
    virtual BatchLoadResult loadBatch(const std::vector<Record>& records) = 0;

protected:
    /**
     * @brief Validates loader configuration
     * @param config Configuration to validate
     * @return true if valid
     */
    virtual bool validateConfig(const LoaderConfig& config) = 0;

    /**
     * @brief Handles loading errors
     * @param error Error information
     */
    virtual void handleError(const LoadingError& error) = 0;
};
```

### Database Loader
Handles loading data into database systems.

#### Class: DatabaseLoader
```cpp
/**
 * @brief Loads data into database systems
 * 
 * Features:
 * - Database connection management
 * - SQL statement generation
 * - Transaction handling
 * - Batch processing
 */
class DatabaseLoader : public LoaderBase {
public:
    /**
     * @brief Sets database connection
     * @param connection Database connection
     */
    void setConnection(std::shared_ptr<DatabaseConnector> connection);

    /**
     * @brief Begins loading transaction
     */
    void beginTransaction();

    /**
     * @brief Commits loading transaction
     */
    void commitTransaction();

private:
    /**
     * @brief Generates SQL for record
     * @param record Record to generate SQL for
     * @return Generated SQL
     */
    std::string generateSql(const Record& record);

    /**
     * @brief Validates database schema
     * @return true if valid
     */
    bool validateSchema();
};
```

### Batch Loader
Manages batch loading operations.

#### Class: BatchLoader
```cpp
/**
 * @brief Manages batch loading operations
 * 
 * Features:
 * - Batch size management
 * - Parallel processing
 * - Error recovery
 * - Progress tracking
 */
class BatchLoader {
public:
    /**
     * @brief Sets batch configuration
     * @param config Batch configuration
     */
    void setBatchConfig(const BatchConfig& config);

    /**
     * @brief Loads batch of records
     * @param records Records to load
     * @return Batch result
     */
    BatchResult loadBatch(const std::vector<Record>& records);

    /**
     * @brief Gets batch statistics
     * @return Batch statistics
     */
    BatchStatistics getStatistics() const;

private:
    /**
     * @brief Processes batch in parallel
     * @param records Records to process
     * @return Processing result
     */
    BatchResult processParallel(const std::vector<Record>& records);

    /**
     * @brief Handles batch errors
     * @param error Batch error
     */
    void handleBatchError(const BatchError& error);
};
```

### Additional Loaders
Specialized loaders for specific target systems.

#### Class: AdditionalLoaders
```cpp
/**
 * @brief Additional specialized loaders
 * 
 * Provides:
 * - File system loading
 * - API endpoint loading
 * - Message queue loading
 * - Custom target loading
 */
class AdditionalLoaders {
public:
    /**
     * @brief Creates file system loader
     * @param config File system configuration
     * @return File system loader
     */
    std::shared_ptr<LoaderBase> createFileSystemLoader(const FileSystemConfig& config);

    /**
     * @brief Creates API loader
     * @param config API configuration
     * @return API loader
     */
    std::shared_ptr<LoaderBase> createApiLoader(const ApiConfig& config);

    /**
     * @brief Creates message queue loader
     * @param config Queue configuration
     * @return Queue loader
     */
    std::shared_ptr<LoaderBase> createQueueLoader(const QueueConfig& config);
};
```

## Configuration

### Loader Configuration
```yaml
load:
  # General settings
  type: "database"
  batch_size: 1000
  error_handling: "strict"

  # Database configuration
  database:
    type: "postgresql"
    host: "localhost"
    port: 5432
    database: "omop_cdm"
    schema: "public"
    username: "user"
    password: "****"
    connection_pool:
      min_size: 5
      max_size: 20
      timeout_seconds: 30

  # Batch processing
  batch:
    size: 1000
    parallel_processing: true
    max_retries: 3
    retry_delay_ms: 1000

  # Error handling
  error_handling:
    mode: "strict"
    error_threshold: 0.01
    error_notification: "email"
```

### Batch Configuration
```yaml
batch:
  # Batch settings
  size: 1000
  parallel_processing: true
  max_retries: 3
  retry_delay_ms: 1000

  # Performance optimization
  optimization:
    compression: true
    buffer_size: 8192
    prefetch: true

  # Error handling
  error_handling:
    mode: "retry"
    max_retries: 3
    retry_delay_ms: 1000
```

## Error Handling

### Loading Errors
```cpp
/**
 * @brief Loading error types
 */
enum class LoadingErrorType {
    CONNECTION_ERROR,     ///< Connection related errors
    VALIDATION_ERROR,     ///< Data validation errors
    SCHEMA_ERROR,         ///< Schema related errors
    BATCH_ERROR,          ///< Batch processing errors
    RESOURCE_ERROR        ///< Resource related errors
};

/**
 * @brief Loading error information
 */
struct LoadingError {
    LoadingErrorType type;    ///< Error type
    std::string target;       ///< Target system
    std::string message;      ///< Error message
    std::time_t timestamp;    ///< Error timestamp
};
```

## Performance Optimization

### Batch Processing
```cpp
/**
 * @brief Batch processing configuration
 */
struct BatchProcessingConfig {
    size_t batch_size;         ///< Number of records per batch
    bool parallel_processing;  ///< Enable parallel processing
    size_t max_retries;        ///< Maximum retry attempts
    size_t retry_delay_ms;     ///< Delay between retries
    bool compression;          ///< Enable compression
    size_t buffer_size;        ///< Buffer size
    bool prefetch;             ///< Enable prefetching
};

/**
 * @brief Batch processing metrics
 */
struct BatchProcessingMetrics {
    size_t total_batches;      ///< Total number of batches
    size_t successful_batches; ///< Number of successful batches
    size_t failed_batches;     ///< Number of failed batches
    double avg_batch_time;     ///< Average batch processing time
    size_t peak_memory_usage;  ///< Peak memory usage
};
```

### Transaction Management
```cpp
/**
 * @brief Transaction configuration
 */
struct TransactionConfig {
    bool auto_commit;          ///< Enable auto-commit
    size_t commit_interval;    ///< Commit interval in records
    bool rollback_on_error;    ///< Enable rollback on error
    size_t timeout_seconds;    ///< Transaction timeout
};

/**
 * @brief Transaction metrics
 */
struct TransactionMetrics {
    size_t total_transactions; ///< Total number of transactions
    size_t committed;          ///< Number of committed transactions
    size_t rolled_back;        ///< Number of rolled back transactions
    double avg_duration;       ///< Average transaction duration
};
``` 