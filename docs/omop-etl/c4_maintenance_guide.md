# C4 Documentation Maintenance Guide

## Overview
This guide provides comprehensive instructions for maintaining C4 documentation in the OMOP ETL Pipeline project. It covers documentation standards, update procedures, and validation requirements.

## Documentation Standards

### 1. Code Documentation
All C++ components must include C4 documentation using the following tags:

```cpp
/**
 * @c4component
 * @c4name ComponentName
 * @c4description Detailed description
 * @c4responsibilities
 *   - Responsibility 1
 *   - Responsibility 2
 * @c4dependencies
 *   - Dependency1: Purpose
 * @c4config ConfigName
 * @c4container ContainerName
 * @c4context ContextName
 */
```

### 2. Configuration Documentation
Configuration files must include C4 references:

```yaml
# @c4config ConfigName
# @c4component ComponentName
config:
  field1: value1
  field2: value2
```

### 3. Markdown Documentation
All markdown documentation must include:

```markdown
## Overview
Brief description of the component or system.

## Components
Detailed component descriptions.

## Configuration
Configuration options and examples.

## Usage Examples
Common usage patterns and examples.
```

## Update Procedures

### 1. Adding New Components
1. Create component files using the template:
   ```bash
   cp docs/templates/c4_code_template.h src/lib/component_name.h
   cp docs/templates/c4_code_template.cpp src/lib/component_name.cpp
   ```

2. Update the component documentation:
   - Add C4 tags
   - Document responsibilities
   - List dependencies
   - Define configuration

3. Update the traceability matrix:
   ```bash
   ./scripts/generate_c4_docs.sh --update
   ```

### 2. Modifying Existing Components
1. Update component documentation:
   - Modify C4 tags
   - Update responsibilities
   - Update dependencies
   - Update configuration

2. Validate changes:
   ```bash
   ./scripts/validate_c4_docs.sh --strict
   ```

3. Update the traceability matrix:
   ```bash
   ./scripts/generate_c4_docs.sh --update
   ```

### 3. Adding New Documentation
1. Create documentation file:
   ```bash
   touch docs/omop-etl/component_name.md
   ```

2. Add required sections:
   - Overview
   - Components
   - Configuration
   - Usage Examples

3. Validate documentation:
   ```bash
   ./scripts/validate_c4_docs.sh
   ```

## Validation Requirements

### 1. Required Tags
- @c4component
- @c4name
- @c4description
- @c4responsibilities
- @c4dependencies
- @c4config

### 2. Required Sections
- Overview
- Components
- Configuration
- Usage Examples

### 3. Validation Rules
- Component names must be PascalCase
- Descriptions must be at least 10 characters
- All links must be valid markdown
- All configuration must be documented

## Automation

### 1. Documentation Generation
```bash
# Generate documentation
./scripts/generate_c4_docs.sh --generate

# Update traceability matrix
./scripts/generate_c4_docs.sh --update
```

### 2. Documentation Validation
```bash
# Validate documentation
./scripts/validate_c4_docs.sh

# Strict validation
./scripts/validate_c4_docs.sh --strict
```

### 3. CI/CD Integration
The project includes GitHub Actions workflows for:
- Automatic documentation validation
- Documentation generation
- Traceability matrix updates
- Documentation artifact creation

## Best Practices

### 1. Documentation Updates
- Update documentation with code changes
- Keep documentation close to implementation
- Use consistent terminology
- Include examples

### 2. Code Organization
- Group related components
- Use clear naming conventions
- Maintain consistent structure
- Document dependencies

### 3. Configuration Management
- Document all configuration options
- Provide default values
- Include validation rules
- Document environment variables

## Troubleshooting

### 1. Common Issues
1. Missing C4 tags:
   ```bash
   ./scripts/validate_c4_docs.sh
   ```

2. Invalid component names:
   - Use PascalCase
   - Avoid special characters
   - Keep names descriptive

3. Invalid documentation structure:
   - Follow the template
   - Include all required sections
   - Validate markdown

### 2. Validation Errors
1. Missing tags:
   - Add required C4 tags
   - Update documentation
   - Run validation

2. Invalid format:
   - Check component names
   - Validate descriptions
   - Fix markdown

3. Missing sections:
   - Add required sections
   - Update documentation
   - Run validation

## Maintenance Schedule

### 1. Daily Tasks
- Review documentation changes
- Validate new components
- Update traceability matrix

### 2. Weekly Tasks
- Review documentation quality
- Update examples
- Check for broken links

### 3. Monthly Tasks
- Review documentation structure
- Update templates
- Validate all documentation

## Resources

### 1. Templates
- Code template: `docs/templates/c4_code_template.h`
- Documentation template: `docs/templates/c4_doc_template.md`
- Configuration template: `docs/templates/c4_config_template.yaml`

### 2. Scripts
- Documentation generation: `scripts/generate_c4_docs.sh`
- Documentation validation: `scripts/validate_c4_docs.sh`
- Traceability update: `scripts/update_traceability.sh`

### 3. Documentation
- C4 Model: [C4 Model Documentation](https://c4model.com/)
- Markdown Guide: [Markdown Guide](https://www.markdownguide.org/)
- Doxygen Guide: [Doxygen Manual](https://www.doxygen.nl/manual/index.html) 