# Transform Library Documentation

## Overview
The Transform Library provides the data transformation capabilities for the OMOP ETL Pipeline. It implements various transformation engines, field transformations, and validation mechanisms to convert source data into OMOP CDM format.

## Components

### Transformation Engine
The central component that orchestrates data transformations.

#### Class: TransformationEngine
```cpp
/**
 * @brief Main transformation engine that orchestrates data transformations
 * 
 * The TransformationEngine:
 * - Manages transformation rules
 * - Coordinates field transformations
 * - Handles data validation
 * - Manages transformation context
 */
class TransformationEngine {
public:
    /**
     * @brief Initializes the transformation engine
     * @param config Transformation configuration
     */
    void initialize(const TransformConfig& config);

    /**
     * @brief Transforms a record according to configured rules
     * @param record Record to transform
     * @return Transformed record
     */
    Record transformRecord(const Record& record);

    /**
     * @brief Registers a transformation rule
     * @param rule Transformation rule to register
     */
    void registerRule(const TransformationRule& rule);

private:
    /**
     * @brief Applies transformation rules to a record
     * @param record Record to transform
     * @param rules Rules to apply
     */
    void applyRules(Record& record, const std::vector<TransformationRule>& rules);

    /**
     * @brief Validates transformed record
     * @param record Record to validate
     * @return Validation result
     */
    ValidationResult validateTransformedRecord(const Record& record);
};
```

### Field Transformations
Specialized transformations for different field types.

#### Class: FieldTransformation
```cpp
/**
 * @brief Base class for field transformations
 * 
 * Provides common functionality for:
 * - Field value transformation
 * - Type conversion
 * - Validation
 */
class FieldTransformation {
public:
    /**
     * @brief Transforms a field value
     * @param value Value to transform
     * @return Transformed value
     */
    virtual FieldValue transform(const FieldValue& value) = 0;

    /**
     * @brief Validates a field value
     * @param value Value to validate
     * @return true if valid
     */
    virtual bool validate(const FieldValue& value) = 0;

protected:
    /**
     * @brief Converts value to target type
     * @param value Value to convert
     * @param target_type Target type
     * @return Converted value
     */
    FieldValue convertToType(const FieldValue& value, FieldType target_type);
};
```

### Specialized Transformations

#### String Transformations
```cpp
/**
 * @brief String field transformations
 * 
 * Handles:
 * - String formatting
 * - Case conversion
 * - Pattern matching
 * - String validation
 */
class StringTransformation : public FieldTransformation {
public:
    /**
     * @brief Applies string formatting
     * @param value String value
     * @param format Format specification
     * @return Formatted string
     */
    std::string formatString(const std::string& value, const StringFormat& format);

    /**
     * @brief Validates string pattern
     * @param value String value
     * @param pattern Regex pattern
     * @return true if matches pattern
     */
    bool validatePattern(const std::string& value, const std::string& pattern);
};
```

#### Date Transformations
```cpp
/**
 * @brief Date field transformations
 * 
 * Handles:
 * - Date parsing
 * - Format conversion
 * - Date validation
 * - Time zone handling
 */
class DateTransformation : public FieldTransformation {
public:
    /**
     * @brief Parses date string
     * @param date_str Date string
     * @param format Date format
     * @return Parsed date
     */
    Date parseDate(const std::string& date_str, const DateFormat& format);

    /**
     * @brief Converts date format
     * @param date Date to convert
     * @param target_format Target format
     * @return Formatted date string
     */
    std::string convertDateFormat(const Date& date, const DateFormat& target_format);
};
```

#### Numeric Transformations
```cpp
/**
 * @brief Numeric field transformations
 * 
 * Handles:
 * - Number formatting
 * - Unit conversion
 * - Range validation
 * - Precision handling
 */
class NumericTransformation : public FieldTransformation {
public:
    /**
     * @brief Converts number format
     * @param value Number value
     * @param format Format specification
     * @return Formatted number
     */
    std::string formatNumber(double value, const NumberFormat& format);

    /**
     * @brief Validates number range
     * @param value Number value
     * @param min Minimum value
     * @param max Maximum value
     * @return true if in range
     */
    bool validateRange(double value, double min, double max);
};
```

### Vocabulary Service
Handles vocabulary mapping and standardization.

#### Class: VocabularyService
```cpp
/**
 * @brief Service for vocabulary mapping and standardization
 * 
 * Provides:
 * - Concept mapping
 * - Vocabulary validation
 * - Standard code lookup
 * - Relationship management
 */
class VocabularyService {
public:
    /**
     * @brief Maps source code to standard concept
     * @param source_code Source code
     * @param vocabulary_id Vocabulary identifier
     * @return Mapped concept
     */
    Concept mapToStandardConcept(const std::string& source_code, const std::string& vocabulary_id);

    /**
     * @brief Validates concept existence
     * @param concept_id Concept identifier
     * @return true if valid
     */
    bool validateConcept(int concept_id);

private:
    /**
     * @brief Loads vocabulary data
     * @param vocabulary_id Vocabulary identifier
     */
    void loadVocabulary(const std::string& vocabulary_id);
};
```

## Configuration

### Transformation Configuration
```yaml
transform:
  # General settings
  engine:
    type: "standard"
    batch_size: 1000
    validation_mode: "strict"

  # Field transformations
  fields:
    - name: "patient_id"
      type: "string"
      transformations:
        - type: "format"
          format: "PREFIX-{value}"
        - type: "validate"
          pattern: "^PREFIX-\\d+$"

    - name: "birth_date"
      type: "date"
      transformations:
        - type: "parse"
          format: "YYYY-MM-DD"
        - type: "validate"
          range:
            min: "1900-01-01"
            max: "current"

    - name: "weight"
      type: "numeric"
      transformations:
        - type: "convert"
          from_unit: "kg"
          to_unit: "g"
        - type: "validate"
          range:
            min: 0
            max: 1000

  # Vocabulary mapping
  vocabulary:
    source_vocabularies:
      - id: "ICD10"
        version: "2023"
      - id: "SNOMED"
        version: "2023"
    
    mapping_rules:
      - source: "ICD10"
        target: "SNOMED"
        mapping_type: "equivalent"
```

### Validation Configuration
```yaml
validation:
  # General validation settings
  mode: "strict"
  error_threshold: 0.01

  # Field validation rules
  fields:
    - name: "patient_id"
      required: true
      unique: true
      format: "^PREFIX-\\d+$"

    - name: "birth_date"
      required: true
      format: "YYYY-MM-DD"
      range:
        min: "1900-01-01"
        max: "current"

  # Cross-field validation
  cross_field:
    - fields: ["start_date", "end_date"]
      rule: "start_date <= end_date"
    
    - fields: ["birth_date", "death_date"]
      rule: "birth_date <= death_date"
```

## Error Handling

### Transformation Errors
```cpp
/**
 * @brief Transformation error types
 */
enum class TransformationErrorType {
    FORMAT_ERROR,           ///< Format conversion error
    VALIDATION_ERROR,       ///< Validation error
    VOCABULARY_ERROR,       ///< Vocabulary mapping error
    TYPE_CONVERSION_ERROR,  ///< Type conversion error
    RULE_ERROR             ///< Rule application error
};

/**
 * @brief Transformation error information
 */
struct TransformationError {
    TransformationErrorType type;    ///< Error type
    std::string field;              ///< Field where error occurred
    std::string message;            ///< Error message
    std::string value;              ///< Value that caused error
};
```

## Performance Optimization

### Transformation Caching
```cpp
/**
 * @brief Transformation cache configuration
 */
struct TransformationCacheConfig {
    size_t max_size;           ///< Maximum cache size
    size_t ttl_seconds;        ///< Time to live in seconds
    bool enable_compression;   ///< Enable cache compression
};

/**
 * @brief Transformation cache metrics
 */
struct TransformationCacheMetrics {
    size_t hits;               ///< Cache hits
    size_t misses;             ///< Cache misses
    size_t evictions;          ///< Cache evictions
    double hit_ratio;          ///< Cache hit ratio
};
``` 