# Core Library Documentation

## Overview
The Core Library provides the fundamental components and interfaces for the OMOP ETL Pipeline. It implements the core pipeline architecture, job management, and data processing infrastructure.

## Components

### Pipeline
The central orchestration component that manages the ETL workflow.

#### Class: Pipeline
```cpp
/**
 * @brief Main pipeline class that orchestrates the ETL process
 * 
 * The Pipeline class is responsible for:
 * - Managing the ETL workflow
 * - Coordinating between extract, transform, and load components
 * - Handling job execution and monitoring
 * - Managing component lifecycle
 * - Error handling and recovery
 */
class Pipeline {
public:
    /**
     * @brief Initializes the pipeline with configuration
     * @param config Pipeline configuration object
     * @throws PipelineException if initialization fails
     */
    void initialize(const PipelineConfig& config);

    /**
     * @brief Executes the ETL pipeline
     * @param job_config Job configuration parameters
     * @return JobResult containing execution status and metrics
     */
    JobResult execute(const JobConfig& job_config);

    /**
     * @brief Registers a component with the pipeline
     * @param component Component to register
     * @param type Component type (extract, transform, load)
     */
    void registerComponent(std::shared_ptr<IComponent> component, ComponentType type);

private:
    /**
     * @brief Validates pipeline configuration
     * @param config Configuration to validate
     * @return true if valid, false otherwise
     */
    bool validateConfig(const PipelineConfig& config);

    /**
     * @brief Handles pipeline errors
     * @param error Error information
     * @param context Error context
     */
    void handleError(const PipelineError& error, const ErrorContext& context);
};
```

### Job Management
Components responsible for job scheduling, execution, and monitoring.

#### Class: JobManager
```cpp
/**
 * @brief Manages ETL jobs and their execution
 * 
 * The JobManager handles:
 * - Job scheduling and prioritization
 * - Resource allocation
 * - Job state management
 * - Progress monitoring
 * - Error handling and recovery
 */
class JobManager {
public:
    /**
     * @brief Creates a new ETL job
     * @param config Job configuration
     * @return JobId of the created job
     */
    JobId createJob(const JobConfig& config);

    /**
     * @brief Schedules a job for execution
     * @param job_id ID of the job to schedule
     * @param priority Job priority
     */
    void scheduleJob(JobId job_id, JobPriority priority);

    /**
     * @brief Gets the current status of a job
     * @param job_id ID of the job
     * @return JobStatus containing current state and metrics
     */
    JobStatus getJobStatus(JobId job_id);

private:
    /**
     * @brief Allocates resources for a job
     * @param job_id ID of the job
     * @return ResourceAllocation containing allocated resources
     */
    ResourceAllocation allocateResources(JobId job_id);

    /**
     * @brief Monitors job progress
     * @param job_id ID of the job
     */
    void monitorProgress(JobId job_id);
};
```

### Component Factory
Factory for creating and managing pipeline components.

#### Class: ComponentFactory
```cpp
/**
 * @brief Factory for creating pipeline components
 * 
 * The ComponentFactory:
 * - Creates components based on configuration
 * - Manages component lifecycle
 * - Handles component dependencies
 * - Provides component configuration
 */
class ComponentFactory {
public:
    /**
     * @brief Creates a new component
     * @param type Type of component to create
     * @param config Component configuration
     * @return Shared pointer to created component
     */
    std::shared_ptr<IComponent> createComponent(ComponentType type, const ComponentConfig& config);

    /**
     * @brief Registers a component type
     * @param type Component type
     * @param creator Component creator function
     */
    void registerComponentType(ComponentType type, ComponentCreator creator);

private:
    /**
     * @brief Validates component configuration
     * @param config Configuration to validate
     * @return true if valid, false otherwise
     */
    bool validateComponentConfig(const ComponentConfig& config);
};
```

### Record Management
Handles data record processing and transformation.

#### Class: Record
```cpp
/**
 * @brief Represents a data record in the pipeline
 * 
 * The Record class:
 * - Stores field values
 * - Provides field access
 * - Handles data type conversion
 * - Manages record metadata
 */
class Record {
public:
    /**
     * @brief Gets a field value
     * @param field_name Name of the field
     * @return Field value
     */
    FieldValue getField(const std::string& field_name) const;

    /**
     * @brief Sets a field value
     * @param field_name Name of the field
     * @param value Value to set
     */
    void setField(const std::string& field_name, const FieldValue& value);

    /**
     * @brief Validates record data
     * @return true if valid, false otherwise
     */
    bool validate() const;

private:
    /**
     * @brief Converts field value to specified type
     * @param value Value to convert
     * @param target_type Target type
     * @return Converted value
     */
    FieldValue convertValue(const FieldValue& value, FieldType target_type);
};
```

## Configuration

### Pipeline Configuration
```yaml
pipeline:
  # General pipeline settings
  name: "OMOP ETL Pipeline"
  version: "1.0.0"
  
  # Component configuration
  components:
    extract:
      type: "csv"
      config:
        path: "/data/source"
        format: "custom"
    
    transform:
      type: "standard"
      config:
        rules_file: "transform_rules.yaml"
    
    load:
      type: "postgresql"
      config:
        connection: "db_connection.yaml"
        batch_size: 1000

  # Job management settings
  job_management:
    max_concurrent_jobs: 4
    resource_limits:
      memory_mb: 4096
      cpu_cores: 2
    
  # Error handling
  error_handling:
    retry_attempts: 3
    retry_delay_seconds: 60
    error_notification: "email"
```

### Job Configuration
```yaml
job:
  # Job identification
  id: "job_001"
  name: "Daily ETL Job"
  
  # Job parameters
  parameters:
    source_data: "patient_records.csv"
    target_schema: "omop_cdm"
    
  # Scheduling
  schedule:
    type: "daily"
    time: "02:00"
    
  # Resource allocation
  resources:
    memory_mb: 1024
    cpu_cores: 1
    
  # Error handling
  error_handling:
    retry_attempts: 3
    retry_delay_seconds: 30
```

## Error Handling

### Error Types
```cpp
/**
 * @brief Pipeline error types
 */
enum class PipelineErrorType {
    CONFIGURATION_ERROR,    ///< Configuration related errors
    COMPONENT_ERROR,        ///< Component related errors
    EXECUTION_ERROR,        ///< Execution related errors
    RESOURCE_ERROR,         ///< Resource related errors
    VALIDATION_ERROR        ///< Validation related errors
};

/**
 * @brief Error context information
 */
struct ErrorContext {
    std::string component;      ///< Component where error occurred
    std::string operation;      ///< Operation being performed
    std::string details;        ///< Detailed error information
    std::time_t timestamp;      ///< Error timestamp
};
```

## Performance Monitoring

### Metrics
```cpp
/**
 * @brief Pipeline performance metrics
 */
struct PipelineMetrics {
    // Job metrics
    size_t total_jobs;          ///< Total number of jobs
    size_t completed_jobs;      ///< Number of completed jobs
    size_t failed_jobs;         ///< Number of failed jobs
    
    // Performance metrics
    double avg_job_duration;    ///< Average job duration
    double avg_throughput;      ///< Average records per second
    size_t peak_memory_usage;   ///< Peak memory usage in bytes
    
    // Resource utilization
    double cpu_utilization;     ///< CPU utilization percentage
    double memory_utilization;  ///< Memory utilization percentage
};
``` 