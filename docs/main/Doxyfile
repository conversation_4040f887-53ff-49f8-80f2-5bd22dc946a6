# Project information
PROJECT_NAME           = "OMOP ETL Pipeline"
PROJECT_NUMBER         = 1.0
PROJECT_BRIEF         = "A robust C++ implementation of an OMOP CDM pipeline"
OUTPUT_DIRECTORY      = docs/main
CREATE_SUBDIRS        = YES
OUTPUT_LANGUAGE       = English
BRIEF_MEMBER_DESC     = YES
REPEAT_BRIEF          = YES
ABBREVIATE_BRIEF      = "The $name class" \
                       "The $name widget" \
                       "The $name file" \
                       is \
                       provides \
                       specifies \
                       contains \
                       represents \
                       a \
                       an \
                       the

# Input settings
INPUT                 = src
FILE_PATTERNS         = *.h *.hpp *.cpp
RECURSIVE             = YES
EXTRACT_ALL           = YES
EXTRACT_PRIVATE       = YES
EXTRACT_STATIC        = YES
EXTRACT_LOCAL_CLASSES = YES

# Output settings
GENERATE_HTML         = YES
GENERATE_LATEX        = NO
HTML_OUTPUT          = html
HTML_FILE_EXTENSION  = .html
HTML_HEADER          = 
HTML_FOOTER          = 
HTML_STYLESHEET      = 
HTML_EXTRA_STYLESHEET = 
HTML_EXTRA_FILES     = 
HTML_COLORSTYLE_HUE  = 220
HTML_COLORSTYLE_SAT  = 100
HTML_COLORSTYLE_GAMMA = 80
HTML_TIMESTAMP       = YES
HTML_DYNAMIC_MENUS   = YES
HTML_DYNAMIC_SECTIONS = NO

# Diagram settings
HAVE_DOT             = YES
CLASS_DIAGRAMS       = YES
HIDE_UNDOC_RELATIONS = YES
CLASS_GRAPH          = YES
COLLABORATION_GRAPH  = YES
UML_LOOK            = YES
UML_LIMIT_NUM_FIELDS = 50
TEMPLATE_RELATIONS   = YES
INCLUDE_GRAPH        = YES
INCLUDED_BY_GRAPH    = YES
CALL_GRAPH          = YES
CALLER_GRAPH        = YES

# Preprocessor settings
ENABLE_PREPROCESSING = YES
MACRO_EXPANSION     = YES
EXPAND_ONLY_PREDEF  = NO
SEARCH_INCLUDES     = YES
INCLUDE_PATH        = 
INCLUDE_FILE_PATTERNS = 
PREDEFINED         = 
EXPAND_AS_DEFINED  = 
SKIP_FUNCTION_MACROS = YES

# Source code settings
SOURCE_BROWSER       = YES
INLINE_SOURCES      = NO
STRIP_CODE_COMMENTS = NO
REFERENCED_BY_RELATION = YES
REFERENCES_RELATION = YES
REFERENCES_LINK_SOURCE = YES
SOURCE_TOOLTIPS     = YES
USE_HTAGS          = NO
VERBATIM_HEADERS   = YES
CLANG_ASSISTED_PARSING = NO
CLANG_OPTIONS      = 

# Index settings
ALPHABETICAL_INDEX  = YES
COLS_IN_ALPHA_INDEX = 5
IGNORE_PREFIX      = 

# Warning settings
QUIET              = NO
WARNINGS           = YES
WARN_IF_UNDOCUMENTED = YES
WARN_IF_DOC_ERROR  = YES
WARN_NO_PARAMDOC  = NO
WARN_AS_ERROR     = NO
WARN_FORMAT       = "$file:$line: $text"
WARN_LOGFILE      = 

# Input filter settings
INPUT_FILTER       = 
FILTER_PATTERNS    = 
FILTER_SOURCE_FILES = NO
FILTER_SOURCE_PATTERNS = 

# Example settings
EXAMPLE_PATH       = 
EXAMPLE_PATTERNS   = *
EXAMPLE_RECURSIVE  = NO
IMAGE_PATH         = 
IMAGE_FILENAME     = 
DOT_PATH           = 
DOTFILE_DIRS       = 
MSCFILE_DIRS       = 
DIAFILE_DIRS       = 
PLANTUML_JAR_PATH  = 
PLANTUML_INCLUDE_PATH = 
DITA_LOCATION      = 

# Configuration for different output formats
GENERATE_RTF       = NO
GENERATE_MAN       = NO
GENERATE_XML       = NO
GENERATE_DOCBOOK   = NO
DOCBOOK_OUTPUT     = docbook
GENERATE_AUTOGEN_DEF = NO
GENERATE_PERLMOD   = NO
PERLMOD_LATEX      = NO
PERLMOD_PRETTY     = YES
PERLMOD_MAKEVAR_PREFIX = 