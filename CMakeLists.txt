# CMakeList.txt : CMake project for application, support libraries, tests, and
# documentation.
#

## CMake version requirement
cmake_minimum_required(VERSION 3.23)

#####################
# Build root
#####################

## Set C++ standard flags
### Must compile with C++20, no extensions
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED on)
set(CMAKE_CXX_EXTENSIONS off)
### Set symbol visibility defaults for all targets
set(CMAKE_CXX_VISIBILITY_PRESET hidden)
set(CMAKE_VISIBILITY_INLINES_HIDDEN on)

## MSVC specific settings
if(MSVC)
    set(variables
        CMAKE_CXX_FLAGS_DEBUG
        CMAKE_CXX_FLAGS_RELEASE
        CMAKE_CXX_FLAGS_RELWITHDEBINFO
        CMAKE_CXX_FLAGS_MINSIZEREL
    )
    foreach(variable ${variables})
        if(${variable} MATCHES "/MD")
            string(REGEX REPLACE "/MD" "/MT" ${variable} "${${variable}}")
            string(REGEX REPLACE "/MDd" "/MTd" ${variable} "${${variable}}")
        endif()
    endforeach()
endif()

## Compile with position independent code for shared libraries.
if(NOT CMAKE_POSITION_INDEPENDENT_CODE)
    set(CMAKE_POSITION_INDEPENDENT_CODE ${BUILD_SHARED_LIBS})
endif(NOT CMAKE_POSITION_INDEPENDENT_CODE)

## Project options
set(BUILD_TESTS OFF CACHE BOOL "Build unit tests")
set(BUILD_DOCS ON CACHE BOOL "Build documentation")
set(BUILD_SHARED_LIBS ON CACHE BOOL "Build shared libraries")
set(ENABLE_COVERAGE OFF CACHE BOOL "Enable code coverage")
set(SEM_VER OFF CACHE BOOL "Enable detection of SemVer")

## Set default build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Release" CACHE STRING "Build type" FORCE)
endif()

## Set project information
set(PROJECT_NAME "omop_etl")
set(PROJECT_VERSION_CURRENT 0)
set(PROJECT_VERSION_AGE 1)
set(PROJECT_VERSION_REVISION 1)
set(PROJECT_VERSION_INFO
    "${PROJECT_VERSION_CURRENT}.${PROJECT_VERSION_AGE}.${PROJECT_VERSION_REVISION}")

## Semantic versioning
if(SEM_VER)
    ## Get git SemVer version
    set(CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/cmake")
    include(DetermineVersion)
    find_package(Git)
    determine_version(${CMAKE_CURRENT_SOURCE_DIR} ${GIT_EXECUTABLE} ${PROJECT_NAME})
endif()

## Project declaration
project(${PROJECT_NAME}
    DESCRIPTION "C++ console application, with build scripts to generate documentation, run tests, and build code."
    VERSION ${PROJECT_VERSION_INFO}
    HOMEPAGE_URL ""
    LANGUAGES CXX
)

## Compiler flags
if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wpedantic")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3")

    if(ENABLE_COVERAGE)
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} --coverage")
        set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} --coverage")
    endif()
elseif(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2")
endif()

## Set output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

## Commands to output information to the console. Useful for displaying errors, warnings, and debugging
message("CXX Flags: " ${CMAKE_CXX_FLAGS})
get_cmake_property(_variableNames VARIABLES)
list (SORT _variableNames)
foreach (_variableName ${_variableNames})
    message(STATUS "${_variableName}=${${_variableName}}")
endforeach()

## Dependencies
find_package(Threads REQUIRED)
find_package(ZLIB REQUIRED)

### Doxygen
find_package(Doxygen QUIET)
if(DOXYGEN_FOUND)
    message(STATUS "Found Doxygen: ${DOXYGEN_VERSION}")
    
    # Create documentation targets
    add_custom_target(docs_core
        COMMAND ${DOXYGEN_EXECUTABLE} ${CMAKE_CURRENT_SOURCE_DIR}/docs/core/Doxyfile
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        COMMENT "Generating core library documentation"
    )
    
    add_custom_target(docs_common
        COMMAND ${DOXYGEN_EXECUTABLE} ${CMAKE_CURRENT_SOURCE_DIR}/docs/common/Doxyfile
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        COMMENT "Generating common library documentation"
    )
    
    add_custom_target(docs_main
        COMMAND ${DOXYGEN_EXECUTABLE} ${CMAKE_CURRENT_SOURCE_DIR}/docs/main/Doxyfile
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        COMMENT "Generating main project documentation"
    )
    
    # Create a target that builds all documentation
    add_custom_target(docs
        DEPENDS docs_core docs_common docs_main
        COMMENT "Generating all documentation"
    )
else()
    message(WARNING "Doxygen not found. Documentation will not be generated.")
endif()

### LibArchive support (optional for compressed file support)
find_package(LibArchive QUIET)

if(LibArchive_FOUND)
    message(STATUS "Found LibArchive ${LibArchive_VERSION}")
    message(STATUS "LibArchive include dirs: ${LibArchive_INCLUDE_DIRS}")
    message(STATUS "LibArchive libraries: ${LibArchive_LIBRARIES}")
    set(HAVE_LIBARCHIVE TRUE)
else()
    # Fallback using pkg-config
    find_package(PkgConfig QUIET)
    if(PkgConfig_FOUND)
        pkg_check_modules(LibArchive QUIET libarchive)
        if(LibArchive_FOUND)
            message(STATUS "Found LibArchive via pkg-config")
            set(HAVE_LIBARCHIVE TRUE)
        endif()
    endif()

    if(NOT LibArchive_FOUND)
        message(WARNING "LibArchive not found. Compressed file support (ZIP, BZIP2, XZ) will be disabled.")
        set(HAVE_LIBARCHIVE FALSE)
    endif()
endif()

if(HAVE_LIBARCHIVE)
    if(TARGET LibArchive::LibArchive)
        message(STATUS "Using LibArchive imported target")
    else()
        # Create imported target for older CMake versions
        add_library(LibArchive::LibArchive UNKNOWN IMPORTED)
        set_target_properties(LibArchive::LibArchive PROPERTIES
            IMPORTED_LOCATION "${LibArchive_LIBRARIES}"
            INTERFACE_INCLUDE_DIRECTORIES "${LibArchive_INCLUDE_DIRS}"
        )
    endif()
endif()

### ODBC
find_package(ODBC)
if(NOT ODBC_FOUND)
    message(WARNING "ODBC not found, database support will be disabled")
    set(HAVE_ODBC FALSE)
else()
    set(HAVE_ODBC TRUE)
endif()

### MySQL Client support
find_package(MySQL)
if(NOT MySQL_FOUND)
    message(STATUS "MySQL not found via find_package, trying pkg-config...")
    find_package(PkgConfig QUIET)
    if(PkgConfig_FOUND)
        pkg_check_modules(MySQL QUIET libmysqlclient)
        if(MySQL_FOUND)
            # Convert pkg-config variables to standard format
            set(MySQL_INCLUDE_DIRS ${MySQL_INCLUDE_DIRS})
            set(MySQL_LIBRARIES ${MySQL_LIBRARIES})
            set(MySQL_LIBRARY_DIRS ${MySQL_LIBRARY_DIRS})
        endif()
    endif()

    if(NOT MySQL_FOUND)
        message(STATUS "MySQL not found via pkg-config, trying mysql_config...")
        # Use mysql_config if available
        find_program(MYSQL_CONFIG mysql_config)
        if(MYSQL_CONFIG)
            execute_process(
                COMMAND ${MYSQL_CONFIG} --cflags
                OUTPUT_VARIABLE MYSQL_CFLAGS
                OUTPUT_STRIP_TRAILING_WHITESPACE
            )
            execute_process(
                COMMAND ${MYSQL_CONFIG} --libs
                OUTPUT_VARIABLE MYSQL_LIBS
                OUTPUT_STRIP_TRAILING_WHITESPACE
            )
            execute_process(
                COMMAND ${MYSQL_CONFIG} --include
                OUTPUT_VARIABLE MYSQL_INCLUDE_DIR
                OUTPUT_STRIP_TRAILING_WHITESPACE
            )
            
            # Parse the output
            string(REGEX REPLACE "^-I" "" MySQL_INCLUDE_DIRS "${MYSQL_INCLUDE_DIR}")
            set(MySQL_LIBRARIES "${MYSQL_LIBS}")

            # Find the actual library file for the imported target
            find_library(MYSQL_LIBRARY
                NAMES mysqlclient libmysqlclient
                PATHS /usr/lib/x86_64-linux-gnu /usr/lib /usr/local/lib
                NO_DEFAULT_PATH
            )

            set(MySQL_FOUND TRUE)
            message(STATUS "Found MySQL via mysql_config")
        endif()
    endif()

    if(NOT MySQL_FOUND)
        message(STATUS "MySQL not found, using fallback settings...")
        # Enhanced fallback with better path detection
        if(APPLE)
            set(MySQL_INCLUDE_DIRS 
                "/opt/homebrew/include/mysql" 
                "/opt/homebrew/include" 
                "/usr/local/include/mysql"
                "/usr/local/include"
            )
            set(MySQL_LIBRARY_DIRS 
                "/opt/homebrew/lib" 
                "/usr/local/lib"
            )
        elseif(UNIX)
            set(MySQL_INCLUDE_DIRS 
                "/usr/include/mysql" 
                "/usr/local/include/mysql"
                "/usr/lib64/mysql"
            )
            set(MySQL_LIBRARY_DIRS 
                "/usr/lib" 
                "/usr/local/lib"
                "/usr/lib64"
                "/usr/lib64/mysql"
            )
        endif()
        
        # Find the actual library file
        find_library(MYSQL_LIBRARY
            NAMES mysqlclient libmysqlclient
            PATHS ${MySQL_LIBRARY_DIRS}
            NO_DEFAULT_PATH
        )
        
        if(MYSQL_LIBRARY)
            set(MySQL_LIBRARIES ${MYSQL_LIBRARY})
            set(MySQL_FOUND TRUE)
            message(STATUS "Found MySQL library: ${MYSQL_LIBRARY}")
        else()
            message(WARNING "MySQL library not found. Install libmysqlclient-dev or mysql-devel package.")
        endif()
    endif()
endif()

if(MySQL_FOUND)
    if(NOT TARGET MySQL::MySQL)
        add_library(MySQL::MySQL UNKNOWN IMPORTED)
        set_target_properties(MySQL::MySQL PROPERTIES
            IMPORTED_LOCATION "${MYSQL_LIBRARY}"
            INTERFACE_INCLUDE_DIRECTORIES "${MySQL_INCLUDE_DIRS}"
        )
    endif()
    message(STATUS "MySQL found - Include dirs: ${MySQL_INCLUDE_DIRS}")
    message(STATUS "MySQL found - Libraries: ${MySQL_LIBRARIES}")
endif()

### PostgreSQL
find_package(PostgreSQL QUIET)
if(NOT PostgreSQL_FOUND)
    message(STATUS "PostgreSQL not found via find_package, trying pkg-config...")
    find_package(PkgConfig QUIET)
    if(PkgConfig_FOUND)
        pkg_check_modules(PostgreSQL QUIET libpq)
    endif()

    if(NOT PostgreSQL_FOUND)
        message(STATUS "PostgreSQL not found, using fallback settings...")
        # Set fallback values for common PostgreSQL installations
        if(APPLE)
            # Common Homebrew installation paths
            set(PostgreSQL_INCLUDE_DIRS "/opt/homebrew/include" "/usr/local/include")
            set(PostgreSQL_LIBRARIES "pq")
            set(PostgreSQL_LIBRARY_DIRS "/opt/homebrew/lib" "/usr/local/lib")
        elseif(UNIX)
            set(PostgreSQL_INCLUDE_DIRS "/usr/include/postgresql" "/usr/local/include/postgresql")
            set(PostgreSQL_LIBRARIES "pq")
            set(PostgreSQL_LIBRARY_DIRS "/usr/lib" "/usr/local/lib")
        endif()
        set(PostgreSQL_FOUND TRUE)
        message(STATUS "Using fallback PostgreSQL settings")
    endif()
endif()

### OpenSSL
find_package(OpenSSL REQUIRED)
if(NOT OpenSSL_FOUND)
    message(FATAL_ERROR "OpenSSL is required but not found, downloading from source...")
    include(FetchContent)
    FetchContent_Declare(
        OpenSSL
        GIT_REPOSITORY https://github.com/openssl/openssl.git
        GIT_TAG        OpenSSL_3_3_1
    )
endif()

### UUID handling - platform specific
if(APPLE)
    set(UUID_FOUND TRUE)
    set(UUID_LIBRARIES "")
    set(UUID_INCLUDE_DIRS "")
elseif(WIN32)
    find_library(UUID_LIBRARIES NAMES Rpcrt4)
    set(UUID_FOUND TRUE)
    set(UUID_INCLUDE_DIRS "")
else()
    # Linux/Unix
    find_path(UUID_INCLUDE_DIRS uuid/uuid.h)
    find_library(UUID_LIBRARIES NAMES uuid)
    if(UUID_INCLUDE_DIRS)
        set(UUID_FOUND TRUE)
        if(NOT UUID_LIBRARIES)
            set(UUID_LIBRARIES "")  # Headers found, library might be in libc
        endif()
    endif()
endif()

### fmt using FetchContent
find_package(fmt QUIET)
if(NOT fmt_FOUND)
    message(STATUS "System fmt not found, downloading from source...")
    include(FetchContent)
    FetchContent_Declare(
        fmt
        GIT_REPOSITORY https://github.com/fmtlib/fmt.git
        GIT_TAG        10.2.1
        GIT_SHALLOW    TRUE
    )
    set(FMT_DOC OFF CACHE BOOL "Generate the doc target.")
    set(FMT_TEST OFF CACHE BOOL "Generate the test target.")
    FetchContent_MakeAvailable(fmt)
endif()

### Add YAML parser and emitter for C++
find_package(yaml-cpp QUIET)

if(yaml-cpp_FOUND)
    message(STATUS "Found yaml-cpp via config file")
    # Modern yaml-cpp provides yaml-cpp target
    if(TARGET yaml-cpp)
        message(STATUS "Using yaml-cpp imported target")
        # Create alias for consistent naming
        if(NOT TARGET yaml-cpp::yaml-cpp)
            add_library(yaml-cpp::yaml-cpp ALIAS yaml-cpp)
        endif()
    endif()
else()
    # Try alternative package name
    find_package(YamlCpp QUIET)
    if(YamlCpp_FOUND)
        message(STATUS "Found yaml-cpp via YamlCpp config")
    else()
        # Fallback using pkg-config
        find_package(PkgConfig QUIET)
        if(PkgConfig_FOUND)
            pkg_check_modules(yaml-cpp QUIET yaml-cpp)
            if(yaml-cpp_FOUND)
                message(STATUS "Found yaml-cpp via pkg-config")
                # Create imported target
                if(NOT TARGET yaml-cpp::yaml-cpp)
                    add_library(yaml-cpp::yaml-cpp UNKNOWN IMPORTED)
                    set_target_properties(yaml-cpp::yaml-cpp PROPERTIES
                        IMPORTED_LOCATION "${yaml-cpp_LIBRARIES}"
                        INTERFACE_INCLUDE_DIRECTORIES "${yaml-cpp_INCLUDE_DIRS}"
                    )
                endif()
            endif()
        endif()
    endif()
endif()

if(NOT yaml-cpp_FOUND AND NOT YamlCpp_FOUND)
    message(FATAL_ERROR "yaml-cpp not found. Please install libyaml-cpp-dev package or build from source.")
endif()

### Enable nlohmann/json
find_package(nlohmann_json QUIET)
if(NOT nlohmann_json_FOUND)
    message(STATUS "nlohmann_json not found via find_package, , downloading from source...")
    include(FetchContent)
    FetchContent_Declare(
        nlohmann_json
        GIT_REPOSITORY https://github.com/nlohmann/json.git
        GIT_TAG        v3.11.2
    )
    set(nlohmann_json_DIR ${nlohmann_json_BINARY_DIR})
    FetchContent_MakeAvailable(nlohmann_json)
endif()

### spdlog for logging
find_package(spdlog QUIET)
if(NOT spdlog_FOUND)
    message(STATUS "spdlog not found via find_package, , downloading from source...")
    include(FetchContent)
    FetchContent_Declare(
        spdlog
        GIT_REPOSITORY https://github.com/gabime/spdlog.git
        GIT_TAG        v1.12.0
    )
    FetchContent_MakeAvailable(spdlog)
endif()

### cpp-httplib for REST API
find_path(HTTPLIB_INCLUDE_DIR NAMES httplib.h)
if(NOT HTTPLIB_INCLUDE_DIR)
    include(FetchContent)
    FetchContent_Declare(
        cpp_httplib
        GIT_REPOSITORY https://github.com/yhirose/cpp-httplib.git
        GIT_TAG        v0.14.0
    )
    FetchContent_MakeAvailable(cpp_httplib)
    set(HTTPLIB_INCLUDE_DIR ${cpp_httplib_SOURCE_DIR})
endif()

### Google Test for unit testing
if(BUILD_TESTS)
    include(FetchContent)
    FetchContent_Declare(
        googletest
        GIT_REPOSITORY https://github.com/google/googletest.git
        GIT_TAG v1.14.0
    )
    FetchContent_MakeAvailable(googletest)
    enable_testing()
endif()

## Schema configuration
include(${CMAKE_CURRENT_SOURCE_DIR}/src/lib/cdm/sql/schema_config.cmake)

## Configure header file
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/include/omop)
# Ensure the input file exists before configuring
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/src/lib/common/config.h.in")
    configure_file(
        ${CMAKE_CURRENT_SOURCE_DIR}/src/lib/common/config.h.in
        ${CMAKE_CURRENT_BINARY_DIR}/include/omop/config.h
    )
    # Debug: Print HAVE_LIBARCHIVE value
    message(STATUS "HAVE_LIBARCHIVE is set to: ${HAVE_LIBARCHIVE}")
else()
    message(FATAL_ERROR "Input file src/lib/common/config.h.in not found. Please create it or check the path.")
endif()

## Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/src/lib
    ${CMAKE_CURRENT_BINARY_DIR}/include
    ${PostgreSQL_INCLUDE_DIRS}
    ${YAML_CPP_INCLUDE_DIRS}
    ${HTTPLIB_INCLUDE_DIR}
)

## Link directories
link_directories(
    ${PostgreSQL_LIBRARY_DIRS}
    ${YAML_CPP_LIBRARY_DIRS}
)

## Add subdirectories
### Add source code
add_subdirectory(src)

### Add tests
if(BUILD_TESTS)
    add_subdirectory(tests)
endif()

## Installation
include(GNUInstallDirs)

### Install configuration files
install(DIRECTORY config/
    DESTINATION ${CMAKE_INSTALL_SYSCONFDIR}/omop-etl
    FILES_MATCHING PATTERN "*.yaml"
)

### Install SQL files
install(DIRECTORY ${CMAKE_BINARY_DIR}/src/lib/cdm/sql/
    DESTINATION ${CMAKE_INSTALL_DATADIR}/omop-etl/sql
    FILES_MATCHING PATTERN "*.sql"
)

### CPack configuration
set(CPACK_PACKAGE_NAME ${PROJECT_NAME})
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_VENDOR "OMOP ETL Team")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY ${PROJECT_DESCRIPTION})
set(CPACK_PACKAGE_DESCRIPTION_FILE "${CMAKE_CURRENT_SOURCE_DIR}/README.md")
set(CPACK_RESOURCE_FILE_LICENSE "${CMAKE_CURRENT_SOURCE_DIR}/LICENSE")
set(CPACK_GENERATOR "TGZ;DEB;RPM")

include(CPack)

## Export targets
# install(EXPORT omop-targets
#     FILE omop-targets.cmake
#     NAMESPACE omop::
#     DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/omop
# )

## Generate and install package configuration files
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/omop-config-version.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY SameMajorVersion
)

configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/cmake/omop-config.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/omop-config.cmake"
    INSTALL_DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/omop
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/omop-config.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/omop-config-version.cmake"
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/omop
)

## Print configuration summary
message(STATUS "")
message(STATUS "OMOP ETL Pipeline Configuration Summary")
message(STATUS "=======================================")
message(STATUS "Version:          ${PROJECT_VERSION}")
message(STATUS "Build type:       ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ Standard:     ${CMAKE_CXX_STANDARD}")
message(STATUS "")
message(STATUS "Options:")
message(STATUS "  Build tests:    ${BUILD_TESTS}")
message(STATUS "  Build docs:     ${BUILD_DOCS}")
message(STATUS "  Shared libs:    ${BUILD_SHARED_LIBS}")
message(STATUS "  Coverage:       ${ENABLE_COVERAGE}")
message(STATUS "")
message(STATUS "Dependencies:")
message(STATUS "  PostgreSQL:     ${PostgreSQL_VERSION}")
message(STATUS "  OpenSSL:        ${OpenSSL_VERSION}")
message(STATUS "  UUID:           ${UUID_FOUND}")
message(STATUS "  fmt:            ${fmt_FOUND}")
message(STATUS "  yaml-cpp:       ${YAML_CPP_VERSION}")
message(STATUS "  nlohmann_json:  ${nlohmann_json_VERSION}")
message(STATUS "  spdlog:         ${spdlog_VERSION}")
message(STATUS "  ODBC:           ${HAVE_ODBC}")
message(STATUS "")
message(STATUS "Schema Configuration:")
message(STATUS "  CDM Schema:     ${CDM_SCHEMA}")
message(STATUS "  Vocab Schema:   ${VOCAB_SCHEMA}")
message(STATUS "")
message(STATUS "Installation:")
message(STATUS "  Prefix:         ${CMAKE_INSTALL_PREFIX}")
message(STATUS "  SQL files:      ${CMAKE_INSTALL_DATADIR}/omop-etl/sql")
message(STATUS "  Config files:   ${CMAKE_INSTALL_DATADIR}/omop-etl/config")
message(STATUS "  Libraries:      ${CMAKE_INSTALL_LIBDIR}")
message(STATUS "  Headers:        ${CMAKE_INSTALL_INCLUDEDIR}")
message(STATUS "  Binaries:       ${CMAKE_INSTALL_BINDIR}")
message(STATUS "=======================================")
message(STATUS "")