File src/lib/cdm/table_definitions.h:

#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <any>
#include <mutex>

namespace omop::cdm {

/**
 * @brief Field definition for OMOP CDM tables
 * 
 * Represents a single column in an OMOP CDM table with all its metadata
 * including data type, nullability, and constraints.
 */
struct FieldDefinition {
    std::string name;
    std::string data_type;
    bool is_nullable;
    bool is_primary_key;
    std::string default_value;
    std::string comment;
};

/**
 * @brief Index definition for OMOP CDM tables
 * 
 * Defines database indexes to improve query performance on OMOP tables.
 * Supports both unique and non-unique indexes, as well as clustered indexes
 * for databases that support them (e.g., SQL Server).
 */
struct IndexDefinition {
    std::string name;
    std::string table_name;
    std::vector<std::string> columns;
    bool is_unique;
    bool is_clustered;
};

/**
 * @brief Foreign key constraint definition
 * 
 * Defines referential integrity constraints between OMOP tables to ensure
 * data consistency across the CDM schema.
 */
struct ForeignKeyDefinition {
    std::string name;
    std::string table_name;
    std::string column_name;
    std::string referenced_table;
    std::string referenced_column;
};

/**
 * @brief Database dialect for SQL generation
 * 
 * Enumeration of supported database platforms. Each dialect has specific
 * SQL syntax requirements for identifiers, data types, and features.
 */
enum class DatabaseDialect {
    PostgreSQL,
    MySQL,
    SQLServer,
    SQLite,
    Oracle
};

/**
 * @brief Table definition containing all metadata
 * 
 * Comprehensive representation of an OMOP CDM table including fields,
 * indexes, and constraints. Provides SQL generation methods for different
 * database dialects.
 */
class TableDefinition {
public:
    /**
     * @brief Constructor
     * @param name Table name
     */
    explicit TableDefinition(const std::string& name);

    /**
     * @brief Add field to table
     * @param field Field definition
     */
    void add_field(const FieldDefinition& field);

    /**
     * @brief Add index to table
     * @param index Index definition
     */
    void add_index(const IndexDefinition& index);

    /**
     * @brief Add foreign key constraint
     * @param fk Foreign key definition
     */
    void add_foreign_key(const ForeignKeyDefinition& fk);

    /**
     * @brief Get table name
     * @return std::string Table name
     */
    [[nodiscard]] const std::string& get_name() const { return name_; }

    /**
     * @brief Get all fields
     * @return const std::vector<FieldDefinition>& Fields
     */
    [[nodiscard]] const std::vector<FieldDefinition>& get_fields() const { return fields_; }

    /**
     * @brief Get all indexes
     * @return const std::vector<IndexDefinition>& Indexes
     */
    [[nodiscard]] const std::vector<IndexDefinition>& get_indexes() const { return indexes_; }

    /**
     * @brief Get all foreign keys
     * @return const std::vector<ForeignKeyDefinition>& Foreign keys
     */
    [[nodiscard]] const std::vector<ForeignKeyDefinition>& get_foreign_keys() const { return foreign_keys_; }

    /**
     * @brief Generate CREATE TABLE SQL
     * 
     * Creates a complete CREATE TABLE statement for the specified database dialect.
     * Includes all fields, data types, constraints, and primary key definition.
     * 
     * @param schema_name Schema name (e.g., "cdm", "public")
     * @param dialect Database dialect for SQL syntax
     * @return std::string CREATE TABLE SQL statement
     */
    [[nodiscard]] std::string generate_create_table_sql(
        const std::string& schema_name,
        DatabaseDialect dialect = DatabaseDialect::PostgreSQL) const;

    /**
     * @brief Generate CREATE INDEX SQL statements
     * 
     * Creates all index definitions for the table. Returns a vector as multiple
     * indexes may be defined for a single table.
     * 
     * @param schema_name Schema name
     * @param dialect Database dialect
     * @return std::vector<std::string> CREATE INDEX statements
     */
    [[nodiscard]] std::vector<std::string> generate_create_index_sql(
        const std::string& schema_name,
        DatabaseDialect dialect = DatabaseDialect::PostgreSQL) const;

    /**
     * @brief Generate ALTER TABLE ADD CONSTRAINT SQL statements
     * 
     * Creates foreign key constraint definitions. These are typically applied
     * after all tables are created to avoid dependency issues.
     * 
     * @param schema_name Schema name
     * @param dialect Database dialect
     * @return std::vector<std::string> ALTER TABLE statements
     */
    [[nodiscard]] std::vector<std::string> generate_foreign_key_sql(
        const std::string& schema_name,
        DatabaseDialect dialect = DatabaseDialect::PostgreSQL) const;

private:
    std::string name_;
    std::vector<FieldDefinition> fields_;
    std::vector<IndexDefinition> indexes_;
    std::vector<ForeignKeyDefinition> foreign_keys_;

    /**
     * @brief Convert field type to SQL data type
     * @param field_type Field type
     * @param dialect Database dialect
     * @return std::string SQL data type
     */
    [[nodiscard]] std::string field_type_to_sql(
        const std::string& field_type,
        DatabaseDialect dialect) const;
};

/**
 * @brief OMOP CDM schema definitions
 * 
 * Singleton class containing all OMOP CDM v5.4 table definitions.
 * Provides methods to retrieve table metadata and generate SQL DDL
 * statements for schema creation.
 */
class SchemaDefinitions {
public:
    /**
     * @brief Get singleton instance
     * @return SchemaDefinitions& Instance
     */
    static SchemaDefinitions& instance();

    /**
     * @brief Get table definition
     * @param table_name Table name
     * @return const TableDefinition* Table definition or nullptr if not found
     */
    [[nodiscard]] const TableDefinition* get_table(const std::string& table_name) const;

    /**
     * @brief Get all table names
     * @return std::vector<std::string> Table names
     */
    [[nodiscard]] std::vector<std::string> get_table_names() const;

    /**
     * @brief Get tables in dependency order for creation
     * 
     * Returns table names ordered to respect foreign key dependencies.
     * Tables without dependencies come first, followed by dependent tables.
     * 
     * @return std::vector<std::string> Ordered table names
     */
    [[nodiscard]] std::vector<std::string> get_creation_order() const;

    /**
     * @brief Get tables in dependency order for dropping
     * 
     * Returns table names in reverse dependency order for safe deletion.
     * Dependent tables come first to avoid foreign key constraint violations.
     * 
     * @return std::vector<std::string> Ordered table names
     */
    [[nodiscard]] std::vector<std::string> get_drop_order() const;

    /**
     * @brief Generate full schema SQL
     * 
     * Generates all SQL statements needed to create a complete OMOP CDM schema.
     * Includes schema creation, tables, indexes, and foreign key constraints.
     * 
     * @param schema_name Schema name (default: "cdm")
     * @param dialect Database dialect (default: PostgreSQL)
     * @param include_indexes Include index creation statements (default: true)
     * @param include_constraints Include foreign key constraints (default: true)
     * @return std::vector<std::string> Ordered SQL statements for execution
     */
    [[nodiscard]] std::vector<std::string> generate_schema_sql(
        const std::string& schema_name = "cdm",
        DatabaseDialect dialect = DatabaseDialect::PostgreSQL,
        bool include_indexes = true,
        bool include_constraints = true) const;

private:
    SchemaDefinitions();
    ~SchemaDefinitions() = default;
    SchemaDefinitions(const SchemaDefinitions&) = delete;
    SchemaDefinitions& operator=(const SchemaDefinitions&) = delete;

    void initialize_tables();
    void initialize_concept_table();
    void initialize_person_table();
    void initialize_observation_period_table();
    void initialize_visit_occurrence_table();
    void initialize_condition_occurrence_table();
    void initialize_drug_exposure_table();
    void initialize_procedure_occurrence_table();
    void initialize_measurement_table();
    void initialize_observation_table();
    void initialize_death_table();
    void initialize_note_table();
    void initialize_location_table();
    void initialize_care_site_table();
    void initialize_provider_table();
    void initialize_visit_detail_table();

    std::unordered_map<std::string, std::unique_ptr<TableDefinition>> tables_;
    mutable std::mutex mutex_; // Thread safety for singleton access
};

/**
 * @brief SQL generator for different database dialects
 * 
 * Utility class providing static methods for generating SQL syntax
 * specific to different database platforms. Handles identifier quoting,
 * data type mapping, and platform-specific features.
 */
class SqlGenerator {
public:
    /**
     * @brief Quote identifier based on dialect
     * @param identifier Identifier to quote
     * @param dialect Database dialect
     * @return std::string Quoted identifier
     */
    [[nodiscard]] static std::string quote_identifier(
        const std::string& identifier,
        DatabaseDialect dialect);

    /**
     * @brief Format schema and table name
     * @param schema_name Schema name
     * @param table_name Table name
     * @param dialect Database dialect
     * @return std::string Formatted table name
     */
    [[nodiscard]] static std::string format_table_name(
        const std::string& schema_name,
        const std::string& table_name,
        DatabaseDialect dialect);

    /**
     * @brief Get auto-increment syntax
     * @param dialect Database dialect
     * @return std::string Auto-increment syntax
     */
    [[nodiscard]] static std::string get_auto_increment_syntax(DatabaseDialect dialect);

    /**
     * @brief Get current timestamp function
     * @param dialect Database dialect
     * @return std::string Current timestamp function
     */
    [[nodiscard]] static std::string get_current_timestamp_function(DatabaseDialect dialect);

    /**
     * @brief Escape string value for SQL
     * 
     * Properly escapes string values to prevent SQL injection.
     * Doubles single quotes and handles special characters based on dialect.
     * 
     * @param value String value to escape
     * @param dialect Database dialect
     * @return std::string Escaped string value with quotes
     */
    [[nodiscard]] static std::string quote_value(
        const std::string& value,
        DatabaseDialect dialect);

    /**
     * @brief Format date/time value for SQL
     * 
     * Thread-safe formatting of date/time values for SQL statements.
     * 
     * @param time_point Time point to format
     * @param dialect Database dialect
     * @return std::string Formatted date/time string
     */
    [[nodiscard]] static std::string format_datetime(
        const std::chrono::system_clock::time_point& time_point,
        DatabaseDialect dialect);
};

} // namespace omop::cdm

File src/lib/cdm/omop_tables.h:

#pragma once

#include <string>
#include <optional>
#include <chrono>
#include <vector>
#include <unordered_map>
#include <any>
#include <memory>
#include <functional>

namespace omop::cdm {

/**
 * @brief OMOP CDM version
 */
constexpr const char* CDM_VERSION = "5.4";

/**
 * @brief Validation result containing status and error messages
 */
struct ValidationResult {
    bool is_valid;
    std::vector<std::string> errors;
    
    /**
     * @brief Implicit conversion to bool for convenience
     */
    operator bool() const { return is_valid; }
};

/**
 * @brief Field visitor interface for efficient field access
 */
class FieldVisitor {
public:
    virtual ~FieldVisitor() = default;
    
    /**
     * @brief Visit a field with its name and value
     * @param name Field name
     * @param value Field value
     */
    virtual void visit(const std::string& name, const std::any& value) = 0;
};

/**
 * @brief Base class for all OMOP CDM tables
 *
 * Provides common functionality for OMOP table records including
 * field access, validation, and SQL generation. Implements visitor
 * pattern for efficient field iteration.
 */
class OmopTable {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~OmopTable() = default;

    /**
     * @brief Get table name
     * @return std::string Table name
     */
    [[nodiscard]] virtual std::string table_name() const = 0;

    /**
     * @brief Get schema name
     * @return std::string Schema name
     */
    [[nodiscard]] virtual std::string schema_name() const { return "cdm"; }

    /**
     * @brief Convert to INSERT SQL statement
     * 
     * Generates a properly escaped SQL INSERT statement for the record.
     * String values are escaped to prevent SQL injection.
     * 
     * @param escape_values Whether to escape string values (default: true)
     * @return std::string INSERT SQL
     */
    [[nodiscard]] virtual std::string to_insert_sql(bool escape_values = true) const = 0;

    /**
     * @brief Get field names
     * @return std::vector<std::string> Field names
     */
    [[nodiscard]] virtual std::vector<std::string> field_names() const = 0;

    /**
     * @brief Get field values
     * 
     * @deprecated Use visit_fields() for better performance
     * @return std::vector<std::any> Field values
     */
    [[nodiscard]] virtual std::vector<std::any> field_values() const = 0;

    /**
     * @brief Visit fields with a visitor for efficient access
     * 
     * Allows iteration over fields without creating intermediate vectors.
     * More efficient than field_names() + field_values() for large tables.
     * 
     * @param visitor Field visitor
     */
    virtual void visit_fields(FieldVisitor& visitor) const = 0;

    /**
     * @brief Validate record
     * @return bool True if valid
     */
    [[nodiscard]] virtual bool validate() const = 0;

    /**
     * @brief Get validation errors
     * @return std::vector<std::string> Validation errors
     */
    [[nodiscard]] virtual std::vector<std::string> validation_errors() const = 0;

    /**
     * @brief Validate record with detailed result
     * @return ValidationResult Validation result with errors
     */
    [[nodiscard]] virtual ValidationResult validate_detailed() const {
        ValidationResult result;
        result.is_valid = validate();
        if (!result.is_valid) {
            result.errors = validation_errors();
        }
        return result;
    }

protected:
    /**
     * @brief Helper to format datetime for SQL
     * @param time_point Time point to format
     * @return std::string Formatted datetime string
     */
    [[nodiscard]] static std::string format_datetime_sql(
        const std::chrono::system_clock::time_point& time_point);

    /**
     * @brief Helper to escape string value for SQL
     * @param value String value to escape
     * @return std::string Escaped string with quotes
     */
    [[nodiscard]] static std::string escape_string_sql(const std::string& value);
};

/**
 * @brief Person table
 *
 * Central table containing demographic information about each person.
 * Includes comprehensive validation for data integrity.
 */
class Person : public OmopTable {
public:
    // Required fields
    int64_t person_id{0};
    int32_t gender_concept_id{0};
    int32_t year_of_birth{0};
    int32_t race_concept_id{0};
    int32_t ethnicity_concept_id{0};

    // Optional fields
    std::optional<int32_t> month_of_birth;
    std::optional<int32_t> day_of_birth;
    std::optional<std::chrono::system_clock::time_point> birth_datetime;
    std::optional<int32_t> location_id;
    std::optional<int32_t> provider_id;
    std::optional<int32_t> care_site_id;
    std::optional<std::string> person_source_value;
    std::optional<std::string> gender_source_value;
    std::optional<int32_t> gender_source_concept_id;
    std::optional<std::string> race_source_value;
    std::optional<int32_t> race_source_concept_id;
    std::optional<std::string> ethnicity_source_value;
    std::optional<int32_t> ethnicity_source_concept_id;

    [[nodiscard]] std::string table_name() const override { return "person"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
    
private:
    /**
     * @brief Validate year of birth is reasonable
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_year_of_birth() const;
    
    /**
     * @brief Validate month of birth if provided
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_month_of_birth() const;
    
    /**
     * @brief Validate day of birth if provided
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_day_of_birth() const;
};

/**
 * @brief Observation Period table
 *
 * Defines periods of time during which a person is observed.
 * Validates that periods have logical date ranges.
 */
class ObservationPeriod : public OmopTable {
public:
    // Required fields
    int64_t observation_period_id{0};
    int64_t person_id{0};
    std::chrono::system_clock::time_point observation_period_start_date;
    std::chrono::system_clock::time_point observation_period_end_date;
    int32_t period_type_concept_id{0};

    [[nodiscard]] std::string table_name() const override { return "observation_period"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
    
private:
    /**
     * @brief Validate date range is logical
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_date_range() const;
};

/**
 * @brief Visit Occurrence table
 *
 * Records encounters between a person and healthcare provider.
 * Includes validation for visit dates and relationships.
 */
class VisitOccurrence : public OmopTable {
public:
    // Required fields
    int64_t visit_occurrence_id{0};
    int64_t person_id{0};
    int32_t visit_concept_id{0};
    std::chrono::system_clock::time_point visit_start_date;
    std::chrono::system_clock::time_point visit_end_date;
    int32_t visit_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> visit_start_datetime;
    std::optional<std::chrono::system_clock::time_point> visit_end_datetime;
    std::optional<int32_t> provider_id;
    std::optional<int32_t> care_site_id;
    std::optional<std::string> visit_source_value;
    std::optional<int32_t> visit_source_concept_id;
    std::optional<int32_t> admitted_from_concept_id;
    std::optional<std::string> admitted_from_source_value;
    std::optional<int32_t> discharged_to_concept_id;
    std::optional<std::string> discharged_to_source_value;
    std::optional<int64_t> preceding_visit_occurrence_id;

    [[nodiscard]] std::string table_name() const override { return "visit_occurrence"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Condition Occurrence table
 *
 * Records conditions (diseases, symptoms, findings) diagnosed or reported.
 */
class ConditionOccurrence : public OmopTable {
public:
    // Required fields
    int64_t condition_occurrence_id{0};
    int64_t person_id{0};
    int32_t condition_concept_id{0};
    std::chrono::system_clock::time_point condition_start_date;
    int32_t condition_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> condition_start_datetime;
    std::optional<std::chrono::system_clock::time_point> condition_end_date;
    std::optional<std::chrono::system_clock::time_point> condition_end_datetime;
    std::optional<int32_t> condition_status_concept_id;
    std::optional<std::string> stop_reason;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> condition_source_value;
    std::optional<int32_t> condition_source_concept_id;
    std::optional<std::string> condition_status_source_value;

    [[nodiscard]] std::string table_name() const override { return "condition_occurrence"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Drug Exposure table
 *
 * Records drug exposures including prescriptions, administrations, and dispensings.
 * Includes validation for drug quantities and date ranges.
 */
class DrugExposure : public OmopTable {
public:
    // Required fields
    int64_t drug_exposure_id{0};
    int64_t person_id{0};
    int32_t drug_concept_id{0};
    std::chrono::system_clock::time_point drug_exposure_start_date;
    std::chrono::system_clock::time_point drug_exposure_end_date;
    int32_t drug_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> drug_exposure_start_datetime;
    std::optional<std::chrono::system_clock::time_point> drug_exposure_end_datetime;
    std::optional<std::chrono::system_clock::time_point> verbatim_end_date;
    std::optional<std::string> stop_reason;
    std::optional<int32_t> refills;
    std::optional<double> quantity;
    std::optional<int32_t> days_supply;
    std::optional<std::string> sig;
    std::optional<int32_t> route_concept_id;
    std::optional<std::string> lot_number;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> drug_source_value;
    std::optional<int32_t> drug_source_concept_id;
    std::optional<std::string> route_source_value;
    std::optional<std::string> dose_unit_source_value;

    [[nodiscard]] std::string table_name() const override { return "drug_exposure"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
    
private:
    /**
     * @brief Validate drug quantity if provided
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_quantity() const;
    
    /**
     * @brief Validate days supply if provided
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_days_supply() const;
};

/**
 * @brief Procedure Occurrence table
 *
 * Records procedures performed on a person.
 */
class ProcedureOccurrence : public OmopTable {
public:
    // Required fields
    int64_t procedure_occurrence_id{0};
    int64_t person_id{0};
    int32_t procedure_concept_id{0};
    std::chrono::system_clock::time_point procedure_date;
    int32_t procedure_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> procedure_datetime;
    std::optional<std::chrono::system_clock::time_point> procedure_end_date;
    std::optional<std::chrono::system_clock::time_point> procedure_end_datetime;
    std::optional<int32_t> modifier_concept_id;
    std::optional<int32_t> quantity;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> procedure_source_value;
    std::optional<int32_t> procedure_source_concept_id;
    std::optional<std::string> modifier_source_value;

    [[nodiscard]] std::string table_name() const override { return "procedure_occurrence"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Measurement table
 *
 * Records measurements including laboratory tests, vital signs, and other clinical observations.
 * Includes validation for measurement values and ranges.
 */
class Measurement : public OmopTable {
public:
    // Required fields
    int64_t measurement_id{0};
    int64_t person_id{0};
    int32_t measurement_concept_id{0};
    std::chrono::system_clock::time_point measurement_date;
    int32_t measurement_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> measurement_datetime;
    std::optional<std::chrono::system_clock::time_point> measurement_time;
    std::optional<int32_t> operator_concept_id;
    std::optional<double> value_as_number;
    std::optional<int32_t> value_as_concept_id;
    std::optional<int32_t> unit_concept_id;
    std::optional<double> range_low;
    std::optional<double> range_high;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> measurement_source_value;
    std::optional<int32_t> measurement_source_concept_id;
    std::optional<std::string> unit_source_value;
    std::optional<int32_t> unit_source_concept_id;
    std::optional<std::string> value_source_value;
    std::optional<int64_t> measurement_event_id;
    std::optional<int32_t> meas_event_field_concept_id;

    [[nodiscard]] std::string table_name() const override { return "measurement"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
    
private:
    /**
     * @brief Validate measurement ranges if provided
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_ranges() const;
};

/**
 * @brief Observation table
 *
 * Records clinical facts about a person that are not measurements or procedures.
 */
class Observation : public OmopTable {
public:
    // Required fields
    int64_t observation_id{0};
    int64_t person_id{0};
    int32_t observation_concept_id{0};
    std::chrono::system_clock::time_point observation_date;
    int32_t observation_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> observation_datetime;
    std::optional<double> value_as_number;
    std::optional<std::string> value_as_string;
    std::optional<int32_t> value_as_concept_id;
    std::optional<int32_t> qualifier_concept_id;
    std::optional<int32_t> unit_concept_id;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> observation_source_value;
    std::optional<int32_t> observation_source_concept_id;
    std::optional<std::string> unit_source_value;
    std::optional<std::string> qualifier_source_value;
    std::optional<std::string> value_source_value;
    std::optional<int64_t> observation_event_id;
    std::optional<int32_t> obs_event_field_concept_id;

    [[nodiscard]] std::string table_name() const override { return "observation"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Death table
 *
 * Records death information for a person.
 */
class Death : public OmopTable {
public:
    // Required fields
    int64_t person_id{0};
    std::chrono::system_clock::time_point death_date;

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> death_datetime;
    std::optional<int32_t> death_type_concept_id;
    std::optional<int32_t> cause_concept_id;
    std::optional<std::string> cause_source_value;
    std::optional<int32_t> cause_source_concept_id;

    [[nodiscard]] std::string table_name() const override { return "death"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
    
private:
    /**
     * @brief Validate death date is not in the future
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_death_date() const;
};

/**
 * @brief Note table
 *
 * Records unstructured text notes about a person.
 */
class Note : public OmopTable {
public:
    // Required fields
    int64_t note_id{0};
    int64_t person_id{0};
    std::chrono::system_clock::time_point note_date;
    int32_t note_type_concept_id{0};
    int32_t note_class_concept_id{0};
    std::string note_title;
    std::string note_text;
    int32_t encoding_concept_id{0};
    int32_t language_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> note_datetime;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> note_source_value;
    std::optional<int64_t> note_event_id;
    std::optional<int32_t> note_event_field_concept_id;

    [[nodiscard]] std::string table_name() const override { return "note"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Factory for creating OMOP table instances
 */
class OmopTableFactory {
public:
    /**
     * @brief Create table instance by name
     * @param table_name Table name
     * @return std::unique_ptr<OmopTable> Table instance or nullptr if unsupported
     */
    [[nodiscard]] static std::unique_ptr<OmopTable> create(const std::string& table_name);

    /**
     * @brief Get all supported table names
     * @return std::vector<std::string> Table names
     */
    [[nodiscard]] static std::vector<std::string> get_supported_tables();

    /**
     * @brief Check if table is supported
     * @param table_name Table name
     * @return bool True if supported
     */
    [[nodiscard]] static bool is_supported(const std::string& table_name);

    /**
     * @brief Register custom table type
     * 
     * Allows extending the factory with custom table implementations.
     * 
     * @param table_name Table name
     * @param creator Factory function
     */
    static void register_table(
        const std::string& table_name,
        std::function<std::unique_ptr<OmopTable>()> creator);

private:
    static std::unordered_map<std::string, std::function<std::unique_ptr<OmopTable>()>>& get_creators();
};

/**
 * @brief OMOP CDM table schema information
 */
class OmopSchema {
public:
    /**
     * @brief Get CREATE TABLE SQL for a table
     * @param table_name Table name
     * @param schema_name Schema name
     * @return std::string CREATE TABLE SQL
     * @throws std::invalid_argument if table name is not recognized
     */
    [[nodiscard]] static std::string get_create_table_sql(
        const std::string& table_name,
        const std::string& schema_name = "cdm");

    /**
     * @brief Get all CREATE TABLE statements
     * @param schema_name Schema name
     * @return std::vector<std::string> CREATE TABLE statements in dependency order
     */
    [[nodiscard]] static std::vector<std::string> get_all_create_table_sql(
        const std::string& schema_name = "cdm");

    /**
     * @brief Get indexes for a table
     * @param table_name Table name
     * @param schema_name Schema name
     * @return std::vector<std::string> CREATE INDEX statements
     * @throws std::invalid_argument if table name is not recognized
     */
    [[nodiscard]] static std::vector<std::string> get_table_indexes(
        const std::string& table_name,
        const std::string& schema_name = "cdm");

    /**
     * @brief Get foreign key constraints
     * @param table_name Table name
     * @param schema_name Schema name
     * @return std::vector<std::string> ALTER TABLE ADD CONSTRAINT statements
     * @throws std::invalid_argument if table name is not recognized
     */
    [[nodiscard]] static std::vector<std::string> get_foreign_keys(
        const std::string& table_name,
        const std::string& schema_name = "cdm");
};

} // namespace omop::cdm

File src/lib/core/interfaces.h:

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>
#include <variant>
#include <optional>
#include <chrono>
#include <concepts>
#include <iostream>
#include <format>
#include <mutex>
#include <spdlog/spdlog.h>
#include <stdexcept>

#include "common/exceptions.h"
#include "common/logging.h"
#include "record.h"

namespace omop::core {

/**
 * @file interfaces.h
 * @brief Core interfaces for the OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header file defines the core interfaces used throughout the OMOP ETL pipeline.
 * These interfaces establish the contract between different components of the system,
 * ensuring consistent behavior and enabling loose coupling between modules.
 * 
 * @section design Design Principles
 * - Interface Segregation: Each interface has a single, well-defined responsibility
 * - Dependency Inversion: High-level modules depend on abstractions
 * - Open/Closed: Interfaces are designed for extension without modification
 * 
 * @section usage Usage
 * To use these interfaces:
 * 1. Include this header in your implementation file
 * 2. Create concrete classes that implement the required interfaces
 * 3. Use the interfaces as type hints in your function parameters
 * 
 * @section example Example
 * @code
 * class MyDataProcessor : public IDataProcessor {
 *     // Implementation
 * };
 * @endcode
 */

/**
 * @brief Forward declarations
 */
class ProcessingContext;
class ValidationResult;

/**
 * @brief Concept for record-like types
 */
template<typename T>
concept RecordLike = requires(T t) {
    { t.getField(std::string{}) } -> std::convertible_to<std::optional<std::any>>;
    { t.setField(std::string{}, std::any{}) } -> std::same_as<void>;
    { t.getFieldNames() } -> std::convertible_to<std::vector<std::string>>;
};

/**
 * @class ProcessingContext
 * @brief Processing context for ETL operations
 * @ingroup core
 * 
 * @details
 * Manages the execution context and state for ETL operations. Provides access to
 * shared resources, tracks processing progress, and maintains job metadata.
 * 
 * @dependencies
 * - spdlog: Logging framework
 * - std::chrono: Timing utilities
 * 
 * @note Thread-safe implementation for concurrent operations
 */
class ProcessingContext {
public:
    /**
     * @brief Processing stage enumeration
     */
    enum class Stage {
        Extract,
        Transform,
        Load
    };

    /**
     * @brief Default constructor
     */
    ProcessingContext() : start_time_(std::chrono::steady_clock::now()) {}

    /**
     * @brief Get current processing stage
     * @return Stage Current stage
     */
    [[nodiscard]] Stage current_stage() const noexcept { return current_stage_; }

    /**
     * @brief Set current processing stage
     * @param stage Processing stage
     */
    void set_stage(Stage stage) { current_stage_ = stage; }

    /**
     * @brief Get job ID
     * @return const std::string& Job identifier
     */
    [[nodiscard]] const std::string& job_id() const noexcept { return job_id_; }

    /**
     * @brief Set job ID
     * @param id Job identifier
     */
    void set_job_id(std::string id) { job_id_ = std::move(id); }

    /**
     * @brief Increment processed record count
     * @param count Number of records to add
     */
    void increment_processed(size_t count = 1) { processed_count_ += count; }

    /**
     * @brief Increment error count
     * @param count Number of errors to add
     */
    void increment_errors(size_t count = 1) { error_count_ += count; }

    /**
     * @brief Get processed record count
     * @return size_t Number of processed records
     */
    [[nodiscard]] size_t processed_count() const noexcept { return processed_count_; }

    /**
     * @brief Get error count
     * @return size_t Number of errors
     */
    [[nodiscard]] size_t error_count() const noexcept { return error_count_; }

    /**
     * @brief Get elapsed time
     * @return std::chrono::duration<double> Elapsed time in seconds
     */
    [[nodiscard]] std::chrono::duration<double> elapsed_time() const {
        return std::chrono::steady_clock::now() - start_time_;
    }

    /**
     * @brief Store context data
     * @param key Data key
     * @param value Data value
     */
    void set_data(const std::string& key, std::any value);

    /**
     * @brief Retrieve context data
     * @param key Data key
     * @return std::optional<std::any> Data value if exists
     */
    [[nodiscard]] std::optional<std::any> get_data(const std::string& key) const;

    /**
     * @brief Log message
     * @param level Log level
     * @param message Log message
     */
    void log(const std::string& level, const std::string& message);

private:
    Stage current_stage_{Stage::Extract};
    std::string job_id_;
    size_t processed_count_{0};
    size_t error_count_{0};
    std::chrono::steady_clock::time_point start_time_;
    mutable std::mutex context_mutex_;
    std::unordered_map<std::string, std::any> context_data_;
};

/**
 * @interface IExtractor
 * @brief Base interface for data extraction
 * @ingroup core
 * 
 * @details
 * Defines the contract for data extraction components. Implementations are responsible
 * for reading data from source systems and converting it to the Record format.
 * 
 * @dependencies
 * - ProcessingContext: Execution context
 * - Record: Data representation
 * 
 * @see @ref extract "Extraction Components"
 */
class IExtractor {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~IExtractor() = default;

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    virtual void initialize(const std::unordered_map<std::string, std::any>& config,
                           ProcessingContext& context) = 0;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return RecordBatch Extracted records (empty if no more data)
     */
    virtual RecordBatch extract_batch(size_t batch_size, ProcessingContext& context) = 0;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    virtual bool has_more_data() const = 0;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Finalize extraction and clean up resources
     * @param context Processing context
     */
    virtual void finalize(ProcessingContext& context) = 0;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() const = 0;
};

/**
 * @interface ITransformer
 * @brief Base interface for data transformation
 * @ingroup core
 * 
 * @details
 * Defines the contract for data transformation components. Implementations apply
 * business rules, validate data quality, and enrich records with additional data.
 * 
 * @dependencies
 * - ProcessingContext: Execution context
 * - Record: Data representation
 * - ValidationResult: Validation results
 * 
 * @see @ref transform "Transformation Components"
 */
class ITransformer {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~ITransformer() = default;

    /**
     * @brief Initialize the transformer
     * @param config Configuration parameters
     * @param context Processing context
     */
    virtual void initialize(const std::unordered_map<std::string, std::any>& config,
                           ProcessingContext& context) = 0;

    /**
     * @brief Transform a single record
     * @param record Record to transform
     * @param context Processing context
     * @return std::optional<Record> Transformed record or empty if filtered out
     */
    virtual std::optional<Record> transform(const Record& record,
                                          ProcessingContext& context) = 0;

    /**
     * @brief Transform a batch of records
     * @param batch Batch to transform
     * @param context Processing context
     * @return RecordBatch Transformed batch
     */
    virtual RecordBatch transform_batch(const RecordBatch& batch,
                                       ProcessingContext& context) = 0;

    /**
     * @brief Get transformer type name
     * @return std::string Transformer type identifier
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Validate record according to transformation rules
     * @param record Record to validate
     * @return ValidationResult Validation result
     */
    virtual ValidationResult validate(const Record& record) const = 0;

    /**
     * @brief Get transformation statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() const = 0;
};

/**
 * @interface ILoader
 * @brief Base interface for data loading
 * @ingroup core
 * 
 * @details
 * Defines the contract for data loading components. Implementations handle writing
 * data to target systems, managing transactions, and handling loading errors.
 * 
 * @dependencies
 * - ProcessingContext: Execution context
 * - Record: Data representation
 * 
 * @see @ref load "Loading Components"
 */
class ILoader {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~ILoader() = default;

    /**
     * @brief Initialize the loader
     * @param config Configuration parameters
     * @param context Processing context
     */
    virtual void initialize(const std::unordered_map<std::string, std::any>& config,
                           ProcessingContext& context) = 0;

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     */
    virtual bool load(const Record& record, ProcessingContext& context) = 0;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     */
    virtual size_t load_batch(const RecordBatch& batch, ProcessingContext& context) = 0;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     */
    virtual void commit(ProcessingContext& context) = 0;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     */
    virtual void rollback(ProcessingContext& context) = 0;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Finalize loading and clean up resources
     * @param context Processing context
     */
    virtual void finalize(ProcessingContext& context) = 0;

    /**
     * @brief Get loading statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() const = 0;
};

/**
 * @class ValidationResult
 * @brief Validation result for record validation
 * @ingroup core
 * 
 * @details
 * Represents the result of record validation operations. Tracks validation status,
 * collects validation errors, and provides detailed error information.
 * 
 * @dependencies
 * - Record: Data validation
 * 
 * @see @ref validation "Validation Framework"
 */
class ValidationResult {
public:
    struct ValidationError {
        std::string field_name;
        std::string error_message;
        std::string rule_name;
    };

    /**
     * @brief Default constructor (valid result)
     */
    ValidationResult() = default;

    /**
     * @brief Add validation error
     * @param error Validation error
     */
    void add_error(ValidationError error) {
        errors_.push_back(std::move(error));
        is_valid_ = false;
    }

    /**
     * @brief Add validation error with field, message and rule
     * @param field_name Field name
     * @param error_message Error message
     * @param rule_name Rule name
     */
    void add_error(const std::string& field_name, 
                  const std::string& error_message,
                  const std::string& rule_name) {
        errors_.push_back({field_name, error_message, rule_name});
        is_valid_ = false;
    }

    /**
     * @brief Check if validation passed
     * @return bool True if valid
     */
    [[nodiscard]] bool is_valid() const noexcept { return is_valid_; }

    /**
     * @brief Get validation errors
     * @return const std::vector<ValidationError>& Vector of errors
     */
    [[nodiscard]] const std::vector<ValidationError>& errors() const noexcept {
        return errors_;
    }

    /**
     * @brief Get error count
     * @return size_t Number of errors
     */
    [[nodiscard]] size_t error_count() const noexcept { return errors_.size(); }
    
    /**
     * @brief Get error messages as a formatted string
     * @return std::string Formatted error messages
     */
    [[nodiscard]] std::string error_messages() const {
        if (errors_.empty()) {
            return "";
        }
        
        std::string result;
        for (const auto& error : errors_) {
            result += std::format("Field '{}': {} (rule: {})\n", 
                                 error.field_name, 
                                 error.error_message,
                                 error.rule_name);
        }
        return result;
    }
    
    /**
     * @brief Merge another validation result
     * @param other Other validation result
     */
    void merge(const ValidationResult& other) {
        if (!other.is_valid()) {
            is_valid_ = false;
            errors_.insert(errors_.end(), other.errors().begin(), other.errors().end());
        }
    }

private:
    bool is_valid_{true};
    std::vector<ValidationError> errors_;
};

/**
 * @class ComponentFactory
 * @brief Factory for creating ETL components
 * @ingroup core
 * 
 * @details
 * Creates and manages ETL component instances. Provides a registry for component
 * creators and handles component lifecycle management.
 * 
 * @tparam T The type of component to create (must implement IExtractor, ITransformer, or ILoader)
 * 
 * @dependencies
 * - IExtractor/ITransformer/ILoader: Component interfaces
 * 
 * @see @ref factory "Component Factory Pattern"
 */
template<typename T>
class ComponentFactory {
public:
    using Creator = std::function<std::unique_ptr<T>()>;

    /**
     * @brief Register component creator
     * @param type Component type name
     * @param creator Creator function
     */
    void register_creator(const std::string& type, Creator creator) {
        creators_[type] = std::move(creator);
    }

    /**
     * @brief Create component by type
     * @param type Component type name
     * @return std::unique_ptr<T> Created component
     */
    [[nodiscard]] std::unique_ptr<T> create(const std::string& type) const {
        auto it = creators_.find(type);
        if (it != creators_.end()) {
            return it->second();
        }
        throw common::ConfigurationException(
            std::format("Unknown component type: '{}'", type));
    }

    /**
     * @brief Get registered types
     * @return std::vector<std::string> Vector of type names
     */
    [[nodiscard]] std::vector<std::string> get_registered_types() const {
        std::vector<std::string> types;
        types.reserve(creators_.size());
        for (const auto& [type, _] : creators_) {
            types.push_back(type);
        }
        return types;
    }
    
    /**
     * @brief Check if a type is registered
     * @param type Component type name
     * @return bool True if type is registered
     */
    [[nodiscard]] bool is_registered(const std::string& type) const {
        return creators_.find(type) != creators_.end();
    }
    
    /**
     * @brief Get number of registered types
     * @return size_t Number of registered types
     */
    [[nodiscard]] size_t registered_count() const noexcept {
        return creators_.size();
    }

private:
    std::unordered_map<std::string, Creator> creators_;
};

/**
 * @brief Interface for data processing operations
 * 
 * @section description Description
 * Defines the contract for data processing operations in the ETL pipeline.
 * Implementations should handle data transformation, validation, and processing
 * according to OMOP CDM specifications.
 * 
 * @section thread_safety Thread Safety
 * Implementations must be thread-safe as this interface may be used in
 * concurrent processing scenarios.
 * 
 * @section error_handling Error Handling
 * Implementations should throw appropriate exceptions for error conditions
 * and provide detailed error messages.
 */
class IDataProcessor {
public:
    /**
     * @brief Virtual destructor for proper cleanup
     */
    virtual ~IDataProcessor() = default;

    /**
     * @brief Process a batch of data
     * 
     * @param data The input data to process
     * @return bool True if processing was successful
     * @throw std::runtime_error if processing fails
     * 
     * @section details Implementation Details
     * - Validates input data format
     * - Applies transformations according to OMOP CDM rules
     * - Performs data quality checks
     * - Returns success status
     */
    virtual bool processData(const std::vector<std::string>& data) = 0;

    /**
     * @brief Get the current processing status
     * 
     * @return std::string Status message
     * 
     * @section details Implementation Details
     * - Returns current processing state
     * - Includes progress information if available
     * - May include error messages if processing failed
     */
    virtual std::string getStatus() const = 0;
};

/**
 * @brief Interface for data validation operations
 * 
 * @section description Description
 * Defines the contract for data validation in the ETL pipeline.
 * Implementations should ensure data quality and compliance with
 * OMOP CDM specifications.
 * 
 * @section thread_safety Thread Safety
 * Implementations must be thread-safe as this interface may be used in
 * concurrent processing scenarios.
 */
class IDataValidator {
public:
    /**
     * @brief Virtual destructor for proper cleanup
     */
    virtual ~IDataValidator() = default;

    /**
     * @brief Validate a data record
     * 
     * @param record The data record to validate
     * @return bool True if validation passes
     * @throw std::invalid_argument if record format is invalid
     * 
     * @section details Implementation Details
     * - Checks data format and structure
     * - Validates against OMOP CDM rules
     * - Performs data quality checks
     * - Returns validation status
     */
    virtual bool validateRecord(const std::string& record) = 0;

    /**
     * @brief Get validation errors
     * 
     * @return std::vector<std::string> List of validation errors
     * 
     * @section details Implementation Details
     * - Returns all validation errors found
     * - Errors are ordered by severity
     * - Includes detailed error messages
     */
    virtual std::vector<std::string> getErrors() const = 0;
};

/**
 * @brief Interface for data source operations
 * 
 * @section description Description
 * Defines the contract for data source operations in the ETL pipeline.
 * Implementations should handle data retrieval from various sources
 * according to OMOP CDM specifications.
 * 
 * @section thread_safety Thread Safety
 * Implementations must be thread-safe as this interface may be used in
 * concurrent processing scenarios.
 */
class IDataSource {
public:
    /**
     * @brief Virtual destructor for proper cleanup
     */
    virtual ~IDataSource() = default;

    /**
     * @brief Read data from the source
     * 
     * @param batchSize Number of records to read
     * @return std::optional<std::vector<std::string>> Batch of records
     * @throw std::runtime_error if reading fails
     * 
     * @section details Implementation Details
     * - Reads specified number of records
     * - Handles source-specific formatting
     * - Returns empty optional if no more data
     */
    virtual std::optional<std::vector<std::string>> readData(size_t batchSize) = 0;

    /**
     * @brief Check if more data is available
     * 
     * @return bool True if more data can be read
     * 
     * @section details Implementation Details
     * - Checks source for remaining data
     * - Considers source-specific conditions
     * - Returns availability status
     */
    virtual bool hasMoreData() const = 0;
};

/**
 * @brief Interface for data sink operations
 * 
 * @section description Description
 * Defines the contract for data sink operations in the ETL pipeline.
 * Implementations should handle data writing to various destinations
 * according to OMOP CDM specifications.
 * 
 * @section thread_safety Thread Safety
 * Implementations must be thread-safe as this interface may be used in
 * concurrent processing scenarios.
 */
class IDataSink {
public:
    /**
     * @brief Virtual destructor for proper cleanup
     */
    virtual ~IDataSink() = default;

    /**
     * @brief Write data to the sink
     * 
     * @param data The data to write
     * @return bool True if writing was successful
     * @throw std::runtime_error if writing fails
     * 
     * @section details Implementation Details
     * - Writes data to destination
     * - Handles sink-specific formatting
     * - Returns success status
     */
    virtual bool writeData(const std::vector<std::string>& data) = 0;

    /**
     * @brief Flush any buffered data
     * 
     * @return bool True if flush was successful
     * @throw std::runtime_error if flush fails
     * 
     * @section details Implementation Details
     * - Ensures all data is written
     * - Handles sink-specific cleanup
     * - Returns success status
     */
    virtual bool flush() = 0;
};

} // namespace omop::core

File src/lib/core/record.h:

/**
 * @file record.h
 * @brief Data record representation for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header file defines the core data structures for representing and
 * manipulating records in the OMOP ETL pipeline. It provides a flexible
 * and type-safe way to handle data records as they flow through the
 * extraction, transformation, and loading stages.
 * 
 * @section design Design Principles
 * - Type Safety: Strong typing with std::any for field values
 * - Flexibility: Support for dynamic field addition/removal
 * - Metadata: Rich metadata for fields and records
 * - Performance: Efficient memory management and copying
 * - Serialization: JSON support for data exchange
 * 
 * @section components Components
 * - Record: Individual data record with fields and metadata
 * - RecordBatch: Collection of records for batch processing
 * - FieldMetadata: Field-level metadata and validation
 * - RecordMetadata: Record-level metadata and tracking
 * 
 * @section usage Usage
 * To use the record system:
 * 1. Create records with initial data
 * 2. Manipulate fields and metadata
 * 3. Process records individually or in batches
 * 4. Serialize/deserialize as needed
 * 
 * @section example Example
 * @code
 * Record record;
 * record.setField("person_id", 12345);
 * record.setField("birth_date", "1980-01-01");
 * RecordMetadata meta;
 * meta.source_table = "patients";
 * record.setMetadata(meta);
 * @endcode
 */

#pragma once

#include <string>
#include <unordered_map>
#include <any>
#include <vector>
#include <chrono>
#include <optional>
#include <memory>
#include <variant>

namespace omop::core {

/**
 * @brief Represents a single data record in the ETL pipeline
 * 
 * @section description Description
 * Encapsulates a data record as it flows through the pipeline, providing
 * a unified interface for accessing and manipulating field values regardless
 * of the source or target data format. Supports rich metadata and type-safe
 * field access.
 * 
 * @section features Features
 * - Dynamic field management
 * - Type-safe value access
 * - Rich metadata support
 * - JSON serialization
 * - Field selection and renaming
 * - Record merging
 * 
 * @section thread_safety Thread Safety
 * Individual Record instances are not thread-safe. For concurrent access,
 * use appropriate synchronization mechanisms.
 */
class Record {
public:
    /**
     * @brief Field metadata and validation information
     * 
     * @section description Description
     * Contains metadata and validation rules for individual fields,
     * including data type information, nullability, and source mapping.
     * 
     * @section fields Fields
     * - name: Field identifier
     * - data_type: Expected data type
     * - is_nullable: Null value support
     * - source_column: Original source mapping
     * - description: Field documentation
     */
    struct FieldMetadata {
        std::string name;           ///< Field name
        std::string data_type;      ///< Data type
        bool is_nullable{true};     ///< Whether field can be null
        std::string source_column;  ///< Original source column name
        std::string description;    ///< Field description
    };

    /**
     * @brief Record-level metadata and tracking information
     * 
     * @section description Description
     * Contains metadata about the record's origin, processing history,
     * and custom attributes. Used for tracking and debugging.
     * 
     * @section fields Fields
     * - source_table: Origin table/file
     * - target_table: Destination table
     * - source_row_number: Original row number
     * - extraction_time: When record was extracted
     * - record_id: Unique identifier
     * - custom: Additional metadata
     */
    struct RecordMetadata {
        std::string source_table;   ///< Source table/file name
        std::string target_table;   ///< Target table name
        size_t source_row_number{0}; ///< Row number in source
        std::chrono::system_clock::time_point extraction_time; ///< When record was extracted
        std::string record_id;      ///< Unique record identifier
        std::unordered_map<std::string, std::string> custom; ///< Custom metadata
    };

    /**
     * @brief Default constructor
     * 
     * @section details Implementation Details
     * Creates an empty record with no fields or metadata
     */
    Record() = default;

    /**
     * @brief Constructor with initial data
     * @param data Initial field data
     * 
     * @section details Implementation Details
     * Creates a record with the provided field values
     * and default metadata
     */
    explicit Record(const std::unordered_map<std::string, std::any>& data);

    /**
     * @brief Constructor with metadata
     * @param data Initial field data
     * @param metadata Record metadata
     * 
     * @section details Implementation Details
     * Creates a record with the provided field values
     * and metadata
     */
    Record(const std::unordered_map<std::string, std::any>& data,
           const RecordMetadata& metadata);

    /**
     * @brief Copy constructor
     * 
     * @section details Implementation Details
     * Creates a deep copy of the record, including
     * all fields and metadata
     */
    Record(const Record& other) = default;

    /**
     * @brief Move constructor
     * 
     * @section details Implementation Details
     * Moves ownership of fields and metadata from
     * the source record
     */
    Record(Record&& other) noexcept = default;

    /**
     * @brief Copy assignment operator
     * 
     * @section details Implementation Details
     * Assigns a deep copy of the source record
     */
    Record& operator=(const Record& other) = default;

    /**
     * @brief Move assignment operator
     * 
     * @section details Implementation Details
     * Moves ownership of fields and metadata from
     * the source record
     */
    Record& operator=(Record&& other) noexcept = default;

    /**
     * @brief Destructor
     * 
     * @section details Implementation Details
     * Cleans up record resources
     */
    virtual ~Record() = default;

    /**
     * @brief Set a field value
     * @param field_name Field name
     * @param value Field value
     * 
     * @section details Implementation Details
     * - Adds or updates the field value
     * - Preserves existing field metadata
     */
    void setField(const std::string& field_name, const std::any& value);

    /**
     * @brief Get a field value
     * @param field_name Field name
     * @return Field value
     * @throws std::out_of_range if field not found
     * 
     * @section details Implementation Details
     * - Returns const reference to field value
     * - Throws if field doesn't exist
     */
    const std::any& getField(const std::string& field_name) const;

    /**
     * @brief Get a field value with type conversion
     * @tparam T Target type
     * @param field_name Field name
     * @return Field value as type T
     * @throws std::bad_any_cast if type conversion fails
     * 
     * @section details Implementation Details
     * - Attempts to convert field value to type T
     * - Throws if conversion fails
     */
    template<typename T>
    T getFieldAs(const std::string& field_name) const {
        const auto& value = getField(field_name);
        return std::any_cast<T>(value);
    }

    /**
     * @brief Get optional field value
     * @param field_name Field name
     * @return Optional containing field value if exists
     * 
     * @section details Implementation Details
     * - Returns empty optional if field doesn't exist
     * - No exceptions thrown
     */
    std::optional<std::any> getFieldOptional(const std::string& field_name) const;

    /**
     * @brief Check if field exists
     * @param field_name Field name
     * @return true if field exists
     * 
     * @section details Implementation Details
     * - Checks for field presence
     * - No exceptions thrown
     */
    bool hasField(const std::string& field_name) const;

    /**
     * @brief Check if field is null
     * @param field_name Field name
     * @return true if field is null or doesn't exist
     * 
     * @section details Implementation Details
     * - Returns true for non-existent fields
     * - No exceptions thrown
     */
    bool isFieldNull(const std::string& field_name) const;

    /**
     * @brief Remove a field
     * @param field_name Field name
     * @return true if field was removed
     * 
     * @section details Implementation Details
     * - Removes field and its metadata
     * - Returns false if field didn't exist
     */
    bool removeField(const std::string& field_name);

    /**
     * @brief Clear all fields
     * 
     * @section details Implementation Details
     * - Removes all fields and their metadata
     * - Preserves record metadata
     */
    void clear();

    /**
     * @brief Get all field names
     * @return Vector of field names
     * 
     * @section details Implementation Details
     * - Returns names in arbitrary order
     * - No exceptions thrown
     */
    std::vector<std::string> getFieldNames() const;

    /**
     * @brief Get all fields
     * @return Map of field names to values
     * 
     * @section details Implementation Details
     * - Returns const reference to field map
     * - No exceptions thrown
     */
    const std::unordered_map<std::string, std::any>& getFields() const { return fields_; }

    /**
     * @brief Get mutable reference to fields
     * @return Mutable map of field names to values
     * 
     * @section details Implementation Details
     * - Returns reference to field map
     * - No exceptions thrown
     */
    std::unordered_map<std::string, std::any>& getFieldsMutable() { return fields_; }

    /**
     * @brief Get number of fields
     * @return Field count
     * 
     * @section details Implementation Details
     * - Returns current field count
     * - No exceptions thrown
     */
    size_t getFieldCount() const { return fields_.size(); }

    /**
     * @brief Check if record is empty
     * @return true if no fields
     * 
     * @section details Implementation Details
     * - Returns true if no fields
     * - No exceptions thrown
     */
    bool isEmpty() const { return fields_.empty(); }

    /**
     * @brief Get record metadata
     * @return Record metadata
     * 
     * @section details Implementation Details
     * - Returns const reference to metadata
     * - No exceptions thrown
     */
    const RecordMetadata& getMetadata() const { return metadata_; }

    /**
     * @brief Get mutable record metadata
     * @return Mutable record metadata
     * 
     * @section details Implementation Details
     * - Returns reference to metadata
     * - No exceptions thrown
     */
    RecordMetadata& getMetadataMutable() { return metadata_; }

    /**
     * @brief Set record metadata
     * @param metadata New metadata
     * 
     * @section details Implementation Details
     * - Replaces existing metadata
     * - No exceptions thrown
     */
    void setMetadata(const RecordMetadata& metadata) { metadata_ = metadata; }

    /**
     * @brief Set field metadata
     * @param field_name Field name
     * @param metadata Field metadata
     * 
     * @section details Implementation Details
     * - Updates metadata for existing field
     * - No exceptions thrown
     */
    void setFieldMetadata(const std::string& field_name, const FieldMetadata& metadata);

    /**
     * @brief Get field metadata
     * @param field_name Field name
     * @return Optional field metadata
     * 
     * @section details Implementation Details
     * - Returns empty optional if no metadata
     * - No exceptions thrown
     */
    std::optional<FieldMetadata> getFieldMetadata(const std::string& field_name) const;

    /**
     * @brief Merge another record into this one
     * @param other Record to merge
     * @param overwrite Whether to overwrite existing fields
     * 
     * @section details Implementation Details
     * - Combines fields from both records
     * - Optionally overwrites existing fields
     * - Preserves metadata from this record
     */
    void merge(const Record& other, bool overwrite = true);

    /**
     * @brief Create a copy with selected fields
     * @param field_names Fields to include
     * @return New record with selected fields
     * 
     * @section details Implementation Details
     * - Creates new record with specified fields
     * - Preserves field metadata
     * - Preserves record metadata
     */
    Record selectFields(const std::vector<std::string>& field_names) const;

    /**
     * @brief Rename a field
     * @param old_name Current field name
     * @param new_name New field name
     * @return true if field was renamed
     * 
     * @section details Implementation Details
     * - Updates field name and metadata
     * - Returns false if field doesn't exist
     * - Returns false if new name exists
     */
    bool renameField(const std::string& old_name, const std::string& new_name);

    /**
     * @brief Convert record to JSON string
     * @param pretty Whether to format with indentation
     * @return JSON representation
     * 
     * @section details Implementation Details
     * - Serializes fields and metadata
     * - Supports pretty printing
     * - No exceptions thrown
     */
    std::string toJson(bool pretty = false) const;

    /**
     * @brief Create record from JSON string
     * @param json JSON representation
     * @return New record
     * 
     * @section details Implementation Details
     * - Parses JSON string
     * - Creates record with fields
     * - Sets metadata if present
     */
    static Record fromJson(const std::string& json);

    /**
     * @brief Convert record to string representation
     * @return String representation
     */
    std::string toString() const;

    /**
     * @brief Equality operator
     * @param other Other record
     * @return true if records are equal
     */
    bool operator==(const Record& other) const;

    /**
     * @brief Inequality operator
     * @param other Other record
     * @return true if records are not equal
     */
    bool operator!=(const Record& other) const { return !(*this == other); }

private:
    std::unordered_map<std::string, std::any> fields_; ///< Field values
    std::unordered_map<std::string, FieldMetadata> field_metadata_; ///< Field metadata
    RecordMetadata metadata_; ///< Record metadata
};

/**
 * @brief Collection of records for batch processing
 * 
 * @section description Description
 * Manages a collection of records for efficient batch processing
 * in the ETL pipeline. Provides methods for adding, accessing,
 * and managing records in bulk.
 * 
 * @section features Features
 * - Fixed or dynamic capacity
 * - Efficient memory management
 * - Batch operations
 * - Const access to records
 * 
 * @section thread_safety Thread Safety
 * Individual RecordBatch instances are not thread-safe. For concurrent
 * access, use appropriate synchronization mechanisms.
 */
class RecordBatch {
public:
    /**
     * @brief Constructor with capacity
     * @param capacity Initial capacity
     * 
     * @section details Implementation Details
     * - Pre-allocates memory for records
     * - No exceptions thrown
     */
    explicit RecordBatch(size_t capacity);

    /**
     * @brief Add record by copy
     * @param record Record to add
     * 
     * @section details Implementation Details
     * - Makes a copy of the record
     * - No exceptions thrown
     */
    void addRecord(const Record& record);

    /**
     * @brief Add record by move
     * @param record Record to add
     * 
     * @section details Implementation Details
     * - Takes ownership of the record
     * - No exceptions thrown
     */
    void addRecord(Record&& record);

    /**
     * @brief Get record by index
     * @param index Record index
     * @return Const reference to record
     * @throws std::out_of_range if index invalid
     * 
     * @section details Implementation Details
     * - Returns const reference to record
     * - Throws if index out of range
     */
    const Record& getRecord(size_t index) const;

    /**
     * @brief Get mutable record by index
     * @param index Record index
     * @return Mutable reference to record
     * @throws std::out_of_range if index invalid
     *
     * @section details Implementation Details
     * - Returns mutable reference to record
     * - Throws if index out of range
     */
    Record& getRecordMutable(size_t index);

    /**
     * @brief Get all records
     * @return Const reference to record vector
     *
     * @section details Implementation Details
     * - Returns const reference to records
     * - No exceptions thrown
     */
    const std::vector<Record>& getRecords() const { return records_; }

    /**
     * @brief Get number of records
     * @return Record count
     * 
     * @section details Implementation Details
     * - Returns current record count
     * - No exceptions thrown
     */
    size_t size() const { return records_.size(); }

    /**
     * @brief Check if batch is empty
     * @return true if no records
     * 
     * @section details Implementation Details
     * - Returns true if no records
     * - No exceptions thrown
     */
    bool isEmpty() const { return records_.empty(); }

    /**
     * @brief Check if batch is empty
     * @return true if no records
     * 
     * @section details Implementation Details
     * - Alias for isEmpty()
     * - No exceptions thrown
     */
    bool empty() const { return records_.empty(); }

    /**
     * @brief Clear all records
     * 
     * @section details Implementation Details
     * - Removes all records
     * - No exceptions thrown
     */
    void clear() { records_.clear(); }

    /**
     * @brief Reserve capacity
     * @param capacity Desired capacity
     * 
     * @section details Implementation Details
     * - Pre-allocates memory
     * - No exceptions thrown
     */
    void reserve(size_t capacity) { records_.reserve(capacity); }

    /**
     * @brief Iterator support
     */
    auto begin() { return records_.begin(); }
    auto end() { return records_.end(); }
    auto begin() const { return records_.begin(); }
    auto end() const { return records_.end(); }

private:
    std::vector<Record> records_; ///< Record collection
};

} // namespace omop::core

File src/lib/core/job_manager.h:

/**
 * @file job_manager.h
 * @brief Job management system for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header file defines the job management system for the OMOP ETL pipeline.
 * It provides functionality for orchestrating, monitoring, and managing ETL jobs
 * throughout their lifecycle, including job submission, execution, monitoring,
 * and error handling.
 * 
 * @section design Design Principles
 * - Thread Safety: All operations are thread-safe
 * - Priority-based Scheduling: Jobs are executed based on priority
 * - Fault Tolerance: Built-in retry mechanism and checkpointing
 * - Resource Management: Controls concurrent job execution
 * 
 * @section components Components
 * - Job: Represents an individual ETL job
 * - JobManager: Orchestrates job execution and management
 * - JobConfig: Configuration for job execution
 * - JobStatistics: Runtime statistics and metrics
 * 
 * @section usage Usage
 * To use the job management system:
 * 1. Create a JobManager instance
 * 2. Configure and submit jobs
 * 3. Monitor job progress and status
 * 4. Handle job completion and errors
 * 
 * @section example Example
 * @code
 * auto job_manager = std::make_shared<JobManager>(config, logger);
 * JobConfig job_config{...};
 * std::string job_id = job_manager->submitJob(job_config);
 * @endcode
 */

#pragma once

#include <string>
#include <memory>
#include <vector>
#include <unordered_map>
#include <chrono>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <thread>
#include <functional>

#include "interfaces.h"
#include "pipeline.h"
#include "common/logging.h"
#include "common/configuration.h"

namespace omop::core {

// JobStatus is defined in pipeline.h

/**
 * @brief ETL job priority levels
 * 
 * @section description Description
 * Defines the priority levels for ETL jobs, which determine the order
 * of job execution when multiple jobs are queued.
 * 
 * @section values Values
 * - LOW: Low priority jobs
 * - NORMAL: Standard priority jobs
 * - HIGH: High priority jobs
 * - CRITICAL: Critical priority jobs
 */
enum class JobPriority {
    LOW = 0,        ///< Low priority jobs
    NORMAL = 1,     ///< Standard priority jobs
    HIGH = 2,       ///< High priority jobs
    CRITICAL = 3    ///< Critical priority jobs
};

/**
 * @brief Job execution statistics
 * 
 * @section description Description
 * Contains runtime statistics and metrics for an ETL job, including
 * processing rates, resource usage, and timing information.
 * 
 * @section metrics Metrics
 * - Record counts (total, successful, failed, skipped)
 * - Processing rate (records per second)
 * - Resource usage (memory, CPU)
 * - Timing information (elapsed time, stage timings)
 */
struct JobStatistics {
    size_t total_records_processed{0};    ///< Total records processed
    size_t successful_records{0};         ///< Successfully processed records
    size_t failed_records{0};             ///< Failed records
    size_t skipped_records{0};            ///< Skipped records
    double processing_rate{0.0};          ///< Records per second
    size_t memory_usage_mb{0};            ///< Memory usage in MB
    double cpu_usage_percent{0.0};        ///< CPU usage percentage
    std::chrono::duration<double> elapsed_time; ///< Total elapsed time
    std::unordered_map<std::string, double> stage_timings; ///< Timing per stage
};

/**
 * @brief ETL job configuration
 * 
 * @section description Description
 * Contains configuration parameters for an ETL job, including
 * job identification, execution parameters, and metadata.
 * 
 * @section parameters Parameters
 * - Job identification (ID, name)
 * - Pipeline configuration
 * - Execution parameters (priority, retries, timeout)
 * - Checkpointing settings
 * - Custom parameters and metadata
 */
struct JobConfig {
    std::string job_id;                   ///< Unique job identifier
    std::string job_name;                 ///< Human-readable job name
    std::string pipeline_config_path;     ///< Path to pipeline configuration
    JobPriority priority{JobPriority::NORMAL}; ///< Job priority
    size_t max_retries{3};                ///< Maximum retry attempts
    std::chrono::seconds retry_delay{60}; ///< Delay between retries
    std::chrono::seconds timeout{0};      ///< Job timeout (0 = no timeout)
    bool enable_checkpointing{true};      ///< Enable job checkpointing
    size_t checkpoint_interval{10000};    ///< Checkpoint interval (records)
    std::unordered_map<std::string, std::string> parameters; ///< Job parameters
    std::unordered_map<std::string, std::string> metadata;   ///< Job metadata
};

/**
 * @brief ETL job instance
 * 
 * @section description Description
 * Represents an individual ETL job, managing its lifecycle and state.
 * Provides methods for job control, status monitoring, and statistics tracking.
 * 
 * @section lifecycle Lifecycle
 * 1. Creation: Job is created with configuration
 * 2. Execution: Job runs through the pipeline
 * 3. Monitoring: Status and statistics are tracked
 * 4. Completion: Job finishes or fails
 * 
 * @section thread_safety Thread Safety
 * All public methods are thread-safe, using appropriate synchronization
 * mechanisms for concurrent access.
 */
class Job {
public:
    /**
     * @brief Constructor
     * @param config Job configuration
     * @param pipeline Pipeline instance
     * 
     * @section details Implementation Details
     * - Initializes job with provided configuration
     * - Sets up pipeline instance
     * - Initializes statistics and state
     */
    Job(const JobConfig& config, std::unique_ptr<ETLPipeline> pipeline);

    /**
     * @brief Get job ID
     * @return Job identifier
     * 
     * @section details Implementation Details
     * - Returns the unique identifier assigned to the job
     * - Thread-safe access to job ID
     */
    const std::string& getId() const { return config_.job_id; }

    /**
     * @brief Get job name
     * @return Job name
     * 
     * @section details Implementation Details
     * - Returns the human-readable name of the job
     * - Thread-safe access to job name
     */
    const std::string& getName() const { return config_.job_name; }

    /**
     * @brief Get job status
     * @return Current job status
     * 
     * @section details Implementation Details
     * - Returns the current status of the job
     * - Thread-safe access using atomic operations
     */
    JobStatus getStatus() const { return status_.load(); }

    /**
     * @brief Set job status
     * @param status New status
     * 
     * @section details Implementation Details
     * - Updates the job status atomically
     * - May trigger status change notifications
     */
    void setStatus(JobStatus status);

    /**
     * @brief Get job configuration
     * @return Job configuration
     * 
     * @section details Implementation Details
     * - Returns a const reference to the job configuration
     * - Thread-safe access to configuration
     */
    const JobConfig& getConfig() const { return config_; }

    /**
     * @brief Get job statistics
     * @return Job statistics
     * 
     * @section details Implementation Details
     * - Returns current job statistics
     * - Thread-safe access using mutex
     */
    JobStatistics getStatistics() const;

    /**
     * @brief Get pipeline instance
     * @return Pipeline pointer
     * 
     * @section details Implementation Details
     * - Returns pointer to the pipeline instance
     * - Thread-safe access to pipeline
     */
    ETLPipeline* getPipeline() { return pipeline_.get(); }

    /**
     * @brief Get creation time
     * @return Creation timestamp
     * 
     * @section details Implementation Details
     * - Returns the time when the job was created
     * - Thread-safe access to timestamp
     */
    std::chrono::system_clock::time_point getCreationTime() const { return creation_time_; }

    /**
     * @brief Get start time
     * @return Start timestamp
     * 
     * @section details Implementation Details
     * - Returns the time when the job started execution
     * - Thread-safe access to timestamp
     */
    std::chrono::system_clock::time_point getStartTime() const { return start_time_; }

    /**
     * @brief Get end time
     * @return End timestamp
     * 
     * @section details Implementation Details
     * - Returns the time when the job completed
     * - Thread-safe access to timestamp
     */
    std::chrono::system_clock::time_point getEndTime() const { return end_time_; }

    /**
     * @brief Get error message
     * @return Error message if job failed
     * 
     * @section details Implementation Details
     * - Returns the error message if job failed
     * - Thread-safe access to error message
     */
    const std::string& getErrorMessage() const { return error_message_; }

    /**
     * @brief Set error message
     * @param message Error message
     * 
     * @section details Implementation Details
     * - Sets the error message for the job
     * - Thread-safe access to error message
     */
    void setErrorMessage(const std::string& message) { error_message_ = message; }

    /**
     * @brief Get retry count
     * @return Number of retries
     * 
     * @section details Implementation Details
     * - Returns the number of retry attempts
     * - Thread-safe access to retry count
     */
    size_t getRetryCount() const { return retry_count_; }

    /**
     * @brief Increment retry count
     * 
     * @section details Implementation Details
     * - Increments the retry counter
     * - Thread-safe operation
     */
    void incrementRetryCount() { ++retry_count_; }

    /**
     * @brief Update job statistics
     * @param stats New statistics
     * 
     * @section details Implementation Details
     * - Updates job statistics with new values
     * - Thread-safe operation using mutex
     */
    void updateStatistics(const JobStatistics& stats);

    /**
     * @brief Check if job can be retried
     * @return true if job can be retried
     * 
     * @section details Implementation Details
     * - Checks retry count against maximum retries
     * - Thread-safe operation
     */
    bool canRetry() const;

    /**
     * @brief Save checkpoint
     * @return true if checkpoint saved successfully
     * 
     * @section details Implementation Details
     * - Saves current job state to checkpoint file
     * - Thread-safe operation
     */
    bool saveCheckpoint();

    /**
     * @brief Load checkpoint
     * @return true if checkpoint loaded successfully
     * 
     * @section details Implementation Details
     * - Loads job state from checkpoint file
     * - Thread-safe operation
     */
    bool loadCheckpoint();

private:
    friend class JobManager; // Allow JobManager to access private members

    JobConfig config_;                              ///< Job configuration
    std::unique_ptr<ETLPipeline> pipeline_;         ///< Pipeline instance
    std::atomic<JobStatus> status_{JobStatus::Created}; ///< Current status
    JobStatistics statistics_;                      ///< Job statistics
    mutable std::mutex stats_mutex_;                ///< Statistics mutex

    std::chrono::system_clock::time_point creation_time_; ///< Creation time
    std::chrono::system_clock::time_point start_time_;    ///< Start time
    std::chrono::system_clock::time_point end_time_;      ///< End time

    std::string error_message_;                     ///< Error message
    size_t retry_count_{0};                         ///< Retry count
    std::string checkpoint_path_;                   ///< Checkpoint file path
};

/**
 * @brief Job execution context
 * 
 * @section description Description
 * Contains the execution context for a job, including the job instance,
 * logger, and control mechanisms.
 * 
 * @section components Components
 * - Job instance
 * - Logger
 * - Progress callback
 * - Stop flag
 */
struct JobExecutionContext {
    std::shared_ptr<Job> job;                       ///< Job instance
    std::shared_ptr<common::Logger> logger;         ///< Logger instance
    std::function<void(const JobStatistics&)> progress_callback; ///< Progress callback
    std::atomic<bool> should_stop{false};           ///< Stop flag
};

/**
 * @brief Job comparator for priority queue
 * 
 * @section description Description
 * Defines the comparison logic for job priority queue, ensuring
 * jobs are processed in the correct order based on priority and
 * creation time.
 * 
 * @section ordering Ordering
 * 1. Higher priority jobs are processed first
 * 2. For equal priority, older jobs are processed first (FIFO)
 */
struct JobPriorityComparator {
    bool operator()(const std::shared_ptr<Job>& a, const std::shared_ptr<Job>& b) const {
        // Higher priority jobs should be processed first
        if (a->getConfig().priority != b->getConfig().priority) {
            return static_cast<int>(a->getConfig().priority) < 
                   static_cast<int>(b->getConfig().priority);
        }
        // If priorities are equal, older jobs should be processed first (FIFO)
        return a->getCreationTime() > b->getCreationTime();
    }
};

/**
 * @brief Job manager for orchestrating ETL jobs
 * 
 * @section description Description
 * Manages the lifecycle of ETL jobs, including submission, execution,
 * monitoring, and cleanup. Provides thread-safe operations for job
 * management and resource control.
 * 
 * @section features Features
 * - Job submission and scheduling
 * - Priority-based execution
 * - Concurrent job management
 * - Job monitoring and control
 * - Resource management
 * - Error handling and retries
 * 
 * @section thread_safety Thread Safety
 * All public methods are thread-safe, using appropriate synchronization
 * mechanisms for concurrent access.
 */
class JobManager {
public:
    /**
     * @brief Constructor
     * @param config Configuration manager
     * @param logger Logger instance
     * 
     * @section details Implementation Details
     * - Initializes job manager with configuration and logger
     * - Sets up worker threads and queues
     * - Configures resource limits
     */
    JobManager(std::shared_ptr<common::ConfigurationManager> config,
               std::shared_ptr<common::Logger> logger);

    /**
     * @brief Destructor
     */
    ~JobManager();

    /**
     * @brief Start the job manager
     * @return true if started successfully
     * 
     * @section details Implementation Details
     * - Initializes worker threads
     * - Starts job processing
     * - Returns success status
     */
    bool start();

    /**
     * @brief Stop the job manager
     * 
     * @section details Implementation Details
     * - Stops worker threads
     * - Cancels running jobs
     * - Cleans up resources
     */
    void stop();

    /**
     * @brief Submit a new job
     * @param config Job configuration
     * @return Job ID
     * 
     * @section details Implementation Details
     * - Creates new job instance
     * - Adds to priority queue
     * - Returns job identifier
     */
    std::string submitJob(const JobConfig& config);

    /**
     * @brief Cancel a job
     * @param job_id Job identifier
     * @return true if cancelled successfully
     * 
     * @section details Implementation Details
     * - Stops job execution
     * - Updates job status
     * - Returns success status
     */
    bool cancelJob(const std::string& job_id);

    /**
     * @brief Pause a job
     * @param job_id Job identifier
     * @return true if paused successfully
     * 
     * @section details Implementation Details
     * - Pauses job execution
     * - Updates job status
     * - Returns success status
     */
    bool pauseJob(const std::string& job_id);

    /**
     * @brief Resume a job
     * @param job_id Job identifier
     * @return true if resumed successfully
     * 
     * @section details Implementation Details
     * - Resumes job execution
     * - Updates job status
     * - Returns success status
     */
    bool resumeJob(const std::string& job_id);

    /**
     * @brief Retry a failed job
     * @param job_id Job identifier
     * @return true if retry started successfully
     * 
     * @section details Implementation Details
     * - Resets job state
     * - Increments retry count
     * - Restarts job execution
     */
    bool retryJob(const std::string& job_id);

    /**
     * @brief Get number of active jobs
     * @return Active job count
     * 
     * @section details Implementation Details
     * - Returns count of currently running jobs
     * - Thread-safe operation
     */
    size_t getActiveJobCount() const;

    /**
     * @brief Get number of queued jobs
     * @return Queued job count
     * 
     * @section details Implementation Details
     * - Returns count of jobs in queue
     * - Thread-safe operation
     */
    size_t getQueuedJobCount() const;

    /**
     * @brief Set maximum concurrent jobs
     * @param max_jobs Maximum concurrent jobs
     * 
     * @section details Implementation Details
     * - Updates maximum concurrent jobs limit
     * - Thread-safe operation
     */
    void setMaxConcurrentJobs(size_t max_jobs);

    /**
     * @brief Register job event callback
     * @param callback Event callback function
     * 
     * @section details Implementation Details
     * - Registers callback for job status changes
     * - Thread-safe operation
     */
    void registerJobEventCallback(
        std::function<void(const std::string&, JobStatus, JobStatus)> callback);

    /**
     * @brief Clean up old jobs
     * @param age Age threshold
     * @return Number of jobs cleaned up
     * 
     * @section details Implementation Details
     * - Removes completed jobs older than threshold
     * - Thread-safe operation
     */
    size_t cleanupOldJobs(std::chrono::hours age);

    /**
     * @brief Get job by ID
     * @param job_id Job identifier
     * @return Job instance or nullptr if not found
     */
    std::shared_ptr<Job> getJob(const std::string& job_id) const;

    /**
     * @brief Get all jobs
     * @return Vector of all jobs
     */
    std::vector<std::shared_ptr<Job>> getAllJobs() const;

    /**
     * @brief Get jobs by status
     * @param status Job status filter
     * @return Vector of jobs with specified status
     */
    std::vector<std::shared_ptr<Job>> getJobsByStatus(JobStatus status) const;

private:
    /**
     * @brief Worker thread function
     * 
     * @section details Implementation Details
     * - Processes jobs from queue
     * - Handles job execution
     * - Manages job lifecycle
     */
    void workerThread();

    /**
     * @brief Execute a job
     * @param context Job execution context
     * 
     * @section details Implementation Details
     * - Runs job through pipeline
     * - Handles job completion
     * - Updates job statistics
     */
    void executeJob(JobExecutionContext& context);

    /**
     * @brief Create pipeline instance
     * @param config_path Pipeline configuration path
     * @return Pipeline instance
     * 
     * @section details Implementation Details
     * - Creates pipeline from configuration
     * - Initializes pipeline components
     */
    std::unique_ptr<ETLPipeline> createPipeline(const std::string& config_path);

    /**
     * @brief Get next job from queue
     * @return Next job to process
     * 
     * @section details Implementation Details
     * - Gets highest priority job
     * - Thread-safe operation
     */
    std::shared_ptr<Job> getNextJob();

    /**
     * @brief Handle job completion
     * @param job Completed job
     * 
     * @section details Implementation Details
     * - Updates job status
     * - Triggers completion callbacks
     * - Updates statistics
     */
    void handleJobCompletion(std::shared_ptr<Job> job);

    /**
     * @brief Handle job failure
     * @param job Failed job
     * 
     * @section details Implementation Details
     * - Updates job status
     * - Handles retry logic
     * - Triggers failure callbacks
     */
    void handleJobFailure(std::shared_ptr<Job> job);

    /**
     * @brief Notify job status change
     * @param job_id Job identifier
     * @param old_status Previous status
     * @param new_status New status
     * 
     * @section details Implementation Details
     * - Triggers status change callbacks
     * - Updates job state
     */
    void notifyJobStatusChange(const std::string& job_id,
                              JobStatus old_status,
                              JobStatus new_status);

    std::shared_ptr<common::ConfigurationManager> config_; ///< Configuration manager
    std::shared_ptr<common::Logger> logger_;              ///< Logger instance
    std::vector<std::thread> worker_threads_;             ///< Worker threads
    std::priority_queue<std::shared_ptr<Job>,
                       std::vector<std::shared_ptr<Job>>,
                       JobPriorityComparator> job_queue_; ///< Job queue
    std::unordered_map<std::string, std::shared_ptr<Job>> active_jobs_map_; ///< Active jobs map
    std::unordered_map<std::string, std::shared_ptr<Job>> jobs_;     ///< All jobs
    mutable std::mutex queue_mutex_;                     ///< Queue mutex
    mutable std::mutex jobs_mutex_;                      ///< Jobs mutex
    mutable std::mutex callbacks_mutex_;                 ///< Callbacks mutex
    std::condition_variable queue_cv_;                   ///< Queue condition variable
    std::atomic<bool> running_{false};                   ///< Running flag
    std::atomic<size_t> active_jobs_{0};                 ///< Active job count
    std::atomic<size_t> max_concurrent_jobs_{4};         ///< Max concurrent jobs
    std::function<void(const std::string&, JobStatus, JobStatus)> status_callback_; ///< Status callback
    std::vector<std::function<void(const std::string&, JobStatus, JobStatus)>> event_callbacks_; ///< Event callbacks
};

} // namespace omop::core

File src/lib/core/pipeline.h:

/**
 * @file pipeline.h
 * @brief ETL Pipeline implementation for OMOP CDM data processing
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header file defines the core ETL pipeline components for the OMOP CDM
 * data processing system. It provides a robust framework for orchestrating
 * data extraction, transformation, and loading operations with support for
 * parallel processing, error handling, and checkpointing.
 * 
 * @section design Design Principles
 * - Modularity: Clear separation of ETL stages
 * - Parallelism: Multi-threaded processing
 * - Fault Tolerance: Error handling and recovery
 * - Monitoring: Progress tracking and statistics
 * - Checkpointing: State persistence for recovery
 * 
 * @section components Components
 * - ETLPipeline: Main pipeline orchestrator
 * - PipelineManager: Job scheduling and management
 * - JobInfo: Job status and statistics
 * - PipelineConfig: Pipeline configuration
 * 
 * @section usage Usage
 * To use the ETL pipeline:
 * 1. Configure pipeline components (extractor, transformer, loader)
 * 2. Set up pipeline configuration
 * 3. Start pipeline execution
 * 4. Monitor progress and handle results
 * 
 * @section example Example
 * @code
 * PipelineConfig config{...};
 * ETLPipeline pipeline(config);
 * pipeline.set_extractor(std::make_unique<MyExtractor>());
 * pipeline.set_transformer(std::make_unique<MyTransformer>());
 * pipeline.set_loader(std::make_unique<MyLoader>());
 * auto future = pipeline.start("job_123");
 * @endcode
 */

#pragma once

#include "interfaces.h"
#include "common/configuration.h"
#include "common/exceptions.h"
#include <memory>
#include <vector>
#include <thread>
#include <atomic>
#include <future>
#include <queue>
#include <condition_variable>
#include <chrono>
#include <functional>
#include <string>
#include <unordered_map>
#include <any>
#include <nlohmann/json.hpp>

namespace omop::core {

/**
 * @brief ETL job status enumeration
 * 
 * @section description Description
 * Defines the possible states of an ETL job throughout its lifecycle,
 * from creation to completion or failure.
 * 
 * @section states States
 * - Created: Initial state when job is created
 * - Initializing: Job is being set up
 * - Running: Job is actively processing
 * - Paused: Job execution is temporarily suspended
 * - Completed: Job finished successfully
 * - Failed: Job encountered an error
 * - Cancelled: Job was manually cancelled
 */
enum class JobStatus {
    Created,      ///< Initial state
    Initializing, ///< Setup phase
    Running,      ///< Active processing
    Paused,       ///< Temporarily suspended
    Completed,    ///< Successfully finished
    Failed,       ///< Error occurred
    Cancelled     ///< Manually cancelled
};

/**
 * @brief ETL job information and statistics
 * 
 * @section description Description
 * Contains comprehensive information about an ETL job's execution,
 * including status, timing, progress metrics, and error tracking.
 * 
 * @section metrics Metrics
 * - Timing: Start/end times and duration
 * - Progress: Record counts and percentages
 * - Errors: Error counts and messages
 * - Metadata: Custom job information
 */
class JobInfo {
public:
    std::string job_id;      ///< Unique job identifier
    std::string job_name;    ///< Human-readable job name
    JobStatus status{JobStatus::Created}; ///< Current job status
    std::chrono::system_clock::time_point start_time; ///< Job start time
    std::chrono::system_clock::time_point end_time;   ///< Job end time
    size_t total_records{0};     ///< Total records to process
    size_t processed_records{0}; ///< Records processed so far
    size_t error_records{0};     ///< Records with errors
    std::vector<std::string> error_messages; ///< Error messages
    std::unordered_map<std::string, std::any> metadata; ///< Custom metadata

    /**
     * @brief Calculate job duration
     * @return Duration in seconds
     * 
     * @section details Implementation Details
     * - For running jobs: current time - start time
     * - For completed jobs: end time - start time
     */
    [[nodiscard]] std::chrono::duration<double> duration() const {
        if (status == JobStatus::Running) {
            return std::chrono::system_clock::now() - start_time;
        }
        return end_time - start_time;
    }

    /**
     * @brief Calculate progress percentage
     * @return Progress as percentage (0-100)
     * 
     * @section details Implementation Details
     * - Returns 0 if no records to process
     * - Otherwise: (processed / total) * 100
     */
    [[nodiscard]] double progress() const {
        if (total_records == 0) return 0.0;
        return (static_cast<double>(processed_records) / total_records) * 100.0;
    }

    /**
     * @brief Calculate error rate
     * @return Error rate as ratio (0-1)
     * 
     * @section details Implementation Details
     * - Returns 0 if no records processed
     * - Otherwise: errors / processed
     */
    [[nodiscard]] double error_rate() const {
        if (processed_records == 0) return 0.0;
        return static_cast<double>(error_records) / processed_records;
    }
};

/**
 * @brief ETL pipeline configuration
 * 
 * @section description Description
 * Contains configuration parameters for the ETL pipeline, controlling
 * batch processing, parallelism, error handling, and checkpointing.
 * 
 * @section parameters Parameters
 * - Batch size and parallelism
 * - Queue capacity
 * - Error thresholds
 * - Checkpoint settings
 * - Validation options
 */
struct PipelineConfig {
    size_t batch_size{1000};           ///< Records per batch
    size_t max_parallel_batches{4};    ///< Maximum parallel batches
    size_t queue_size{10000};          ///< Maximum queue size
    size_t commit_interval{10000};     ///< Records between commits
    double error_threshold{0.01};      ///< Maximum error rate
    bool stop_on_error{true};          ///< Stop on first error
    bool validate_records{true};       ///< Enable record validation
    std::chrono::seconds checkpoint_interval{300}; ///< Checkpoint interval
    std::string checkpoint_dir;        ///< Checkpoint directory
};

/**
 * @brief ETL pipeline orchestrator
 * 
 * @section description Description
 * Coordinates the entire ETL process, managing data extraction,
 * transformation, and loading operations. Provides thread-safe
 * operations and comprehensive error handling.
 * 
 * @section features Features
 * - Multi-threaded processing
 * - Progress monitoring
 * - Error handling
 * - Checkpointing
 * - Pre/post processing hooks
 * 
 * @section thread_safety Thread Safety
 * All public methods are thread-safe, using appropriate synchronization
 * mechanisms for concurrent access.
 */
class ETLPipeline {
public:
    /**
     * @brief Constructor
     * @param config Pipeline configuration
     * 
     * @section details Implementation Details
     * - Initializes pipeline with provided configuration
     * - Sets up processing queues and threads
     * - Prepares error handling
     */
    explicit ETLPipeline(PipelineConfig config = {});

    /**
     * @brief Destructor
     * 
     * @section details Implementation Details
     * - Stops all processing threads
     * - Cleans up resources
     * - Saves final state
     */
    ~ETLPipeline();

    /**
     * @brief Set data extractor
     * @param extractor Data extractor instance
     * 
     * @section details Implementation Details
     * - Sets the extractor component
     * - Thread-safe operation
     */
    void set_extractor(std::unique_ptr<IExtractor> extractor);

    /**
     * @brief Set data transformer
     * @param transformer Data transformer instance
     * 
     * @section details Implementation Details
     * - Sets the transformer component
     * - Thread-safe operation
     */
    void set_transformer(std::unique_ptr<ITransformer> transformer);

    /**
     * @brief Set data loader
     * @param loader Data loader instance
     * 
     * @section details Implementation Details
     * - Sets the loader component
     * - Thread-safe operation
     */
    void set_loader(std::unique_ptr<ILoader> loader);

    /**
     * @brief Add pre-processing function
     * @param processor Pre-processing function
     * 
     * @section details Implementation Details
     * - Adds function to pre-processing pipeline
     * - Thread-safe operation
     */
    void add_pre_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Add post-processing function
     * @param processor Post-processing function
     * 
     * @section details Implementation Details
     * - Adds function to post-processing pipeline
     * - Thread-safe operation
     */
    void add_post_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Start pipeline execution
     * @param job_id Job identifier
     * @return Future containing job results
     * 
     * @section details Implementation Details
     * - Initializes job state
     * - Starts processing threads
     * - Returns future for results
     */
    std::future<JobInfo> start(const std::string& job_id);

    /**
     * @brief Stop pipeline gracefully
     * 
     * @section details Implementation Details
     * - Signals threads to stop
     * - Waits for completion
     * - Saves final state
     */
    void stop();

    /**
     * @brief Pause pipeline execution
     * 
     * @section details Implementation Details
     * - Suspends processing threads
     * - Preserves current state
     */
    void pause();

    /**
     * @brief Resume pipeline execution
     * 
     * @section details Implementation Details
     * - Resumes processing threads
     * - Restores previous state
     */
    void resume();

    /**
     * @brief Get current job status
     * @return Current job status
     * 
     * @section details Implementation Details
     * - Returns current status
     * - Thread-safe operation
     */
    [[nodiscard]] JobStatus get_status() const;

    /**
     * @brief Get current job information
     * @return Current job information
     * 
     * @section details Implementation Details
     * - Returns current job info
     * - Thread-safe operation
     */
    [[nodiscard]] JobInfo get_job_info() const;

    /**
     * @brief Set progress callback
     * @param callback Progress callback function
     * 
     * @section details Implementation Details
     * - Sets progress notification callback
     * - Thread-safe operation
     */
    void set_progress_callback(
        std::function<void(const JobInfo&)> callback);

    /**
     * @brief Set error callback
     * @param callback Error callback function
     * 
     * @section details Implementation Details
     * - Sets error notification callback
     * - Thread-safe operation
     */
    void set_error_callback(
        std::function<void(const std::string&, const std::exception&)> callback);

protected:
    /**
     * @brief Main pipeline execution method
     * 
     * @section details Implementation Details
     * - Coordinates ETL stages
     * - Manages processing flow
     * - Handles errors
     */
    void run_pipeline();

    /**
     * @brief Data extraction worker
     * 
     * @section details Implementation Details
     * - Runs in separate thread
     * - Extracts data from source
     * - Queues for transformation
     */
    void extraction_worker();

    /**
     * @brief Data transformation worker
     * 
     * @section details Implementation Details
     * - Runs in separate thread
     * - Transforms data records
     * - Queues for loading
     */
    void transformation_worker();

    /**
     * @brief Data loading worker
     * 
     * @section details Implementation Details
     * - Runs in separate thread
     * - Loads data to target
     * - Handles commits
     */
    void loading_worker();

    /**
     * @brief Handle pipeline error
     * @param stage Processing stage
     * @param error Error message
     * @param exception Optional exception
     * 
     * @section details Implementation Details
     * - Logs error details
     * - Updates job state
     * - Triggers callbacks
     */
    void handle_error(ProcessingContext::Stage stage,
                     const std::string& error,
                     const std::exception* exception = nullptr);

    /**
     * @brief Save checkpoint
     * 
     * @section details Implementation Details
     * - Saves current state
     * - Updates checkpoint file
     */
    void save_checkpoint();

    /**
     * @brief Load checkpoint
     * @return true if checkpoint loaded
     * 
     * @section details Implementation Details
     * - Loads previous state
     * - Restores processing
     */
    bool load_checkpoint();

private:
    // Configuration
    PipelineConfig config_; ///< Pipeline configuration

    // Components
    std::unique_ptr<IExtractor> extractor_;     ///< Data extractor
    std::unique_ptr<ITransformer> transformer_; ///< Data transformer
    std::unique_ptr<ILoader> loader_;           ///< Data loader

    // Pre/post processors
    std::vector<std::function<void(RecordBatch&, ProcessingContext&)>> pre_processors_;  ///< Pre-processing functions
    std::vector<std::function<void(RecordBatch&, ProcessingContext&)>> post_processors_; ///< Post-processing functions

    // Job information
    JobInfo job_info_;           ///< Current job information
    ProcessingContext context_;   ///< Processing context

    // Thread management
    std::vector<std::thread> workers_;
    std::atomic<JobStatus> status_{JobStatus::Created};
    std::atomic<bool> should_stop_{false};
    std::atomic<bool> is_paused_{false};

    // Queues
    std::queue<RecordBatch> extract_queue_;
    std::queue<RecordBatch> transform_queue_;
    std::mutex extract_mutex_;
    std::mutex transform_mutex_;
    std::condition_variable extract_cv_;
    std::condition_variable transform_cv_;
    std::condition_variable pause_cv_;

    // Callbacks
    std::function<void(const JobInfo&)> progress_callback_;
    std::function<void(const std::string&, const std::exception&)> error_callback_;

    // Statistics
    mutable std::mutex stats_mutex_;
    std::chrono::steady_clock::time_point last_checkpoint_;
};

/**
 * @brief Pipeline builder for fluent API
 *
 * Provides a builder pattern for constructing ETL pipelines.
 */
class PipelineBuilder {
public:
    /**
     * @brief Constructor
     */
    PipelineBuilder() : pipeline_(std::make_unique<ETLPipeline>()) {}

    /**
     * @brief Set configuration
     * @param config Pipeline configuration
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_config(const PipelineConfig& config);

    /**
     * @brief Set configuration from file
     * @param config_file Configuration file path
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_config_file(const std::string& config_file);

    /**
     * @brief Set extractor by type
     * @param type Extractor type
     * @param params Extractor parameters
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_extractor(const std::string& type,
                                   const std::unordered_map<std::string, std::any>& params);

    /**
     * @brief Set custom extractor
     * @param extractor Extractor instance
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_extractor(std::unique_ptr<IExtractor> extractor);

    /**
     * @brief Set transformer by type
     * @param type Transformer type
     * @param params Transformer parameters
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_transformer(const std::string& type,
                                     const std::unordered_map<std::string, std::any>& params);

    /**
     * @brief Set transformer for table
     * @param table_name OMOP table name
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_transformer_for_table(const std::string& table_name);

    /**
     * @brief Set custom transformer
     * @param transformer Transformer instance
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_transformer(std::unique_ptr<ITransformer> transformer);

    /**
     * @brief Set loader by type
     * @param type Loader type
     * @param params Loader parameters
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_loader(const std::string& type,
                                const std::unordered_map<std::string, std::any>& params);

    /**
     * @brief Set custom loader
     * @param loader Loader instance
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_loader(std::unique_ptr<ILoader> loader);

    /**
     * @brief Add pre-processor
     * @param processor Pre-processor function
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_pre_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Add post-processor
     * @param processor Post-processor function
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_post_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Set progress callback
     * @param callback Progress callback
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_progress_callback(
        std::function<void(const JobInfo&)> callback);

    /**
     * @brief Set error callback
     * @param callback Error callback
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_error_callback(
        std::function<void(const std::string&, const std::exception&)> callback);

    /**
     * @brief Build the pipeline
     * @return std::unique_ptr<ETLPipeline> Constructed pipeline
     */
    [[nodiscard]] std::unique_ptr<ETLPipeline> build();

private:
    std::unique_ptr<ETLPipeline> pipeline_;
};

/**
 * @brief Pipeline manager for managing multiple ETL jobs
 *
 * This class manages multiple ETL pipelines, providing job scheduling,
 * monitoring, and resource management.
 */
class PipelineManager {
public:
    /**
     * @brief Constructor
     * @param max_concurrent_jobs Maximum concurrent jobs
     */
    explicit PipelineManager(size_t max_concurrent_jobs = 4);

    /**
     * @brief Destructor
     */
    ~PipelineManager();

    /**
     * @brief Submit job
     * @param job_name Job name
     * @param pipeline Pipeline to execute
     * @return std::string Job ID
     */
    std::string submit_job(const std::string& job_name,
                          std::unique_ptr<ETLPipeline> pipeline);

    /**
     * @brief Get job status
     * @param job_id Job ID
     * @return std::optional<JobStatus> Job status if found
     */
    [[nodiscard]] std::optional<JobStatus> get_job_status(const std::string& job_id) const;

    /**
     * @brief Get job information
     * @param job_id Job ID
     * @return std::optional<JobInfo> Job information if found
     */
    [[nodiscard]] std::optional<JobInfo> get_job_info(const std::string& job_id) const;

    /**
     * @brief Get all jobs
     * @return std::vector<JobInfo> All job information
     */
    [[nodiscard]] std::vector<JobInfo> get_all_jobs() const;

    /**
     * @brief Cancel job
     * @param job_id Job ID
     * @return bool True if cancelled
     */
    bool cancel_job(const std::string& job_id);

    /**
     * @brief Pause job
     * @param job_id Job ID
     * @return bool True if paused
     */
    bool pause_job(const std::string& job_id);

    /**
     * @brief Resume job
     * @param job_id Job ID
     * @return bool True if resumed
     */
    bool resume_job(const std::string& job_id);

    /**
     * @brief Wait for job completion
     * @param job_id Job ID
     * @param timeout_ms Timeout in milliseconds (-1 for no timeout)
     * @return bool True if completed within timeout
     */
    bool wait_for_job(const std::string& job_id, int timeout_ms = -1);

    /**
     * @brief Shutdown manager
     * @param wait_for_jobs Whether to wait for running jobs
     */
    void shutdown(bool wait_for_jobs = true);

private:
    struct JobEntry {
        std::string job_id;
        std::unique_ptr<ETLPipeline> pipeline;
        std::future<JobInfo> future;
        JobInfo info;
    };

    size_t max_concurrent_jobs_;
    std::unordered_map<std::string, std::unique_ptr<JobEntry>> jobs_;
    std::queue<std::string> job_queue_;
    std::vector<std::thread> scheduler_threads_;

    mutable std::mutex jobs_mutex_;
    std::condition_variable job_cv_;
    std::atomic<bool> shutdown_{false};

    void scheduler_worker();
    std::string generate_job_id();
};

} // namespace omop::core

File src/lib/core/job_scheduler.h:

/**
 * @file job_scheduler.h
 * @brief Job scheduling system for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header file defines the job scheduling system that manages the execution
 * of ETL jobs based on various scheduling strategies. It provides a flexible
 * and extensible framework for scheduling, monitoring, and managing ETL jobs
 * with support for different scheduling algorithms and trigger types.
 * 
 * @section design Design Principles
 * - Flexibility: Multiple scheduling strategies
 * - Reliability: Robust job dependency management
 * - Scalability: Efficient job queue management
 * - Extensibility: Support for custom scheduling policies
 * - Monitoring: Comprehensive job tracking and statistics
 * 
 * @section components Components
 * - JobScheduler: Main scheduling orchestrator
 * - JobSchedule: Schedule definition and configuration
 * - QueuedJob: Job queue entry with metadata
 * - SchedulingStrategy: Different scheduling algorithms
 * - TriggerType: Various job trigger mechanisms
 * 
 * @section usage Usage
 * To use the job scheduler:
 * 1. Create a JobScheduler instance
 * 2. Define job schedules
 * 3. Start the scheduler
 * 4. Monitor job execution
 * 5. Handle job completion
 * 
 * @section example Example
 * @code
 * auto scheduler = std::make_shared<JobScheduler>(job_manager);
 * JobSchedule schedule;
 * schedule.job_config_id = "daily_extract";
 * schedule.trigger_type = TriggerType::SCHEDULED;
 * schedule.cron_expression = "0 0 * * *";
 * scheduler->addSchedule(schedule);
 * scheduler->start();
 * @endcode
 */

#pragma once

#include <string>
#include <memory>
#include <vector>
#include <queue>
#include <chrono>
#include <functional>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>

#include "interfaces.h"
#include "pipeline.h"
#include "job_manager.h"

namespace omop::core {

/**
 * @brief Job scheduling strategy
 * 
 * @section description Description
 * Defines different algorithms for determining the order of job execution
 * in the scheduler. Each strategy has different characteristics and is
 * suitable for different use cases.
 * 
 * @section strategies Strategies
 * - FIFO: Simple first-in-first-out ordering
 * - PRIORITY: Based on job priority levels
 * - ROUND_ROBIN: Equal time distribution
 * - FAIR_SHARE: Resource allocation fairness
 * - DEADLINE: Based on job deadlines
 */
enum class SchedulingStrategy {
    FIFO,           ///< First In First Out
    PRIORITY,       ///< Priority-based scheduling
    ROUND_ROBIN,    ///< Round-robin scheduling
    FAIR_SHARE,     ///< Fair share between users/groups
    DEADLINE        ///< Deadline-based scheduling
};

/**
 * @brief Scheduled job trigger type
 * 
 * @section description Description
 * Defines different mechanisms for triggering job execution.
 * Each trigger type has specific characteristics and use cases.
 * 
 * @section types Types
 * - MANUAL: User-initiated execution
 * - SCHEDULED: Time-based execution
 * - EVENT: Event-driven execution
 * - DEPENDENCY: Dependency-based execution
 * - FILE_WATCH: File system change detection
 */
enum class TriggerType {
    MANUAL,         ///< Manual trigger
    SCHEDULED,      ///< Time-based schedule
    EVENT,          ///< Event-based trigger
    DEPENDENCY,     ///< Dependency-based trigger
    FILE_WATCH      ///< File system watch trigger
};

/**
 * @brief Job schedule definition
 * 
 * @section description Description
 * Contains all configuration and metadata for a scheduled job,
 * including trigger conditions, timing information, and dependencies.
 * 
 * @section fields Fields
 * - schedule_id: Unique schedule identifier
 * - job_config_id: Associated job configuration
 * - trigger_type: How the job is triggered
 * - cron_expression: Time-based schedule
 * - next_run: Next scheduled execution
 * - last_run: Last execution time
 * - enabled: Schedule status
 * - dependencies: Required job completions
 * - parameters: Additional configuration
 */
struct JobSchedule {
    std::string schedule_id;                    ///< Schedule identifier
    std::string job_config_id;                  ///< Job configuration ID
    TriggerType trigger_type{TriggerType::MANUAL}; ///< Trigger type
    std::string cron_expression;                ///< Cron expression for scheduled jobs
    std::chrono::system_clock::time_point next_run; ///< Next scheduled run time
    std::chrono::system_clock::time_point last_run; ///< Last run time
    bool enabled{true};                         ///< Whether schedule is enabled
    std::vector<std::string> dependencies;      ///< Job dependencies
    std::unordered_map<std::string, std::string> parameters; ///< Schedule parameters
};

/**
 * @brief Job queue entry
 * 
 * @section description Description
 * Represents a job waiting to be executed in the scheduler's queue.
 * Contains all necessary information for job execution and tracking.
 * 
 * @section fields Fields
 * - job_id: Unique job identifier
 * - job_config: Job configuration
 * - priority: Execution priority
 * - enqueue_time: When job was queued
 * - deadline: Latest execution time
 * - dependencies: Required completions
 * - callback: Completion handler
 */
struct QueuedJob {
    std::string job_id;                         ///< Job identifier
    JobConfig job_config;                       ///< Job configuration
    JobPriority priority;                       ///< Job priority
    std::chrono::system_clock::time_point enqueue_time; ///< Enqueue time
    std::chrono::system_clock::time_point deadline; ///< Job deadline
    std::vector<std::string> dependencies;      ///< Job dependencies
    std::function<void()> callback;             ///< Completion callback
};

/**
 * @brief Job scheduler for managing ETL job execution
 * 
 * @section description Description
 * Provides advanced scheduling capabilities for ETL jobs, including
 * cron-based scheduling, dependency management, and various scheduling
 * strategies. Manages job queues, execution order, and monitoring.
 * 
 * @section features Features
 * - Multiple scheduling strategies
 * - Cron-based scheduling
 * - Job dependency management
 * - Priority-based execution
 * - Job completion callbacks
 * - Comprehensive monitoring
 * 
 * @section thread_safety Thread Safety
 * The scheduler is thread-safe and can be used from multiple threads.
 * All public methods are synchronized appropriately.
 */
class JobScheduler {
public:
    /**
     * @brief Constructor
     * @param job_manager Job manager instance
     * @param strategy Scheduling strategy
     * 
     * @section details Implementation Details
     * - Initializes scheduler with job manager
     * - Sets scheduling strategy
     * - Prepares internal data structures
     */
    JobScheduler(std::shared_ptr<JobManager> job_manager,
                 SchedulingStrategy strategy = SchedulingStrategy::PRIORITY);

    /**
     * @brief Destructor
     * 
     * @section details Implementation Details
     * - Stops scheduler if running
     * - Cleans up resources
     */
    ~JobScheduler();

    /**
     * @brief Start the scheduler
     * @return true if started successfully
     * 
     * @section details Implementation Details
     * - Initializes scheduler thread
     * - Starts processing jobs
     * - Returns false if already running
     */
    bool start();

    /**
     * @brief Stop the scheduler
     * 
     * @section details Implementation Details
     * - Signals scheduler thread to stop
     * - Waits for completion
     * - Cleans up resources
     */
    void stop();

    /**
     * @brief Add a job schedule
     * @param schedule Job schedule definition
     * @return Schedule ID
     * 
     * @section details Implementation Details
     * - Validates schedule configuration
     * - Generates unique schedule ID
     * - Adds to schedule collection
     */
    std::string addSchedule(const JobSchedule& schedule);

    /**
     * @brief Remove a job schedule
     * @param schedule_id Schedule identifier
     * @return true if removed successfully
     * 
     * @section details Implementation Details
     * - Removes schedule from collection
     * - Cancels pending executions
     * - Returns false if not found
     */
    bool removeSchedule(const std::string& schedule_id);

    /**
     * @brief Update a job schedule
     * @param schedule_id Schedule identifier
     * @param schedule Updated schedule
     * @return true if updated successfully
     * 
     * @section details Implementation Details
     * - Validates new configuration
     * - Updates schedule in collection
     * - Recalculates next run time
     */
    bool updateSchedule(const std::string& schedule_id, const JobSchedule& schedule);

    /**
     * @brief Enable/disable a schedule
     * @param schedule_id Schedule identifier
     * @param enabled Enable flag
     * @return true if updated successfully
     * 
     * @section details Implementation Details
     * - Updates schedule status
     * - Cancels pending if disabled
     * - Returns false if not found
     */
    bool setScheduleEnabled(const std::string& schedule_id, bool enabled);

    /**
     * @brief Get schedule by ID
     * @param schedule_id Schedule identifier
     * @return Schedule if found
     * 
     * @section details Implementation Details
     * - Returns copy of schedule
     * - Returns empty optional if not found
     */
    std::optional<JobSchedule> getSchedule(const std::string& schedule_id) const;

    /**
     * @brief Get all schedules
     * @return Vector of all schedules
     * 
     * @section details Implementation Details
     * - Returns copy of all schedules
     * - No exceptions thrown
     */
    std::vector<JobSchedule> getAllSchedules() const;

    /**
     * @brief Submit a job for immediate execution
     * @param job_config Job configuration
     * @param priority Job priority
     * @param dependencies Job dependencies
     * @return Job ID
     * 
     * @section details Implementation Details
     * - Creates job configuration
     * - Adds to execution queue
     * - Returns unique job ID
     */
    std::string submitJob(const JobConfig& job_config,
                         JobPriority priority = JobPriority::NORMAL,
                         const std::vector<std::string>& dependencies = {});

    /**
     * @brief Get queued jobs
     * @return Vector of queued jobs
     * 
     * @section details Implementation Details
     * - Returns copy of queue contents
     * - No exceptions thrown
     */
    std::vector<QueuedJob> getQueuedJobs() const;

    /**
     * @brief Get scheduler statistics
     * @return Statistics map
     * 
     * @section details Implementation Details
     * - Returns current statistics
     * - Includes queue size, execution times
     * - No exceptions thrown
     */
    std::unordered_map<std::string, std::any> getStatistics() const;

    /**
     * @brief Set scheduling strategy
     * @param strategy New scheduling strategy
     * 
     * @section details Implementation Details
     * - Updates strategy
     * - Reorders job queue
     * - No exceptions thrown
     */
    void setSchedulingStrategy(SchedulingStrategy strategy);

    /**
     * @brief Register job completion callback
     * @param callback Callback function
     * 
     * @section details Implementation Details
     * - Sets completion handler
     * - Called for all jobs
     * - No exceptions thrown
     */
    void registerJobCompletionCallback(
        std::function<void(const std::string&, JobStatus)> callback);

    /**
     * @brief Trigger a scheduled job immediately
     * @param schedule_id Schedule identifier
     * @return Job ID if triggered
     * 
     * @section details Implementation Details
     * - Validates schedule exists
     * - Creates job configuration
     * - Submits for execution
     */
    std::optional<std::string> triggerSchedule(const std::string& schedule_id);

private:
    /**
     * @brief Scheduler main loop
     * 
     * @section details Implementation Details
     * - Processes scheduled jobs
     * - Manages job queue
     * - Handles job completion
     */
    void schedulerLoop();

    /**
     * @brief Process scheduled jobs
     * 
     * @section details Implementation Details
     * - Checks schedule times
     * - Triggers due jobs
     * - Updates next run times
     */
    void processScheduledJobs();

    /**
     * @brief Process job queue
     * 
     * @section details Implementation Details
     * - Gets next job to execute
     * - Checks dependencies
     * - Submits to job manager
     */
    void processJobQueue();

    /**
     * @brief Check job dependencies
     * @param job Queued job
     * @return true if all dependencies satisfied
     * 
     * @section details Implementation Details
     * - Verifies all dependencies
     * - Checks completion status
     * - Returns false if any missing
     */
    bool checkDependencies(const QueuedJob& job) const;

    /**
     * @brief Calculate next run time from cron expression
     * @param cron_expr Cron expression
     * @param from_time Starting time
     * @return Next run time
     * 
     * @section details Implementation Details
     * - Parses cron expression
     * - Calculates next occurrence
     * - Returns system time point
     */
    std::chrono::system_clock::time_point calculateNextRunTime(
        const std::string& cron_expr,
        std::chrono::system_clock::time_point from_time) const;

    /**
     * @brief Get next job based on scheduling strategy
     * @return Next job to execute
     * 
     * @section details Implementation Details
     * - Applies scheduling strategy
     * - Checks dependencies
     * - Returns next job if ready
     */
    std::optional<QueuedJob> getNextJob();

    /**
     * @brief Compare jobs for priority queue
     * 
     * @section description Description
     * Functor for comparing jobs based on the current
     * scheduling strategy.
     * 
     * @section details Implementation Details
     * - Implements strategy-specific comparison
     * - Used by priority queue
     */
    struct JobComparator {
        SchedulingStrategy strategy;

        bool operator()(const QueuedJob& a, const QueuedJob& b) const;
    };

private:
    std::shared_ptr<JobManager> job_manager_;    ///< Job manager
    SchedulingStrategy strategy_;                ///< Scheduling strategy
    std::unordered_map<std::string, JobSchedule> schedules_; ///< Schedule collection
    std::priority_queue<QueuedJob, std::vector<QueuedJob>, JobComparator> job_queue_; ///< Job queue
    std::thread scheduler_thread_;               ///< Scheduler thread
    std::atomic<bool> running_{false};          ///< Running flag
    mutable std::mutex mutex_;                  ///< Synchronization mutex
    mutable std::mutex schedules_mutex_;        ///< Schedules mutex
    mutable std::mutex queue_mutex_;            ///< Queue mutex
    mutable std::mutex callbacks_mutex_;        ///< Callbacks mutex
    mutable std::mutex completed_mutex_;        ///< Completed jobs mutex
    std::condition_variable cv_;                ///< Thread synchronization
    std::condition_variable queue_cv_;          ///< Queue condition variable
    std::function<void(const std::string&, JobStatus)> completion_callback_; ///< Completion handler
    std::vector<std::function<void(const std::string&, JobStatus)>> completion_callbacks_; ///< Completion callbacks
    std::unordered_map<std::string, JobStatus> completed_jobs_; ///< Completed jobs tracking
    std::atomic<size_t> jobs_scheduled_{0};     ///< Jobs scheduled counter
    std::atomic<size_t> jobs_executed_{0};      ///< Jobs executed counter
    std::atomic<size_t> jobs_failed_{0};        ///< Jobs failed counter
};

} // namespace omop::core

File src/lib/load/batch_loader.h:

/**
 * @file batch_loader.h
 * @brief Batch loading functionality for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 * @version 1.0
 * 
 * @section overview Overview
 * This header provides batch loading functionality for the OMOP ETL pipeline,
 * including memory management, compression, and parallel processing capabilities.
 * 
 * @section design Design Principles
 * - Performance: Batch processing and parallel execution
 * - Memory Efficiency: Controlled memory usage and compression
 * - Reliability: Batch-level transaction management
 * - Flexibility: Configurable batch operations
 * - Thread Safety: Concurrent batch processing
 * 
 * @section components Components
 * - BatchLoaderOptions: Loading configuration
 * - BatchStatistics: Batch metrics
 * - EnhancedBatch: Batch container with metadata
 * - BatchLoader: Core batch loading functionality
 * 
 * @section usage Usage
 * To use the batch loader:
 * 1. Configure batch options
 * 2. Initialize with desired settings
 * 3. Load records or batches
 * 4. Process batches in parallel
 * 
 * @section example Example
 * @code
 * auto loader = BatchLoader::create("file", options);
 * loader->initialize(config);
 * loader->load_batch(batch, context);
 * loader->commit(context);
 * @endcode
 */

#pragma once

#include "load/loader_base.h"
#include "core/record.h"
#include <queue>
#include <condition_variable>
#include <functional>

namespace omop::load {

/**
 * @brief Batch loading configuration options
 * 
 * @section description Description
 * Configures batch loading behavior, including batch sizes,
 * memory limits, compression settings, and parallel processing.
 * 
 * @section fields Fields
 * - batch_size: Records per batch
 * - max_batches_in_memory: Memory limit
 * - flush_interval_ms: Auto-flush timing
 * - enable_compression: Compression flag
 * - parallel_processing: Parallel flag
 * - worker_threads: Thread count
 * - compression_type: Algorithm
 * - deduplicate: Deduplication flag
 * - sort_batch: Sorting flag
 * - sort_key: Sort field
 */
struct BatchLoaderOptions {
    size_t batch_size{1000};              ///< Number of records per batch
    size_t max_batches_in_memory{10};     ///< Maximum batches to keep in memory
    size_t flush_interval_ms{5000};       ///< Auto-flush interval in milliseconds
    bool enable_compression{false};        ///< Enable batch compression
    bool parallel_processing{true};        ///< Enable parallel batch processing
    size_t worker_threads{4};             ///< Number of worker threads
    std::string compression_type{"gzip"}; ///< Compression algorithm
    bool deduplicate{false};              ///< Remove duplicate records
    bool sort_batch{false};               ///< Sort records within batch
    std::string sort_key;                 ///< Field to sort by
};

/**
 * @brief Batch processing statistics
 * 
 * @section description Description
 * Tracks metrics and timing information for batch processing,
 * including record counts, sizes, and processing times.
 * 
 * @section fields Fields
 * - records_in_batch: Record count
 * - batch_size_bytes: Uncompressed size
 * - compressed_size_bytes: Compressed size
 * - creation_time: Batch creation
 * - processing_start_time: Processing start
 * - processing_end_time: Processing end
 * - processed: Processing flag
 * - success: Success flag
 * - error_message: Error details
 */
struct BatchStatistics {
    size_t records_in_batch{0};  ///< Number of records in batch
    size_t batch_size_bytes{0};  ///< Uncompressed size in bytes
    size_t compressed_size_bytes{0};  ///< Compressed size in bytes
    std::chrono::steady_clock::time_point creation_time;  ///< Batch creation time
    std::chrono::steady_clock::time_point processing_start_time;  ///< Processing start time
    std::chrono::steady_clock::time_point processing_end_time;  ///< Processing end time
    bool processed{false};  ///< Processing completion flag
    bool success{false};  ///< Processing success flag
    std::string error_message;  ///< Error message if failed
};

/**
 * @brief Enhanced batch container
 * 
 * @section description Description
 * Extends basic batch functionality with metadata tracking,
 * compression, sorting, and deduplication capabilities.
 * 
 * @section features Features
 * - Record management
 * - Statistics tracking
 * - Compression support
 * - Sorting capabilities
 * - Deduplication
 * - Memory estimation
 */
class EnhancedBatch {
public:
    /**
     * @brief Constructor
     * @param batch_id Unique batch identifier
     * @param capacity Initial capacity
     * 
     * @section details Implementation Details
     * - Sets batch ID
     * - Sets capacity
     * - Initializes statistics
     * - Thread-safe
     */
    EnhancedBatch(size_t batch_id, size_t capacity);

    /**
     * @brief Add record to batch
     * @param record Record to add
     * @return bool True if batch is full after adding
     * 
     * @section details Implementation Details
     * - Adds record
     * - Updates statistics
     * - Checks capacity
     * - Thread-safe
     */
    bool add_record(const core::Record& record);

    /**
     * @brief Get batch records
     * @return const core::RecordBatch& Records in batch
     * 
     * @section details Implementation Details
     * - Returns records
     * - Thread-safe
     */
    const core::RecordBatch& get_records() const { return records_; }

    /**
     * @brief Get mutable batch records
     * @return core::RecordBatch& Mutable records
     * 
     * @section details Implementation Details
     * - Returns mutable records
     * - Thread-safe
     */
    core::RecordBatch& get_mutable_records() { return records_; }

    /**
     * @brief Get batch ID
     * @return size_t Batch identifier
     * 
     * @section details Implementation Details
     * - Returns ID
     * - Thread-safe
     */
    size_t get_batch_id() const { return batch_id_; }

    /**
     * @brief Get batch statistics
     * @return const BatchStatistics& Statistics
     * 
     * @section details Implementation Details
     * - Returns statistics
     * - Thread-safe
     */
    const BatchStatistics& get_statistics() const { return statistics_; }

    /**
     * @brief Update batch statistics
     * @return BatchStatistics& Mutable statistics
     * 
     * @section details Implementation Details
     * - Returns mutable statistics
     * - Thread-safe
     */
    BatchStatistics& get_mutable_statistics() { return statistics_; }

    /**
     * @brief Check if batch is full
     * @return bool True if full
     * 
     * @section details Implementation Details
     * - Checks capacity
     * - Thread-safe
     */
    bool is_full() const { return records_.size() >= capacity_; }

    /**
     * @brief Get batch size
     * @return size_t Number of records
     * 
     * @section details Implementation Details
     * - Returns size
     * - Thread-safe
     */
    size_t size() const { return records_.size(); }

    /**
     * @brief Clear batch
     * 
     * @section details Implementation Details
     * - Clears records
     * - Resets statistics
     * - Thread-safe
     */
    void clear();

    /**
     * @brief Sort batch records
     * @param key_extractor Function to extract sort key
     * 
     * @section details Implementation Details
     * - Sorts records
     * - Updates statistics
     * - Thread-safe
     */
    void sort(std::function<std::any(const core::Record&)> key_extractor);

    /**
     * @brief Remove duplicate records
     * @param key_extractor Function to extract deduplication key
     * @return size_t Number of duplicates removed
     * 
     * @section details Implementation Details
     * - Removes duplicates
     * - Updates statistics
     * - Thread-safe
     */
    size_t deduplicate(std::function<std::string(const core::Record&)> key_extractor);

    /**
     * @brief Compress batch data
     * @param compression_type Compression algorithm
     * @return std::vector<uint8_t> Compressed data
     * 
     * @section details Implementation Details
     * - Compresses data
     * - Updates statistics
     * - Thread-safe
     */
    std::vector<uint8_t> compress(const std::string& compression_type);

    /**
     * @brief Estimate memory usage
     * @return size_t Estimated bytes
     * 
     * @section details Implementation Details
     * - Estimates usage
     * - Thread-safe
     */
    size_t estimate_memory_usage() const;

private:
    size_t batch_id_;  ///< Unique batch identifier
    size_t capacity_;  ///< Maximum records
    core::RecordBatch records_;  ///< Batch records
    BatchStatistics statistics_;  ///< Batch statistics
};

/**
 * @brief Batch loader base class
 * 
 * @section description Description
 * Provides efficient batch-based loading with memory management,
 * compression, and parallel processing capabilities.
 * 
 * @section features Features
 * - Batch management
 * - Memory control
 * - Parallel processing
 * - Compression support
 * - Statistics tracking
 * - Thread safety
 */
class BatchLoader : public LoaderBase {
public:
    /**
     * @brief Constructor
     * @param name Loader name
     * @param options Batch loader options
     * 
     * @section details Implementation Details
     * - Sets name
     * - Sets options
     * - Thread-safe
     */
    BatchLoader(const std::string& name, BatchLoaderOptions options = {});

    /**
     * @brief Destructor
     * 
     * @section details Implementation Details
     * - Stops workers
     * - Flushes batches
     * - Thread-safe
     */
    ~BatchLoader() override;

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     * 
     * @section details Implementation Details
     * - Adds to batch
     * - Handles errors
     * - Thread-safe
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     * 
     * @section details Implementation Details
     * - Processes batch
     * - Handles errors
     * - Thread-safe
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Flushes batches
     * - Commits changes
     * - Thread-safe
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Clears batches
     * - Rolls back changes
     * - Thread-safe
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "batch"; }

protected:
    /**
     * @brief Process a complete batch
     * @param batch Batch to process
     * @param context Processing context
     * @return size_t Number of successfully processed records
     * 
     * @section details Implementation Details
     * - Processes batch
     * - Updates statistics
     * - Thread-safe
     */
    virtual size_t process_batch(std::unique_ptr<EnhancedBatch> batch,
                                core::ProcessingContext& context) = 0;

    /**
     * @brief Perform batch loader initialization
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Initializes loader
     * - Starts workers
     * - Thread-safe
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Perform batch loader finalization
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Stops workers
     * - Flushes batches
     * - Thread-safe
     */
    void do_finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get additional statistics
     * @return std::unordered_map<std::string, std::any> Batch-specific statistics
     * 
     * @section details Implementation Details
     * - Returns statistics
     * - Thread-safe
     */
    std::unordered_map<std::string, std::any> get_additional_statistics() const override;

    /**
     * @brief Get batch loader options
     * @return const BatchLoaderOptions& Options
     * 
     * @section details Implementation Details
     * - Returns options
     * - Thread-safe
     */
    const BatchLoaderOptions& get_options() const { return options_; }

private:
    /**
     * @brief Worker thread function
     * @param worker_id Worker identifier
     * 
     * @section details Implementation Details
     * - Processes batches
     * - Thread-safe
     */
    void worker_thread(size_t worker_id);

    /**
     * @brief Flush thread function
     * 
     * @section details Implementation Details
     * - Flushes batches
     * - Thread-safe
     */
    void flush_thread();

    /**
     * @brief Flush all pending batches
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Flushes batches
     * - Thread-safe
     */
    void flush_all_batches(core::ProcessingContext& context);

    /**
     * @brief Submit batch for processing
     * @param batch Batch to submit
     * 
     * @section details Implementation Details
     * - Submits batch
     * - Thread-safe
     */
    void submit_batch(std::unique_ptr<EnhancedBatch> batch);

    BatchLoaderOptions options_;  ///< Loader options
    std::queue<std::unique_ptr<EnhancedBatch>> batch_queue_;  ///< Batch queue
    std::condition_variable queue_cv_;  ///< Queue condition
    std::mutex queue_mutex_;  ///< Queue mutex
    std::vector<std::thread> workers_;  ///< Worker threads
    std::thread flush_thread_;  ///< Flush thread
    std::atomic<bool> stop_workers_{false};  ///< Stop flag
    std::atomic<size_t> total_processed_{0};  ///< Total processed records
    std::atomic<size_t> total_failed_{0};  ///< Total failed records
};

/**
 * @brief Create batch loader
 * @param type Loader type
 * @param options Loader options
 * @return std::unique_ptr<BatchLoader> Loader instance
 * 
 * @section details Implementation Details
 * - Creates loader
 * - Thread-safe
 */
std::unique_ptr<BatchLoader> create_batch_loader(
    const std::string& type,
    const BatchLoaderOptions& options = {});

/**
 * @brief Register batch loaders
 * 
 * @section details Implementation Details
 * - Registers loaders
 * - Thread-safe
 */
void register_batch_loaders();

/**
 * @brief CSV batch loader
 *
 * Specialized batch loader for CSV file output.
 */
class CsvBatchLoader : public BatchLoader {
public:
    /**
     * @brief Constructor
     * @param options Batch loader options
     * @param delimiter CSV delimiter
     * @param quote_char Quote character
     */
    CsvBatchLoader(BatchLoaderOptions options = {},
                   char delimiter = ',',
                   char quote_char = '"');

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "csv_batch"; }

protected:
    /**
     * @brief Process a batch by writing to CSV
     * @param batch Batch to process
     * @param context Processing context
     * @return size_t Number of successfully processed records
     */
    size_t process_batch(std::unique_ptr<EnhancedBatch> batch,
                        core::ProcessingContext& context) override;

    /**
     * @brief Initialize CSV batch loader
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

private:
    /**
     * @brief Format record as CSV line
     * @param record Record to format
     * @return std::string CSV line
     */
    std::string format_csv_line(const core::Record& record);

    /**
     * @brief Escape CSV value
     * @param value Value to escape
     * @return std::string Escaped value
     */
    std::string escape_csv_value(const std::string& value);

    char delimiter_;
    char quote_char_;
    std::string output_file_;
    std::ofstream output_stream_;
    std::mutex output_mutex_;
    bool header_written_{false};
    std::vector<std::string> column_order_;
};

/**
 * @brief Memory-mapped file batch loader
 *
 * High-performance batch loader using memory-mapped files.
 */
class MmapBatchLoader : public BatchLoader {
public:
    /**
     * @brief Constructor
     * @param options Batch loader options
     * @param file_size_hint Expected file size hint
     */
    MmapBatchLoader(BatchLoaderOptions options = {},
                    size_t file_size_hint = 0);

    /**
     * @brief Destructor
     */
    ~MmapBatchLoader() override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "mmap_batch"; }

protected:
    /**
     * @brief Process batch using memory-mapped file
     * @param batch Batch to process
     * @param context Processing context
     * @return size_t Number of successfully processed records
     */
    size_t process_batch(std::unique_ptr<EnhancedBatch> batch,
                        core::ProcessingContext& context) override;

    /**
     * @brief Initialize memory-mapped file
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Finalize and unmap file
     * @param context Processing context
     */
    void do_finalize(core::ProcessingContext& context) override;

private:
    /**
     * @brief Map file into memory
     * @param file_path File path
     * @param size Initial size
     */
    void map_file(const std::string& file_path, size_t size);

    /**
     * @brief Unmap file from memory
     */
    void unmap_file();

    /**
     * @brief Extend mapped file size
     * @param new_size New file size
     */
    void extend_file(size_t new_size);

    size_t file_size_hint_;
    std::string mapped_file_path_;
    void* mapped_memory_{nullptr};
    size_t mapped_size_{0};
    size_t current_offset_{0};
    std::mutex mmap_mutex_;
    
#ifdef _WIN32
    void* file_handle_{nullptr};
    void* mapping_handle_{nullptr};
#else
    int file_descriptor_{-1};
#endif
};

} // namespace omop::load

File src/lib/load/database_loader.h:

/**
 * @file database_loader.h
 * @brief Database loading functionality for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 * @version 1.0
 * 
 * @section overview Overview
 * This header provides database loading functionality for the OMOP ETL pipeline,
 * including bulk loading, transaction management, and database-specific optimizations.
 * 
 * @section design Design Principles
 * - Performance: Bulk loading and buffering
 * - Reliability: Transaction management
 * - Flexibility: Database-specific optimizations
 * - Thread Safety: Concurrent loading support
 * - Error Handling: Comprehensive error tracking
 * 
 * @section components Components
 * - DatabaseLoaderOptions: Loading configuration
 * - BulkInsertBuffer: Record buffering
 * - DatabaseLoader: Core loading functionality
 * - ParallelDatabaseLoader: Concurrent loading
 * 
 * @section usage Usage
 * To use the database loader:
 * 1. Configure loader options
 * 2. Initialize with database connection
 * 3. Load records or batches
 * 4. Commit or rollback changes
 * 
 * @section example Example
 * @code
 * auto loader = DatabaseLoader::create("postgresql", connection);
 * loader->initialize(config);
 * loader->load_batch(batch, context);
 * loader->commit(context);
 * @endcode
 */

#pragma once

#include "core/interfaces.h"
#include "extract/database_connector.h"
#include "cdm/omop_tables.h"
#include <queue>
#include <atomic>
#include <thread>
#include <condition_variable>
#include <shared_mutex>
#include <unordered_set>
#include <functional>

namespace omop::load {

/**
 * @brief Database loader configuration options
 * 
 * @section description Description
 * Configures database loading behavior, including batch sizes,
 * transaction settings, and database-specific optimizations.
 * 
 * @section fields Fields
 * - batch_size: Records per batch
 * - commit_interval: Records per commit
 * - use_bulk_insert: Bulk insert flag
 * - truncate_before_load: Truncate flag
 * - disable_constraints: Constraint flag
 * - create_indexes_after_load: Index flag
 * - lock_timeout: Lock timeout
 * - temp_table_prefix: Temp table prefix
 * - use_copy_command: Copy command flag
 * - null_string: NULL representation
 * - delimiter: Field delimiter
 */
struct DatabaseLoaderOptions {
    size_t batch_size{1000};  ///< Records per batch
    size_t commit_interval{5000};  ///< Records per commit
    bool use_bulk_insert{true};  ///< Bulk insert flag
    bool truncate_before_load{false};  ///< Truncate flag
    bool disable_constraints{false};  ///< Constraint flag
    bool create_indexes_after_load{false};  ///< Index flag
    std::chrono::seconds lock_timeout{30};  ///< Lock timeout
    std::string temp_table_prefix{"tmp_"};  ///< Temp table prefix
    bool use_copy_command{true};  ///< Copy command flag
    std::string null_string{"\\N"};  ///< NULL representation
    char delimiter{'\t'};  ///< Field delimiter
};

/**
 * @brief Bulk insert buffer
 * 
 * @section description Description
 * Manages buffering of records for efficient bulk insertion,
 * providing capacity management and record access.
 * 
 * @section features Features
 * - Record buffering
 * - Capacity management
 * - Table association
 * - Thread safety
 */
class BulkInsertBuffer {
public:
    /**
     * @brief Constructor
     * @param table_name Target table name
     * @param capacity Buffer capacity
     * 
     * @section details Implementation Details
     * - Sets table name
     * - Sets capacity
     * - Reserves space
     * - Thread-safe
     */
    BulkInsertBuffer(const std::string& table_name, size_t capacity)
        : table_name_(table_name), capacity_(capacity) {
        records_.reserve(capacity);
    }

    /**
     * @brief Add record to buffer
     * @param record Record to add
     * @return bool True if buffer is full
     * 
     * @section details Implementation Details
     * - Adds record
     * - Checks capacity
     * - Thread-safe
     */
    bool add(const core::Record& record) {
        records_.push_back(record);
        return records_.size() >= capacity_;
    }

    /**
     * @brief Get buffered records
     * @return const std::vector<core::Record>& Records
     * 
     * @section details Implementation Details
     * - Returns records
     * - Thread-safe
     */
    [[nodiscard]] const std::vector<core::Record>& records() const {
        return records_;
    }

    /**
     * @brief Get buffer size
     * @return size_t Number of records
     * 
     * @section details Implementation Details
     * - Returns size
     * - Thread-safe
     */
    [[nodiscard]] size_t size() const { return records_.size(); }

    /**
     * @brief Check if buffer is empty
     * @return bool True if empty
     * 
     * @section details Implementation Details
     * - Checks emptiness
     * - Thread-safe
     */
    [[nodiscard]] bool empty() const { return records_.empty(); }

    /**
     * @brief Clear buffer
     * 
     * @section details Implementation Details
     * - Clears records
     * - Thread-safe
     */
    void clear() { records_.clear(); }

    /**
     * @brief Get table name
     * @return const std::string& Table name
     * 
     * @section details Implementation Details
     * - Returns name
     * - Thread-safe
     */
    [[nodiscard]] const std::string& table_name() const { return table_name_; }

private:
    std::string table_name_;  ///< Target table name
    size_t capacity_;  ///< Buffer capacity
    std::vector<core::Record> records_;  ///< Buffered records
};

/**
 * @brief Database loader base class
 * 
 * @section description Description
 * Provides common functionality for loading data into databases,
 * including bulk loading, transaction management, and optimizations.
 * 
 * @section features Features
 * - Bulk loading
 * - Transaction management
 * - Buffer management
 * - Constraint handling
 * - Index management
 * - Thread safety
 */
class DatabaseLoader : public core::ILoader {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     * @param options Loader options
     * 
     * @section details Implementation Details
     * - Stores connection
     * - Sets options
     * - Thread-safe
     */
    DatabaseLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                   DatabaseLoaderOptions options = {});

    /**
     * @brief Destructor
     * 
     * @section details Implementation Details
     * - Flushes buffers
     * - Thread-safe
     */
    ~DatabaseLoader() override;

    /**
     * @brief Initialize the loader
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Prepares tables
     * - Sets up buffers
     * - Thread-safe
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     * 
     * @section details Implementation Details
     * - Buffers record
     * - Handles errors
     * - Thread-safe
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     * 
     * @section details Implementation Details
     * - Loads records
     * - Handles errors
     * - Thread-safe
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Flushes buffers
     * - Commits transaction
     * - Thread-safe
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Clears buffers
     * - Rolls back transaction
     * - Thread-safe
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "database"; }

    /**
     * @brief Finalize loading
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Flushes buffers
     * - Creates indexes
     * - Thread-safe
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get loading statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     * 
     * @section details Implementation Details
     * - Returns stats
     * - Thread-safe
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Prepare table for loading
     * @param table_name Table name
     * 
     * @section details Implementation Details
     * - Truncates table
     * - Disables constraints
     * - Thread-safe
     */
    virtual void prepare_table(const std::string& table_name);

    /**
     * @brief Execute bulk insert
     * @param buffer Bulk insert buffer
     * @return size_t Number of inserted records
     * 
     * @section details Implementation Details
     * - Inserts records
     * - Handles errors
     * - Thread-safe
     */
    virtual size_t execute_bulk_insert(const BulkInsertBuffer& buffer);

    /**
     * @brief Execute single insert
     * @param table_name Table name
     * @param record Record to insert
     * @return bool True if successful
     * 
     * @section details Implementation Details
     * - Inserts record
     * - Handles errors
     * - Thread-safe
     */
    virtual bool execute_single_insert(const std::string& table_name,
                                      const core::Record& record);

    /**
     * @brief Build insert statement
     * @param table_name Table name
     * @param record Sample record
     * @return std::string INSERT statement
     * 
     * @section details Implementation Details
     * - Builds statement
     * - Thread-safe
     */
    virtual std::string build_insert_statement(const std::string& table_name,
                                             const core::Record& record);

    /**
     * @brief Flush all buffers
     * 
     * @section details Implementation Details
     * - Flushes buffers
     * - Thread-safe
     */
    void flush_all_buffers();

    /**
     * @brief Get or create buffer for table
     * @param table_name Table name
     * @return BulkInsertBuffer& Buffer reference
     * 
     * @section details Implementation Details
     * - Gets buffer
     * - Creates if needed
     * - Thread-safe
     */
    BulkInsertBuffer& get_buffer(const std::string& table_name);

    /**
     * @brief Index definition
     * 
     * @section description Description
     * Defines a database index with name and columns.
     * 
     * @section fields Fields
     * - name: Index name
     * - columns: Index columns
     */
    struct IndexDefinition {
        std::string name;  ///< Index name
        std::string columns;  ///< Index columns
    };

    /**
     * @brief Get OMOP standard indexes for table
     * @param table_name Table name
     * @return std::vector<IndexDefinition> Index definitions
     * 
     * @section details Implementation Details
     * - Returns indexes
     * - Thread-safe
     */
    std::vector<IndexDefinition> get_omop_indexes(const std::string& table_name);

    /**
     * @brief Disable constraints for table
     * @param table_name Table name
     * 
     * @section details Implementation Details
     * - Disables constraints
     * - Thread-safe
     */
    void disable_constraints(const std::string& table_name);

    /**
     * @brief Enable constraints for table
     * @param table_name Table name
     * 
     * @section details Implementation Details
     * - Enables constraints
     * - Thread-safe
     */
    void enable_constraints(const std::string& table_name);

    /**
     * @brief Create deferred indexes for table
     * @param table_name Table name
     * 
     * @section details Implementation Details
     * - Creates indexes
     * - Thread-safe
     */
    void create_deferred_indexes(const std::string& table_name);

protected:
    std::unique_ptr<extract::IDatabaseConnection> connection_;  ///< Database connection
    DatabaseLoaderOptions options_;  ///< Loader options
    std::unordered_map<std::string, BulkInsertBuffer> buffers_;  ///< Table buffers
    mutable std::shared_mutex buffer_mutex_;  ///< Buffer mutex
    std::atomic<size_t> total_loaded_{0};  ///< Total loaded records
    std::atomic<size_t> total_failed_{0};  ///< Total failed records
    std::atomic<size_t> total_committed_{0};  ///< Total committed records
    std::atomic<size_t> total_rolled_back_{0};  ///< Total rolled back records
};

/**
 * @brief PostgreSQL-specific loader
 *
 * Optimized loader for PostgreSQL with COPY command support.
 */
class PostgreSQLLoader : public DatabaseLoader {
public:
    /**
     * @brief Constructor
     * @param connection PostgreSQL connection
     * @param options Loader options
     */
    PostgreSQLLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                     DatabaseLoaderOptions options = {})
        : DatabaseLoader(std::move(connection), options) {}

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "postgresql"; }

protected:
    /**
     * @brief Execute bulk insert using COPY command
     * @param buffer Bulk insert buffer
     * @return size_t Number of inserted records
     */
    size_t execute_bulk_insert(const BulkInsertBuffer& buffer) override;

    /**
     * @brief Prepare COPY data
     * @param records Records to format
     * @return std::string COPY-formatted data
     */
    std::string prepare_copy_data(const std::vector<core::Record>& records);
};

/**
 * @brief MySQL-specific loader
 *
 * Optimized loader for MySQL with LOAD DATA support.
 */
class MySQLLoader : public DatabaseLoader {
public:
    /**
     * @brief Constructor
     * @param connection MySQL connection
     * @param options Loader options
     */
    MySQLLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                DatabaseLoaderOptions options = {})
        : DatabaseLoader(std::move(connection), options) {}

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "mysql"; }

protected:
    /**
     * @brief Execute bulk insert using LOAD DATA
     * @param buffer Bulk insert buffer
     * @return size_t Number of inserted records
     */
    size_t execute_bulk_insert(const BulkInsertBuffer& buffer) override;
};

/**
 * @brief OMOP-specific database loader
 *
 * Specialized loader that understands OMOP CDM structure and handles
 * table-specific loading requirements.
 */
class OmopDatabaseLoader : public DatabaseLoader {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     * @param options Loader options
     */
    OmopDatabaseLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                       DatabaseLoaderOptions options = {});

    /**
     * @brief Initialize with OMOP table
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "omop_database"; }

protected:
    /**
     * @brief Convert record to OMOP table object
     * @param record Generic record
     * @param table_name OMOP table name
     * @return std::unique_ptr<cdm::OmopTable> OMOP table object
     */
    std::unique_ptr<cdm::OmopTable> convert_to_omop_table(
        const core::Record& record,
        const std::string& table_name);

    /**
     * @brief Validate OMOP constraints
     * @param table OMOP table object
     * @return bool True if valid
     */
    bool validate_omop_constraints(const cdm::OmopTable& table);

    /**
     * @brief Handle foreign key constraints
     * @param table_name Table name
     * @param enable Whether to enable constraints
     */
    void handle_foreign_key_constraints(const std::string& table_name, bool enable);

    /**
     * @brief Create indexes for table
     * @param table_name Table name
     */
    void create_table_indexes(const std::string& table_name);

private:
    std::string current_omop_table_;
    bool validate_foreign_keys_{true};
    bool create_missing_tables_{false};

    // Cache for foreign key validation
    std::unordered_set<int64_t> person_id_cache_;
    std::unordered_set<int64_t> visit_id_cache_;
    std::unordered_set<int32_t> concept_id_cache_;
    mutable std::shared_mutex cache_mutex_;
};

/**
 * @brief Parallel database loader
 * 
 * @section description Description
 * Extends database loader with parallel loading capabilities,
 * using multiple worker threads for improved performance.
 * 
 * @section features Features
 * - Parallel loading
 * - Worker management
 * - Load balancing
 * - Thread safety
 */
class ParallelDatabaseLoader : public DatabaseLoader {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     * @param options Loader options
     * @param num_workers Number of worker threads
     * 
     * @section details Implementation Details
     * - Sets workers
     * - Thread-safe
     */
    ParallelDatabaseLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                          DatabaseLoaderOptions options = {},
                          size_t num_workers = 4);

    /**
     * @brief Destructor
     * 
     * @section details Implementation Details
     * - Stops workers
     * - Thread-safe
     */
    ~ParallelDatabaseLoader() override;

    /**
     * @brief Initialize the loader
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Starts workers
     * - Thread-safe
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     * 
     * @section details Implementation Details
     * - Queues record
     * - Thread-safe
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     * 
     * @section details Implementation Details
     * - Queues batch
     * - Thread-safe
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Waits for workers
     * - Commits changes
     * - Thread-safe
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Stops workers
     * - Rolls back changes
     * - Thread-safe
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Finalize loading
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Stops workers
     * - Finalizes loading
     * - Thread-safe
     */
    void finalize(core::ProcessingContext& context) override;

protected:
    /**
     * @brief Worker thread function
     * @param worker_id Worker identifier
     * 
     * @section details Implementation Details
     * - Processes records
     * - Thread-safe
     */
    void worker_thread(size_t worker_id);

private:
    size_t num_workers_;  ///< Number of workers
    std::vector<std::thread> workers_;  ///< Worker threads
    std::queue<core::Record> record_queue_;  ///< Record queue
    std::condition_variable queue_cv_;  ///< Queue condition
    std::mutex queue_mutex_;  ///< Queue mutex
    std::atomic<bool> stop_workers_{false};  ///< Stop flag
};

/**
 * @brief Create database loader
 * @param type Database type
 * @param connection Database connection
 * @param options Loader options
 * @return std::unique_ptr<DatabaseLoader> Loader instance
 * 
 * @section details Implementation Details
 * - Creates loader
 * - Thread-safe
 */
std::unique_ptr<DatabaseLoader> create_database_loader(
    const std::string& type,
    std::unique_ptr<extract::IDatabaseConnection> connection,
    const DatabaseLoaderOptions& options = {});

/**
 * @brief Register database loaders
 * 
 * @section details Implementation Details
 * - Registers loaders
 * - Thread-safe
 */
void register_database_loaders();

} // namespace omop::load

File src/lib/load/additional_loaders.h:

#pragma once

#include "load/batch_loader.h"
#include "load/loader_base.h"
#include <nlohmann/json.hpp>

namespace omop::load {

/**
 * @brief JSON batch loader
 *
 * Specialized batch loader for JSON file output with support for
 * nested structures and pretty printing.
 */
class JsonBatchLoader : public BatchLoader {
public:
    /**
     * @brief JSON output options
     */
    struct JsonOptions {
        bool pretty_print{true};
        int indent_size{2};
        bool include_metadata{true};
        bool array_output{true};  // If true, output as array; if false, as NDJSON
        std::string date_format{"%Y-%m-%d %H:%M:%S"};
    };

    /**
     * @brief Constructor
     * @param options Batch loader options
     * @param json_options JSON-specific options
     */
    JsonBatchLoader(BatchLoaderOptions options,
                    JsonOptions json_options);

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "json_batch"; }

protected:
    /**
     * @brief Process a batch by writing to JSON
     * @param batch Batch to process
     * @param context Processing context
     * @return size_t Number of successfully processed records
     */
    size_t process_batch(std::unique_ptr<EnhancedBatch> batch,
                        core::ProcessingContext& context) override;

    /**
     * @brief Initialize JSON batch loader
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Finalize JSON output
     * @param context Processing context
     */
    void do_finalize(core::ProcessingContext& context) override;

private:
    /**
     * @brief Convert record to JSON object
     * @param record Record to convert
     * @return nlohmann::json JSON object
     */
    nlohmann::json record_to_json(const core::Record& record);

    /**
     * @brief Convert std::any value to JSON
     * @param value Value to convert
     * @return nlohmann::json JSON value
     */
    nlohmann::json any_to_json(const std::any& value);

    JsonOptions json_options_;
    std::string output_file_;
    std::ofstream output_stream_;
    std::mutex output_mutex_;
    bool first_batch_{true};
    nlohmann::json json_array_;  // For array output mode
};

/**
 * @brief HTTP/REST API loader
 *
 * Network-based loader that sends data to HTTP endpoints.
 */
class HttpLoader : public NetworkLoaderBase {
public:
    /**
     * @brief HTTP options
     */
    struct HttpOptions {
        std::string method{"POST"};
        std::unordered_map<std::string, std::string> headers;
        std::string content_type{"application/json"};
        size_t timeout_seconds{30};
        size_t retry_count{3};
        size_t retry_delay_ms{1000};
        bool use_compression{true};
        std::string auth_type;  // "basic", "bearer", "apikey"
        std::string auth_credentials;
    };

    /**
     * @brief Constructor
     * @param options HTTP options
     */
    explicit HttpLoader(HttpOptions options);

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "http"; }

    /**
     * @brief Finalize the loader
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

protected:
    /**
     * @brief Connect to HTTP endpoint
     * @param endpoint Endpoint URL
     * @param timeout Connection timeout
     */
    void connect(const std::string& endpoint,
                std::chrono::seconds timeout) override;

    /**
     * @brief Disconnect from endpoint
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    bool is_connected() const override;

    /**
     * @brief Send data over HTTP
     * @param data Data to send
     * @param timeout Send timeout
     * @return bool True if successful
     */
    bool send_data(const std::string& data,
                  std::chrono::seconds timeout) override;

private:
    /**
     * @brief Format batch as JSON payload
     * @param batch Batch to format
     * @return std::string JSON payload
     */
    std::string format_batch_payload(const core::RecordBatch& batch);

    /**
     * @brief Send HTTP request with retries
     * @param payload Request payload
     * @return bool True if successful
     */
    bool send_with_retry(const std::string& payload);

    HttpOptions http_options_;
    std::atomic<bool> connected_{false};
    
    // Buffering for batch sending
    std::vector<core::Record> pending_records_;
    std::mutex pending_mutex_;
    size_t batch_threshold_{100};
};

/**
 * @brief Multi-format loader
 *
 * Loader that can output to multiple formats simultaneously.
 */
class MultiFormatLoader : public LoaderBase {
public:
    /**
     * @brief Constructor
     * @param name Loader name
     */
    explicit MultiFormatLoader(const std::string& name = "multi_format");

    /**
     * @brief Add a sub-loader
     * @param loader Loader to add
     * @param weight Relative weight for load distribution (default 1.0)
     */
    void add_loader(std::unique_ptr<core::ILoader> loader, double weight = 1.0);

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded by all loaders
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "multi_format"; }

    /**
     * @brief Get number of sub-loaders
     * @return size_t Number of loaders
     */
    size_t loader_count() const { return loaders_.size(); }

protected:
    /**
     * @brief Initialize all sub-loaders
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Finalize all sub-loaders
     * @param context Processing context
     */
    void do_finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get additional statistics from all loaders
     * @return std::unordered_map<std::string, std::any> Combined statistics
     */
    std::unordered_map<std::string, std::any> get_additional_statistics() const override;

private:
    struct LoaderInfo {
        std::unique_ptr<core::ILoader> loader;
        double weight;
        size_t success_count{0};
        size_t failure_count{0};
    };

    std::vector<LoaderInfo> loaders_;
    bool fail_on_any_{false};  // If true, fail if any loader fails
    bool parallel_load_{true};  // If true, load to all loaders in parallel
};

/**
 * @brief S3-compatible object storage loader
 *
 * Loader for writing to S3-compatible object storage systems.
 */
class S3Loader : public NetworkLoaderBase {
public:
    /**
     * @brief S3 options
     */
    struct S3Options {
        std::string bucket_name;
        std::string key_prefix;
        std::string region{"us-east-1"};
        std::string access_key_id;
        std::string secret_access_key;
        std::string session_token;  // Optional for temporary credentials
        bool use_multipart_upload{true};
        size_t multipart_threshold{5 * 1024 * 1024};  // 5MB
        size_t part_size{5 * 1024 * 1024};  // 5MB
        std::string storage_class{"STANDARD"};
        bool server_side_encryption{false};
        std::string sse_algorithm{"AES256"};
        std::unordered_map<std::string, std::string> metadata;
    };

    /**
     * @brief Constructor
     * @param options S3 options
     */
    explicit S3Loader(S3Options options);

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "s3"; }

    /**
     * @brief Finalize the loader
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

protected:
    /**
     * @brief Connect to S3 endpoint
     * @param endpoint Endpoint URL
     * @param timeout Connection timeout
     */
    void connect(const std::string& endpoint,
                std::chrono::seconds timeout) override;

    /**
     * @brief Disconnect from S3
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    bool is_connected() const override;

    /**
     * @brief Send data to S3
     * @param data Data to send
     * @param timeout Send timeout
     * @return bool True if successful
     */
    bool send_data(const std::string& data,
                  std::chrono::seconds timeout) override;

private:
    /**
     * @brief Generate S3 object key
     * @param suffix Key suffix
     * @return std::string Full object key
     */
    std::string generate_object_key(const std::string& suffix = "");

    /**
     * @brief Upload buffer to S3
     * @param key Object key
     * @param data Data to upload
     * @return bool True if successful
     */
    bool upload_to_s3(const std::string& key, const std::string& data);

    /**
     * @brief Start multipart upload
     * @param key Object key
     * @return std::string Upload ID
     */
    std::string start_multipart_upload(const std::string& key);

    /**
     * @brief Upload part
     * @param key Object key
     * @param upload_id Upload ID
     * @param part_number Part number
     * @param data Part data
     * @return std::string ETag
     */
    std::string upload_part(const std::string& key,
                           const std::string& upload_id,
                           int part_number,
                           const std::string& data);

    /**
     * @brief Complete multipart upload
     * @param key Object key
     * @param upload_id Upload ID
     * @param parts Part ETags
     * @return bool True if successful
     */
    bool complete_multipart_upload(const std::string& key,
                                  const std::string& upload_id,
                                  const std::vector<std::pair<int, std::string>>& parts);

    S3Options s3_options_;
    std::string current_key_;
    std::string current_upload_id_;
    std::vector<std::pair<int, std::string>> uploaded_parts_;
    std::ostringstream buffer_;
    size_t buffer_size_{0};
    int next_part_number_{1};
    std::mutex upload_mutex_;
};

} // namespace omop::load

File src/lib/load/loader_base.h:

/**
 * @file loader_base.h
 * @brief Base classes for data loaders in OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header provides base classes for implementing data loaders in the OMOP ETL pipeline,
 * including common functionality for file-based and network-based loaders.
 * 
 * @section design Design Principles
 * - Inheritance: Base class hierarchy
 * - Thread Safety: Atomic counters and mutexes
 * - Error Handling: Comprehensive error tracking
 * - Statistics: Performance and progress tracking
 * - Configuration: Type-safe configuration management
 * 
 * @section components Components
 * - LoaderBase: Abstract base class for all loaders
 * - FileLoaderBase: Base class for file-based loaders
 * - NetworkLoaderBase: Base class for network-based loaders
 * 
 * @section usage Usage
 * To implement a new loader:
 * 1. Inherit from appropriate base class
 * 2. Implement required virtual methods
 * 3. Use protected helper methods
 * 4. Track statistics and errors
 * 
 * @section example Example
 * @code
 * class MyLoader : public LoaderBase {
 * protected:
 *     void do_initialize(const Config& config) override {
 *         // Initialize loader
 *     }
 * };
 * @endcode
 */

#pragma once

#include "core/interfaces.h"
#include "common/exceptions.h"
#include <chrono>
#include <atomic>
#include <unordered_map>
#include <any>
#include <fstream>
#include <mutex>
#include <vector>

namespace omop::load {

/**
 * @brief Base class for all loaders
 * 
 * @section description Description
 * Provides common functionality and statistics tracking for data loaders,
 * implementing the ILoader interface and providing shared functionality
 * for derived loader classes.
 * 
 * @section features Features
 * - Initialization management
 * - Statistics tracking
 * - Error handling
 * - Progress monitoring
 * - Configuration management
 * - Thread safety
 */
class LoaderBase : public core::ILoader {
public:
    /**
     * @brief Constructor
     * @param name Loader name for identification
     * 
     * @section details Implementation Details
     * - Sets name
     * - Initializes counters
     * - Thread-safe
     */
    explicit LoaderBase(const std::string& name);

    /**
     * @brief Virtual destructor
     * 
     * @section details Implementation Details
     * - Default implementation
     * - Thread-safe
     */
    virtual ~LoaderBase() = default;

    /**
     * @brief Initialize the loader
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Validates config
     * - Calls do_initialize
     * - Sets start time
     * - Thread-safe
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Get loader statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     * 
     * @section details Implementation Details
     * - Combines base stats
     * - Adds derived stats
     * - Thread-safe
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

    /**
     * @brief Finalize the loader
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Calls do_finalize
     * - Sets end time
     * - Thread-safe
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get loader name
     * @return const std::string& Loader name
     * 
     * @section details Implementation Details
     * - Returns name
     * - Thread-safe
     */
    const std::string& get_name() const { return name_; }

    /**
     * @brief Check if loader is initialized
     * @return bool True if initialized
     * 
     * @section details Implementation Details
     * - Returns state
     * - Thread-safe
     */
    bool is_initialized() const { return initialized_; }

protected:
    /**
     * @brief Perform loader-specific initialization
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Pure virtual
     * - Must be implemented
     * - Thread-safe
     */
    virtual void do_initialize(const std::unordered_map<std::string, std::any>& config,
                              core::ProcessingContext& context) = 0;

    /**
     * @brief Perform loader-specific finalization
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Optional override
     * - Thread-safe
     */
    virtual void do_finalize(core::ProcessingContext& context) {}

    /**
     * @brief Get loader-specific statistics
     * @return std::unordered_map<std::string, std::any> Additional statistics
     * 
     * @section details Implementation Details
     * - Optional override
     * - Thread-safe
     */
    virtual std::unordered_map<std::string, std::any> get_additional_statistics() const {
        return {};
    }

    /**
     * @brief Update loading progress
     * @param loaded Number of successfully loaded records
     * @param failed Number of failed records
     * 
     * @section details Implementation Details
     * - Updates counters
     * - Thread-safe
     */
    void update_progress(size_t loaded, size_t failed = 0);

    /**
     * @brief Record an error
     * @param error_message Error message
     * @param record_info Optional record information
     * 
     * @section details Implementation Details
     * - Adds error
     * - Thread-safe
     */
    void record_error(const std::string& error_message, 
                     const std::string& record_info = "");

    /**
     * @brief Get elapsed time since initialization
     * @return std::chrono::duration<double> Elapsed time in seconds
     * 
     * @section details Implementation Details
     * - Calculates duration
     * - Thread-safe
     */
    std::chrono::duration<double> get_elapsed_time() const;

    /**
     * @brief Check if configuration contains a key
     * @param config Configuration map
     * @param key Key to check
     * @return bool True if key exists
     * 
     * @section details Implementation Details
     * - Checks existence
     * - Thread-safe
     */
    bool has_config_key(const std::unordered_map<std::string, std::any>& config,
                       const std::string& key) const;

    /**
     * @brief Get configuration value with type checking
     * @tparam T Expected value type
     * @param config Configuration map
     * @param key Configuration key
     * @param default_value Default value if key not found
     * @return T Configuration value
     * 
     * @section details Implementation Details
     * - Type checks
     * - Returns default
     * - Thread-safe
     */
    template<typename T>
    T get_config_value(const std::unordered_map<std::string, std::any>& config,
                      const std::string& key,
                      const T& default_value) const {
        auto it = config.find(key);
        if (it != config.end()) {
            try {
                return std::any_cast<T>(it->second);
            } catch (const std::bad_any_cast& e) {
                throw common::ConfigurationException(
                    std::format("Invalid type for configuration key '{}': {}", key, e.what()));
            }
        }
        return default_value;
    }

private:
    std::string name_;  ///< Loader name
    bool initialized_{false};  ///< Initialization state
    
    // Statistics tracking
    std::atomic<size_t> total_loaded_{0};  ///< Total loaded records
    std::atomic<size_t> total_failed_{0};  ///< Total failed records
    std::atomic<size_t> total_processed_{0};  ///< Total processed records
    std::chrono::steady_clock::time_point start_time_;  ///< Start time
    std::chrono::steady_clock::time_point end_time_;  ///< End time
    
    // Error tracking
    mutable std::mutex error_mutex_;  ///< Error mutex
    std::vector<std::pair<std::string, std::chrono::steady_clock::time_point>> errors_;  ///< Error list
    static constexpr size_t MAX_ERRORS_TO_TRACK = 100;  ///< Max errors to track
};

/**
 * @brief File-based loader base class
 * 
 * @section description Description
 * Base class for loaders that write to files (CSV, JSON, etc.),
 * providing file handling and buffering functionality.
 * 
 * @section features Features
 * - File management
 * - Buffered writing
 * - File statistics
 * - Thread safety
 */
class FileLoaderBase : public LoaderBase {
public:
    /**
     * @brief Constructor
     * @param name Loader name
     * @param file_extension Default file extension
     * 
     * @section details Implementation Details
     * - Sets name
     * - Sets extension
     * - Thread-safe
     */
    FileLoaderBase(const std::string& name, const std::string& file_extension);

    /**
     * @brief Destructor
     * 
     * @section details Implementation Details
     * - Closes file
     * - Thread-safe
     */
    ~FileLoaderBase() override;

protected:
    /**
     * @brief Perform file loader initialization
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Opens file
     * - Thread-safe
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Perform file loader finalization
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Closes file
     * - Thread-safe
     */
    void do_finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get additional statistics for file loader
     * @return std::unordered_map<std::string, std::any> File-specific statistics
     * 
     * @section details Implementation Details
     * - Returns stats
     * - Thread-safe
     */
    std::unordered_map<std::string, std::any> get_additional_statistics() const override;

    /**
     * @brief Open output file
     * @param filename File path
     * @param append Whether to append to existing file
     * 
     * @section details Implementation Details
     * - Opens file
     * - Thread-safe
     */
    virtual void open_file(const std::string& filename, bool append = false);

    /**
     * @brief Close output file
     * 
     * @section details Implementation Details
     * - Closes file
     * - Thread-safe
     */
    virtual void close_file();

    /**
     * @brief Write data to file
     * @param data Data to write
     * 
     * @section details Implementation Details
     * - Writes data
     * - Thread-safe
     */
    virtual void write_to_file(const std::string& data);

    /**
     * @brief Flush file buffer
     * 
     * @section details Implementation Details
     * - Flushes buffer
     * - Thread-safe
     */
    virtual void flush_file();

    /**
     * @brief Get output file path
     * @return const std::string& File path
     * 
     * @section details Implementation Details
     * - Returns path
     * - Thread-safe
     */
    const std::string& get_file_path() const { return file_path_; }

    /**
     * @brief Check if file is open
     * @return bool True if file is open
     * 
     * @section details Implementation Details
     * - Returns state
     * - Thread-safe
     */
    bool is_file_open() const { return file_stream_.is_open(); }

    /**
     * @brief Get file extension
     * @return const std::string& File extension
     * 
     * @section details Implementation Details
     * - Returns extension
     * - Thread-safe
     */
    const std::string& get_file_extension() const { return file_extension_; }

private:
    std::string file_extension_;  ///< File extension
    std::string file_path_;  ///< File path
    std::ofstream file_stream_;  ///< File stream
    std::atomic<size_t> bytes_written_{0};  ///< Bytes written
    mutable std::mutex file_mutex_;  ///< File mutex
};

/**
 * @brief Network-based loader base class
 * 
 * @section description Description
 * Base class for loaders that send data over network protocols,
 * providing connection management and network statistics.
 * 
 * @section features Features
 * - Connection management
 * - Network statistics
 * - Timeout handling
 * - Thread safety
 */
class NetworkLoaderBase : public LoaderBase {
public:
    /**
     * @brief Constructor
     * @param name Loader name
     * @param protocol Network protocol
     * 
     * @section details Implementation Details
     * - Sets name
     * - Sets protocol
     * - Thread-safe
     */
    NetworkLoaderBase(const std::string& name, const std::string& protocol);

    /**
     * @brief Destructor
     * 
     * @section details Implementation Details
     * - Closes connection
     * - Thread-safe
     */
    ~NetworkLoaderBase() override;

protected:
    /**
     * @brief Perform network loader initialization
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Connects to endpoint
     * - Thread-safe
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Connect to network endpoint
     * @param endpoint Network endpoint
     * @param timeout Connection timeout
     * 
     * @section details Implementation Details
     * - Pure virtual
     * - Must be implemented
     * - Thread-safe
     */
    virtual void connect(const std::string& endpoint,
                        std::chrono::seconds timeout = std::chrono::seconds(30)) = 0;

    /**
     * @brief Send data over network
     * @param data Data to send
     * @param timeout Send timeout
     * @return bool True if successful
     * 
     * @section details Implementation Details
     * - Pure virtual
     * - Must be implemented
     * - Thread-safe
     */
    virtual bool send_data(const std::string& data,
                          std::chrono::seconds timeout = std::chrono::seconds(30)) = 0;

    /**
     * @brief Update network statistics
     * @param bytes_sent Bytes sent
     * @param success Success flag
     * 
     * @section details Implementation Details
     * - Updates stats
     * - Thread-safe
     */
    void update_network_stats(size_t bytes_sent, bool success);

    /**
     * @brief Get network endpoint
     * @return const std::string& Endpoint
     * 
     * @section details Implementation Details
     * - Returns endpoint
     * - Thread-safe
     */
    const std::string& get_endpoint() const { return endpoint_; }

    /**
     * @brief Get network protocol
     * @return const std::string& Protocol
     * 
     * @section details Implementation Details
     * - Returns protocol
     * - Thread-safe
     */
    const std::string& get_protocol() const { return protocol_; }

private:
    std::string protocol_;  ///< Network protocol
    std::string endpoint_;  ///< Network endpoint
    std::atomic<size_t> bytes_sent_{0};  ///< Bytes sent
    std::atomic<size_t> send_errors_{0};  ///< Send errors
    mutable std::mutex network_mutex_;  ///< Network mutex
};

} // namespace omop::load

File src/lib/extract/csv_extractor.h:

#pragma once

#include "extract/database_connector.h"
#include "core/interfaces.h"
#include <fstream>
#include <sstream>
#include <filesystem>
#include <regex>

namespace omop::extract {

/**
 * @brief CSV parsing options
 */
struct CsvOptions {
    char delimiter{','};
    char quote_char{'"'};
    char escape_char{'\\'};
    bool has_header{true};
    std::string encoding{"UTF-8"};
    std::vector<std::string> column_names;
    std::vector<std::string> column_types;
    bool skip_empty_lines{true};
    bool trim_fields{true};
    size_t skip_lines{0};
    std::optional<size_t> max_lines;
    std::string null_string{"NULL"};
    std::string true_string{"TRUE"};
    std::string false_string{"FALSE"};
    std::string date_format{"%Y-%m-%d"};
    std::string datetime_format{"%Y-%m-%d %H:%M:%S"};
};

/**
 * @brief CSV field parser
 *
 * Handles parsing of individual CSV fields with proper quote and escape handling.
 */
class CsvFieldParser {
public:
    /**
     * @brief Constructor
     * @param options CSV parsing options
     */
    explicit CsvFieldParser(const CsvOptions& options) : options_(options) {}

    /**
     * @brief Parse a single field
     * @param input Input string
     * @param pos Current position (updated)
     * @return std::string Parsed field value
     */
    std::string parse_field(const std::string& input, size_t& pos);

    /**
     * @brief Parse a complete line
     * @param line Input line
     * @return std::vector<std::string> Parsed fields
     */
    std::vector<std::string> parse_line(const std::string& line);

    /**
     * @brief Convert field to typed value
     * @param field Field value
     * @param type_hint Type hint (optional)
     * @return std::any Typed value
     */
    std::any convert_field(const std::string& field,
                          const std::string& type_hint = "");

    /**
     * @brief Parse date/time value
     * @param value String value
     * @param format Format string
     * @return std::chrono::system_clock::time_point Parsed time
     */
    std::chrono::system_clock::time_point parse_datetime(
        const std::string& value, const std::string& format) const;

private:
    CsvOptions options_;

    /**
     * @brief Trim whitespace from string
     * @param str String to trim
     * @return std::string Trimmed string
     */
    std::string trim(const std::string& str) const;
};

/**
 * @brief CSV file extractor
 *
 * Implements the IExtractor interface for CSV file sources,
 * providing efficient streaming extraction from CSV files.
 */
class CsvExtractor : public core::IExtractor {
public:
    /**
     * @brief Constructor
     */
    CsvExtractor() : parser_(options_) {}

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "csv"; }

    /**
     * @brief Finalize extraction
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Open CSV file
     * @param filepath File path
     */
    void open_file(const std::string& filepath);

    /**
     * @brief Read and parse header
     */
    void read_header();

    /**
     * @brief Infer column types from data
     * @param sample_size Number of rows to sample
     */
    void infer_column_types(size_t sample_size = 100);

    /**
     * @brief Create record from parsed fields
     * @param fields Field values
     * @return core::Record Created record
     */
    core::Record create_record(const std::vector<std::string>& fields);

protected:
    std::ifstream file_stream_;
    std::string filepath_;
    CsvOptions options_;
    CsvFieldParser parser_;
    std::vector<std::string> column_names_;
    std::vector<std::string> column_types_;
    size_t current_line_{0};
    size_t total_lines_{0};
    size_t extracted_count_{0};
    size_t error_count_{0};
    bool has_more_{true};
    std::chrono::steady_clock::time_point start_time_;
};

/**
 * @brief Multi-file CSV extractor
 *
 * Extends CsvExtractor to handle multiple CSV files as a single data source.
 */
class MultiFileCsvExtractor : public CsvExtractor {
public:
    /**
     * @brief Constructor
     */
    MultiFileCsvExtractor() = default;

    /**
     * @brief Initialize with multiple files
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "multi_csv"; }

protected:
    /**
     * @brief Move to next file
     * @return bool True if next file opened successfully
     */
    bool next_file();

    std::vector<std::string> file_paths_;
    size_t current_file_index_{0};
    bool skip_headers_after_first_{true};
};

/**
 * @brief CSV directory extractor
 *
 * Extracts data from all CSV files in a directory with pattern matching.
 */
class CsvDirectoryExtractor : public MultiFileCsvExtractor {
public:
    /**
     * @brief Constructor
     */
    CsvDirectoryExtractor() = default;

    /**
     * @brief Initialize with directory
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "csv_directory"; }

protected:
    /**
     * @brief Find CSV files in directory
     * @param directory Directory path
     * @param pattern File name pattern (regex)
     * @param recursive Whether to search recursively
     * @return std::vector<std::string> File paths
     */
    std::vector<std::string> find_csv_files(const std::string& directory,
                                           const std::string& pattern = ".*\\.csv$",
                                           bool recursive = false);

private:
    std::string directory_path_;
    std::regex file_pattern_;
    bool recursive_search_{false};
};

/**
 * @brief Compressed CSV extractor
 *
 * Handles extraction from compressed CSV files (gzip, zip, etc.).
 */
class CompressedCsvExtractor : public CsvExtractor {
public:
    /**
     * @brief Compression format
     */
    enum class CompressionFormat {
        None,
        Gzip,
        Zip,
        Bzip2,
        Xz
    };

    /**
     * @brief Constructor
     */
    CompressedCsvExtractor() = default;

    /**
     * @brief Initialize with compressed file
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "compressed_csv"; }

    /**
     * @brief Finalize extraction and cleanup
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

protected:
    /**
     * @brief Detect compression format
     * @param filepath File path
     * @return CompressionFormat Detected format
     */
    CompressionFormat detect_compression(const std::string& filepath);

    /**
     * @brief Decompress file
     * @param filepath Compressed file path
     * @param format Compression format
     * @return std::string Path to decompressed file
     */
    std::string decompress_file(const std::string& filepath,
                               CompressionFormat format);

    /**
     * @brief Convert compression format to string
     * @param format Compression format
     * @return std::string String representation
     */
    std::string format_to_string(CompressionFormat format);

    /**
     * @brief Convert string to compression format
     * @param format_str String representation
     * @return CompressionFormat Compression format
     */
    CompressionFormat string_to_format(const std::string& format_str);

private:
    CompressionFormat compression_format_{CompressionFormat::None};
    std::string temp_file_path_;
    bool cleanup_temp_file_{true};
};

/**
 * @brief CSV extractor factory
 */
class CsvExtractorFactory {
public:
    /**
     * @brief Create CSV extractor
     * @param type Extractor type (csv, multi_csv, csv_directory, compressed_csv)
     * @return std::unique_ptr<core::IExtractor> Extractor instance
     */
    static std::unique_ptr<core::IExtractor> create(const std::string& type);

    /**
     * @brief Register CSV extractors with the main factory
     */
    static void register_extractors();
};

// Implementation helpers

inline std::string CsvFieldParser::trim(const std::string& str) const {
    if (!options_.trim_fields) return str;

    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) return "";

    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

inline std::string CsvFieldParser::parse_field(const std::string& input, size_t& pos) {
    std::string field;
    bool in_quotes = false;
    bool escape_next = false;

    // Skip leading whitespace
    while (pos < input.length() && std::isspace(input[pos]) && input[pos] != '\n') {
        ++pos;
    }

    // Check if field starts with quote
    if (pos < input.length() && input[pos] == options_.quote_char) {
        in_quotes = true;
        ++pos;
    }

    while (pos < input.length()) {
        char c = input[pos];

        if (escape_next) {
            field += c;
            escape_next = false;
            ++pos;
            continue;
        }

        if (c == options_.escape_char) {
            escape_next = true;
            ++pos;
            continue;
        }

        if (in_quotes) {
            if (c == options_.quote_char) {
                // Check for escaped quote (double quote)
                if (pos + 1 < input.length() && input[pos + 1] == options_.quote_char) {
                    field += c;
                    pos += 2;
                    continue;
                }
                in_quotes = false;
                ++pos;
                // Skip to delimiter or end of line
                while (pos < input.length() &&
                       input[pos] != options_.delimiter &&
                       input[pos] != '\n') {
                    ++pos;
                }
                break;
            }
            field += c;
            ++pos;
        } else {
            if (c == options_.delimiter || c == '\n') {
                break;
            }
            field += c;
            ++pos;
        }
    }

    // Skip delimiter if present
    if (pos < input.length() && input[pos] == options_.delimiter) {
        ++pos;
    }

    return trim(field);
}

inline std::vector<std::string> CsvFieldParser::parse_line(const std::string& line) {
    std::vector<std::string> fields;
    size_t pos = 0;

    while (pos < line.length()) {
        fields.push_back(parse_field(line, pos));
        if (pos >= line.length() || line[pos] == '\n') {
            break;
        }
    }

    return fields;
}

} // namespace omop::extract

File src/lib/extract/mysql_connector.h:

/**
 * @file mysql_connector.h
 * @brief MySQL database connector interface
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file provides the MySQL implementation of the database connector
 * interface, supporting MySQL and MariaDB databases.
 */

#pragma once

#include "extract/database_connector.h"
#include <mysql.h>
#include <memory>
#include <mutex>
#include <vector>

namespace omop::extract {

/**
 * @brief RAII wrapper for MySQL statement
 */
class MySQLStatement {
public:
    /**
     * @brief Constructor
     * @param mysql MySQL connection handle
     */
    explicit MySQLStatement(MYSQL* mysql) {
        stmt_ = mysql_stmt_init(mysql);
        if (!stmt_) {
            throw common::DatabaseException("Failed to initialize MySQL statement", "MySQL", 0);
        }
    }

    /**
     * @brief Destructor
     */
    ~MySQLStatement() {
        if (stmt_) {
            mysql_stmt_close(stmt_);
        }
    }

    // Delete copy operations
    MySQLStatement(const MySQLStatement&) = delete;
    MySQLStatement& operator=(const MySQLStatement&) = delete;

    /**
     * @brief Get raw statement handle
     * @return MYSQL_STMT* Statement handle
     */
    MYSQL_STMT* get() { return stmt_; }

private:
    MYSQL_STMT* stmt_;
};

/**
 * @brief MySQL result set implementation
 *
 * This class provides access to MySQL query results through the
 * IResultSet interface, handling type conversions and NULL values.
 */
class MySQLResultSet : public ResultSetBase {
public:
    /**
     * @brief Constructor
     * @param statement MySQL statement
     */
    explicit MySQLResultSet(std::shared_ptr<MySQLStatement> statement);

    /**
     * @brief Destructor
     */
    ~MySQLResultSet() override;

    /**
     * @brief Move to next row
     * @return bool True if successful
     */
    bool next() override;

    /**
     * @brief Get column value by index
     * @param index Column index (0-based)
     * @return std::any Column value
     */
    std::any get_value(size_t index) const override;

    /**
     * @brief Get column value by name
     * @param column_name Column name
     * @return std::any Column value
     */
    std::any get_value(const std::string& column_name) const override;

    /**
     * @brief Check if column value is NULL
     * @param index Column index
     * @return bool True if NULL
     */
    bool is_null(size_t index) const override;

    /**
     * @brief Check if column value is NULL
     * @param column_name Column name
     * @return bool True if NULL
     */
    bool is_null(const std::string& column_name) const override;

    /**
     * @brief Get column count
     * @return size_t Number of columns
     */
    size_t column_count() const override;

    /**
     * @brief Get column name by index
     * @param index Column index
     * @return std::string Column name
     */
    std::string column_name(size_t index) const override;

    /**
     * @brief Get column type by index
     * @param index Column index
     * @return std::string Column type name
     */
    std::string column_type(size_t index) const override;

private:
    /**
     * @brief Column metadata
     */
    struct ColumnInfo {
        std::string name;
        enum_field_types type;
        unsigned long length;
        unsigned int flags;
        unsigned int decimals;
    };

    /**
     * @brief MySQL bind buffer
     */
    struct BindBuffer {
        enum_field_types buffer_type;
        void* buffer;
        unsigned long buffer_length;
        bool is_null_value;
        unsigned long length_value;
        bool error_value;
        bool* is_null;
        unsigned long* length;
        bool* error;
    };

    /**
     * @brief Get column index by name
     * @param column_name Column name
     * @return size_t Column index
     * @throws DatabaseException if column not found
     */
    size_t get_column_index(const std::string& column_name) const;

    /**
     * @brief Convert MySQL value to appropriate type
     * @param index Column index
     * @return std::any Converted value
     */
    std::any convert_value(size_t index) const;

    /**
     * @brief Load column metadata
     */
    void load_metadata();

    std::shared_ptr<MySQLStatement> statement_;
    MYSQL_RES* result_;
    std::vector<ColumnInfo> columns_;
    std::vector<MYSQL_BIND> bind_buffers_;
    mutable std::unordered_map<std::string, size_t> column_index_cache_;
    int current_row_{-1};
    my_ulonglong row_count_{0};
    unsigned int column_count_{0};
};

/**
 * @brief MySQL prepared statement implementation
 *
 * This class implements prepared statements for MySQL,
 * providing parameterized query execution.
 */
class MySQLPreparedStatement : public IPreparedStatement {
public:
    /**
     * @brief Constructor
     * @param mysql MySQL connection
     * @param sql SQL query
     */
    MySQLPreparedStatement(MYSQL* mysql, const std::string& sql);

    /**
     * @brief Destructor
     */
    ~MySQLPreparedStatement() override;

    /**
     * @brief Bind parameter by index
     * @param index Parameter index (1-based)
     * @param value Parameter value
     */
    void bind(size_t index, const std::any& value) override;

    /**
     * @brief Execute query and return result set
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query() override;

    /**
     * @brief Execute update/insert/delete
     * @return size_t Number of affected rows
     */
    size_t execute_update() override;

    /**
     * @brief Clear all bound parameters
     */
    void clear_parameters() override;

private:
    /**
     * @brief Parameter binding information
     */
    struct ParameterBinding {
        std::any value;
        MYSQL_BIND bind;
        std::vector<char> buffer;
        bool is_null;
        unsigned long length;
    };

    /**
     * @brief Bind all parameters
     */
    void bind_parameters();

    /**
     * @brief Set up parameter binding
     * @param binding Parameter binding
     * @param value Parameter value
     */
    void setup_parameter_binding(ParameterBinding& binding, const std::any& value);

    std::shared_ptr<MySQLStatement> statement_;
    std::string sql_;
    std::vector<ParameterBinding> parameters_;
    size_t param_count_{0};
};

/**
 * @brief MySQL database connection implementation
 *
 * This class provides the concrete implementation of IDatabaseConnection
 * for MySQL databases, using the MySQL C API.
 */
class MySQLConnection : public IDatabaseConnection {
public:
    /**
     * @brief Constructor
     */
    MySQLConnection();

    /**
     * @brief Destructor
     */
    ~MySQLConnection() override;

    /**
     * @brief Connect to database
     * @param params Connection parameters
     */
    void connect(const ConnectionParams& params) override;

    /**
     * @brief Disconnect from database
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    bool is_connected() const override;

    /**
     * @brief Execute SQL query
     * @param sql SQL query string
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query(const std::string& sql) override;

    /**
     * @brief Execute SQL update/insert/delete
     * @param sql SQL statement
     * @return size_t Number of affected rows
     */
    size_t execute_update(const std::string& sql) override;

    /**
     * @brief Prepare SQL statement
     * @param sql SQL statement with parameter placeholders
     * @return std::unique_ptr<IPreparedStatement> Prepared statement
     */
    std::unique_ptr<IPreparedStatement> prepare_statement(const std::string& sql) override;

    /**
     * @brief Begin transaction
     */
    void begin_transaction() override;

    /**
     * @brief Commit transaction
     */
    void commit() override;

    /**
     * @brief Rollback transaction
     */
    void rollback() override;

    /**
     * @brief Get database type name
     * @return std::string Database type
     */
    std::string get_database_type() const override { return "MySQL"; }

    /**
     * @brief Get database version
     * @return std::string Database version string
     */
    std::string get_version() const override;

    /**
     * @brief Set query timeout
     * @param seconds Timeout in seconds
     */
    void set_query_timeout(int seconds) override;

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return bool True if table exists
     */
    bool table_exists(const std::string& table_name,
                     const std::string& schema = "") const override;

    /**
     * @brief Get raw MySQL connection handle
     * @return MYSQL* Connection handle (for internal use)
     */
    MYSQL* get_raw_connection() { return mysql_; }

private:
    /**
     * @brief Check for MySQL errors and throw if needed
     * @param operation Operation description
     */
    void check_error(const std::string& operation) const;

    /**
     * @brief Set connection options from parameters
     * @param params Connection parameters
     */
    void set_connection_options(const ConnectionParams& params);

    MYSQL* mysql_;
    bool connected_{false};
    bool in_transaction_{false};
    mutable std::mutex connection_mutex_;
    int query_timeout_{0};
};

/**
 * @brief MySQL-specific database extractor
 *
 * This class extends DatabaseExtractor with MySQL-specific optimizations.
 */
class MySQLExtractor : public DatabaseExtractor {
public:
    /**
     * @brief Constructor
     * @param connection MySQL connection
     */
    explicit MySQLExtractor(std::unique_ptr<IDatabaseConnection> connection)
        : DatabaseExtractor(std::move(connection)) {}

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "mysql"; }

protected:
    /**
     * @brief Build extraction query with MySQL-specific optimizations
     * @return std::string SQL query
     */
    std::string build_query() const override;
};

/**
 * @brief Registration helper for MySQL components
 */
class MySQLRegistrar {
public:
    /**
     * @brief Register all MySQL components
     */
    static void register_components() {
        DatabaseConnectionFactory::instance().register_type(
            "mysql",
            [](const IDatabaseConnection::ConnectionParams& params) {
                auto conn = std::make_unique<MySQLConnection>();
                conn->connect(params);
                return conn;
            }
        );

        DatabaseConnectionFactory::instance().register_type(
            "mariadb",  // Alias for MariaDB
            [](const IDatabaseConnection::ConnectionParams& params) {
                auto conn = std::make_unique<MySQLConnection>();
                conn->connect(params);
                return conn;
            }
        );
    }

private:
    MySQLRegistrar() = default;
};

} // namespace omop::extract

File src/lib/extract/extractor_factory.h:

/**
 * @file extractor_factory.h
 * @brief Extractor factory and registry interface
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file provides the factory registry for creating and managing
 * different types of data extractors in the OMOP ETL pipeline.
 */

#pragma once

#include "core/interfaces.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>
#include <mutex>
#include <any>

namespace omop::extract {

/**
 * @brief Central registry for extractor types
 *
 * This class maintains a registry of all available extractor types
 * and provides factory methods for creating instances.
 */
class ExtractorFactoryRegistry {
public:
    /**
     * @brief Register an extractor type
     * @param type Extractor type identifier
     * @param creator Factory function
     */
    static void register_type(const std::string& type,
                            std::function<std::unique_ptr<core::IExtractor>()> creator);

    /**
     * @brief Create an extractor instance
     * @param type Extractor type identifier
     * @return std::unique_ptr<core::IExtractor> Extractor instance
     * @throws ConfigurationException if type is not registered
     */
    static std::unique_ptr<core::IExtractor> create(const std::string& type);

    /**
     * @brief Get list of registered extractor types
     * @return std::vector<std::string> Sorted list of type identifiers
     */
    static std::vector<std::string> get_registered_types();

    /**
     * @brief Check if a type is registered
     * @param type Extractor type identifier
     * @return bool True if type is registered
     */
    static bool is_type_registered(const std::string& type);

    /**
     * @brief Clear all registered types (for testing)
     */
    static void clear();

private:
    static std::unordered_map<std::string,
        std::function<std::unique_ptr<core::IExtractor>()>> creators_;
    static std::mutex mutex_;
};

/**
 * @brief Initialize all built-in extractors
 *
 * This function registers all built-in extractor types with the factory.
 * It is called automatically when creating extractors, but can be called
 * manually to ensure all types are available.
 */
void initialize_extractors();

/**
 * @brief Create and initialize an extractor
 * @param type Extractor type identifier
 * @param config Configuration parameters
 * @return std::unique_ptr<core::IExtractor> Initialized extractor
 * @throws ConfigurationException if type is invalid or initialization fails
 */
std::unique_ptr<core::IExtractor> create_extractor(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config = {});

/**
 * @brief Extractor configuration builder
 *
 * Helper class for building extractor configurations with type safety
 * and validation.
 */
class ExtractorConfigBuilder {
public:
    /**
     * @brief Constructor
     * @param type Extractor type
     */
    explicit ExtractorConfigBuilder(const std::string& type) : type_(type) {}

    /**
     * @brief Set configuration parameter
     * @param key Parameter name
     * @param value Parameter value
     * @return ExtractorConfigBuilder& Builder instance for chaining
     */
    template<typename T>
    ExtractorConfigBuilder& set(const std::string& key, T&& value) {
        config_[key] = std::forward<T>(value);
        return *this;
    }

    /**
     * @brief Set file path
     * @param path File path
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_file(const std::string& path) {
        return set("filepath", path);
    }

    /**
     * @brief Set multiple files
     * @param files File paths
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_files(const std::vector<std::string>& files) {
        return set("files", files);
    }

    /**
     * @brief Set directory path
     * @param path Directory path
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_directory(const std::string& path) {
        return set("directory", path);
    }

    /**
     * @brief Set database connection parameters
     * @param host Database host
     * @param port Database port
     * @param database Database name
     * @param username Username
     * @param password Password
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_database(const std::string& host,
                                         int port,
                                         const std::string& database,
                                         const std::string& username,
                                         const std::string& password) {
        return set("host", host)
              .set("port", port)
              .set("database", database)
              .set("username", username)
              .set("password", password);
    }

    /**
     * @brief Set table name
     * @param table Table name
     * @param schema Schema name (optional)
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_table(const std::string& table,
                                      const std::string& schema = "") {
        set("table", table);
        if (!schema.empty()) {
            set("schema", schema);
        }
        return *this;
    }

    /**
     * @brief Set columns to extract
     * @param columns Column names
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_columns(const std::vector<std::string>& columns) {
        return set("columns", columns);
    }

    /**
     * @brief Set filter condition
     * @param filter SQL WHERE clause or filter expression
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_filter(const std::string& filter) {
        return set("filter", filter);
    }

    /**
     * @brief Build and create extractor
     * @return std::unique_ptr<core::IExtractor> Configured extractor
     */
    std::unique_ptr<core::IExtractor> build() {
        return create_extractor(type_, config_);
    }

    /**
     * @brief Get configuration map
     * @return std::unordered_map<std::string, std::any> Configuration
     */
    const std::unordered_map<std::string, std::any>& get_config() const {
        return config_;
    }

private:
    std::string type_;
    std::unordered_map<std::string, std::any> config_;
};

/**
 * @brief Extractor type information
 */
struct ExtractorTypeInfo {
    std::string type;                              ///< Type identifier
    std::string description;                       ///< Description
    std::vector<std::string> required_params;      ///< Required parameters
    std::vector<std::string> optional_params;      ///< Optional parameters
    std::string example_config;                    ///< Example configuration JSON
};

/**
 * @brief Get information about all extractor types
 * @return std::vector<ExtractorTypeInfo> Type information
 */
std::vector<ExtractorTypeInfo> get_extractor_info();

/**
 * @brief Print extractor type information
 * @param stream Output stream
 */
void print_extractor_info(std::ostream& stream = std::cout);

} // namespace omop::extract

File src/lib/extract/platform/unix_utils.h:

/**
 * @file unix_utils.h
 * @brief Unix/Linux-specific utility functions for the extract module
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains Unix/Linux-specific implementations for file handling,
 * path operations, and system interactions.
 */

#pragma once

#ifndef _WIN32

#include <string>
#include <vector>
#include <memory>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>

namespace omop::extract::platform {

/**
 * @brief Unix file descriptor wrapper with RAII
 */
class UnixFileDescriptor {
public:
    /**
     * @brief Constructor
     * @param fd File descriptor
     */
    explicit UnixFileDescriptor(int fd = -1) : fd_(fd) {}
    
    /**
     * @brief Destructor
     */
    ~UnixFileDescriptor() {
        if (fd_ >= 0) {
            close(fd_);
        }
    }
    
    // Delete copy operations
    UnixFileDescriptor(const UnixFileDescriptor&) = delete;
    UnixFileDescriptor& operator=(const UnixFileDescriptor&) = delete;
    
    // Move operations
    UnixFileDescriptor(UnixFileDescriptor&& other) noexcept : fd_(other.fd_) {
        other.fd_ = -1;
    }
    
    UnixFileDescriptor& operator=(UnixFileDescriptor&& other) noexcept {
        if (this != &other) {
            if (fd_ >= 0) {
                close(fd_);
            }
            fd_ = other.fd_;
            other.fd_ = -1;
        }
        return *this;
    }
    
    /**
     * @brief Get raw file descriptor
     * @return int File descriptor
     */
    int get() const { return fd_; }
    
    /**
     * @brief Check if descriptor is valid
     * @return bool True if valid
     */
    bool is_valid() const { return fd_ >= 0; }
    
private:
    int fd_;
};

/**
 * @brief Memory mapped file wrapper
 */
class MemoryMappedFile {
public:
    /**
     * @brief Constructor
     */
    MemoryMappedFile() = default;
    
    /**
     * @brief Destructor
     */
    ~MemoryMappedFile();
    
    // Delete copy operations
    MemoryMappedFile(const MemoryMappedFile&) = delete;
    MemoryMappedFile& operator=(const MemoryMappedFile&) = delete;
    
    // Move operations
    MemoryMappedFile(MemoryMappedFile&& other) noexcept;
    MemoryMappedFile& operator=(MemoryMappedFile&& other) noexcept;
    
    /**
     * @brief Map file into memory
     * @param filepath File path
     * @param read_only Map as read-only
     * @return bool True if successful
     */
    bool map_file(const std::string& filepath, bool read_only = true);
    
    /**
     * @brief Unmap file from memory
     */
    void unmap();
    
    /**
     * @brief Get mapped memory pointer
     * @return void* Memory pointer
     */
    void* data() const { return data_; }
    
    /**
     * @brief Get mapped size
     * @return size_t Mapped size
     */
    size_t size() const { return size_; }
    
    /**
     * @brief Check if file is mapped
     * @return bool True if mapped
     */
    bool is_mapped() const { return data_ != nullptr; }
    
private:
    void* data_{nullptr};
    size_t size_{0};
    UnixFileDescriptor fd_;
};

/**
 * @brief Get system error message
 * @param error_code Error code (default = errno)
 * @return std::string Error message
 */
std::string get_system_error_message(int error_code = 0);

/**
 * @brief Get file size
 * @param filepath File path
 * @return size_t File size in bytes
 */
size_t get_file_size(const std::string& filepath);

/**
 * @brief Get file modification time
 * @param filepath File path
 * @return time_t Modification time
 */
time_t get_file_mtime(const std::string& filepath);

/**
 * @brief Check if path is a symbolic link
 * @param path File path
 * @return bool True if symbolic link
 */
bool is_symbolic_link(const std::string& path);

/**
 * @brief Resolve symbolic link
 * @param path Symbolic link path
 * @return std::string Resolved path
 */
std::string resolve_symbolic_link(const std::string& path);

/**
 * @brief Get real path (resolving all symbolic links)
 * @param path File path
 * @return std::string Real path
 */
std::string get_real_path(const std::string& path);

/**
 * @brief Check if path is on a network filesystem
 * @param path File path
 * @return bool True if on network filesystem
 */
bool is_network_path(const std::string& path);

/**
 * @brief Get mounted filesystems
 * @return std::vector<std::string> Mounted filesystem paths
 */
std::vector<std::string> get_mounted_filesystems();

/**
 * @brief Get temporary directory path
 * @return std::string Temporary directory path
 */
std::string get_temp_directory();

/**
 * @brief Create unique temporary file
 * @param prefix File name prefix
 * @param extension File extension
 * @return std::string Temporary file path
 */
std::string create_temp_file(const std::string& prefix = "omop_", 
                           const std::string& extension = ".tmp");

/**
 * @brief Set file permissions
 * @param filepath File path
 * @param mode Permission mode (e.g., 0644)
 * @return bool True if successful
 */
bool set_file_permissions(const std::string& filepath, mode_t mode);

/**
 * @brief Get file permissions
 * @param filepath File path
 * @return mode_t Permission mode
 */
mode_t get_file_permissions(const std::string& filepath);

/**
 * @brief High-resolution timer using clock_gettime
 */
class UnixHighResTimer {
public:
    /**
     * @brief Constructor - starts timer
     */
    UnixHighResTimer();
    
    /**
     * @brief Reset timer
     */
    void reset();
    
    /**
     * @brief Get elapsed time in seconds
     * @return double Elapsed time
     */
    double elapsed_seconds() const;
    
    /**
     * @brief Get elapsed time in milliseconds
     * @return double Elapsed time
     */
    double elapsed_milliseconds() const;
    
private:
    struct timespec start_time_;
};

/**
 * @brief Get system memory information
 */
struct MemoryInfo {
    size_t total_physical;     ///< Total physical memory
    size_t available_physical; ///< Available physical memory
    size_t total_swap;         ///< Total swap space
    size_t available_swap;     ///< Available swap space
};

/**
 * @brief Get system memory information
 * @return MemoryInfo Memory statistics
 */
MemoryInfo get_memory_info();

/**
 * @brief Set process priority (nice value)
 * @param priority Nice value (-20 to 19)
 * @return bool True if successful
 */
bool set_process_priority(int priority);

/**
 * @brief Get current process priority
 * @return int Nice value
 */
int get_process_priority();

/**
 * @brief Lock memory pages to prevent swapping
 * @param addr Memory address
 * @param size Memory size
 * @return bool True if successful
 */
bool lock_memory(void* addr, size_t size);

/**
 * @brief Unlock memory pages
 * @param addr Memory address
 * @param size Memory size
 * @return bool True if successful
 */
bool unlock_memory(void* addr, size_t size);

/**
 * @brief Advise kernel about memory usage pattern
 * @param addr Memory address
 * @param size Memory size
 * @param advice Advice flag (e.g., MADV_SEQUENTIAL)
 * @return bool True if successful
 */
bool advise_memory_usage(void* addr, size_t size, int advice);

/**
 * @brief Get number of CPU cores
 * @return size_t Number of CPU cores
 */
size_t get_cpu_count();

/**
 * @brief Set CPU affinity for current thread
 * @param cpu_set CPU set mask
 * @return bool True if successful
 */
bool set_thread_affinity(const std::vector<int>& cpu_set);

} // namespace omop::extract::platform

#endif // !_WIN32

File src/lib/extract/platform/windows_utils.h:

/**
 * @file windows_utils.h
 * @brief Windows-specific utility functions for the extract module
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains Windows-specific implementations for file handling,
 * path operations, and system interactions.
 */

#pragma once

#ifdef _WIN32

#include <string>
#include <vector>
#include <memory>
#include <windows.h>

namespace omop::extract::platform {

/**
 * @brief Windows file handle wrapper with RAII
 */
class WindowsFileHandle {
public:
    /**
     * @brief Constructor
     * @param handle Windows file handle
     */
    explicit WindowsFileHandle(HANDLE handle = INVALID_HANDLE_VALUE) : handle_(handle) {}
    
    /**
     * @brief Destructor
     */
    ~WindowsFileHandle() {
        if (handle_ != INVALID_HANDLE_VALUE) {
            CloseHandle(handle_);
        }
    }
    
    // Delete copy operations
    WindowsFileHandle(const WindowsFileHandle&) = delete;
    WindowsFileHandle& operator=(const WindowsFileHandle&) = delete;
    
    // Move operations
    WindowsFileHandle(WindowsFileHandle&& other) noexcept : handle_(other.handle_) {
        other.handle_ = INVALID_HANDLE_VALUE;
    }
    
    WindowsFileHandle& operator=(WindowsFileHandle&& other) noexcept {
        if (this != &other) {
            if (handle_ != INVALID_HANDLE_VALUE) {
                CloseHandle(handle_);
            }
            handle_ = other.handle_;
            other.handle_ = INVALID_HANDLE_VALUE;
        }
        return *this;
    }
    
    /**
     * @brief Get raw handle
     * @return HANDLE Windows file handle
     */
    HANDLE get() const { return handle_; }
    
    /**
     * @brief Check if handle is valid
     * @return bool True if valid
     */
    bool is_valid() const { return handle_ != INVALID_HANDLE_VALUE; }
    
private:
    HANDLE handle_;
};

/**
 * @brief Convert UTF-8 string to wide string
 * @param utf8_str UTF-8 encoded string
 * @return std::wstring Wide string
 */
std::wstring utf8_to_wide(const std::string& utf8_str);

/**
 * @brief Convert wide string to UTF-8 string
 * @param wide_str Wide string
 * @return std::string UTF-8 encoded string
 */
std::string wide_to_utf8(const std::wstring& wide_str);

/**
 * @brief Get Windows error message
 * @param error_code Windows error code (default = GetLastError())
 * @return std::string Error message
 */
std::string get_windows_error_message(DWORD error_code = 0);

/**
 * @brief Create a memory-mapped file for reading
 * @param filepath File path
 * @return std::pair<WindowsFileHandle, WindowsFileHandle> File and mapping handles
 */
std::pair<WindowsFileHandle, WindowsFileHandle> create_file_mapping(const std::string& filepath);

/**
 * @brief Map file view into memory
 * @param mapping_handle Mapping handle
 * @param offset File offset
 * @param size Size to map (0 = entire file)
 * @return void* Mapped memory pointer
 */
void* map_view_of_file(HANDLE mapping_handle, size_t offset = 0, size_t size = 0);

/**
 * @brief Unmap file view from memory
 * @param view Mapped memory pointer
 * @return bool True if successful
 */
bool unmap_view_of_file(void* view);

/**
 * @brief Get file size
 * @param filepath File path
 * @return size_t File size in bytes
 */
size_t get_file_size(const std::string& filepath);

/**
 * @brief Check if path is a network path
 * @param path File path
 * @return bool True if network path
 */
bool is_network_path(const std::string& path);

/**
 * @brief Get available drive letters
 * @return std::vector<char> Available drive letters
 */
std::vector<char> get_available_drives();

/**
 * @brief Get temporary directory path
 * @return std::string Temporary directory path
 */
std::string get_temp_directory();

/**
 * @brief Create unique temporary file
 * @param prefix File name prefix
 * @param extension File extension
 * @return std::string Temporary file path
 */
std::string create_temp_file(const std::string& prefix = "omop_", 
                           const std::string& extension = ".tmp");

/**
 * @brief Set file attributes
 * @param filepath File path
 * @param attributes File attributes (FILE_ATTRIBUTE_*)
 * @return bool True if successful
 */
bool set_file_attributes(const std::string& filepath, DWORD attributes);

/**
 * @brief Get file attributes
 * @param filepath File path
 * @return DWORD File attributes (INVALID_FILE_ATTRIBUTES on error)
 */
DWORD get_file_attributes(const std::string& filepath);

/**
 * @brief High-resolution timer using Windows performance counter
 */
class WindowsHighResTimer {
public:
    /**
     * @brief Constructor - starts timer
     */
    WindowsHighResTimer();
    
    /**
     * @brief Reset timer
     */
    void reset();
    
    /**
     * @brief Get elapsed time in seconds
     * @return double Elapsed time
     */
    double elapsed_seconds() const;
    
    /**
     * @brief Get elapsed time in milliseconds
     * @return double Elapsed time
     */
    double elapsed_milliseconds() const;
    
private:
    LARGE_INTEGER start_time_;
    LARGE_INTEGER frequency_;
};

/**
 * @brief Get system memory information
 */
struct MemoryInfo {
    size_t total_physical;     ///< Total physical memory
    size_t available_physical; ///< Available physical memory
    size_t total_virtual;      ///< Total virtual memory
    size_t available_virtual;  ///< Available virtual memory
};

/**
 * @brief Get system memory information
 * @return MemoryInfo Memory statistics
 */
MemoryInfo get_memory_info();

/**
 * @brief Set process priority
 * @param priority Priority class (e.g., NORMAL_PRIORITY_CLASS)
 * @return bool True if successful
 */
bool set_process_priority(DWORD priority);

/**
 * @brief Enable large page support for process
 * @return bool True if successful
 */
bool enable_large_pages();

} // namespace omop::extract::platform

#endif // _WIN32

File src/lib/extract/extract.h:

/**
 * @file extract.h
 * @brief Comprehensive header for the OMOP ETL extract module
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header provides convenient access to all extraction functionality
 * within the OMOP ETL pipeline, including various data source extractors
 * and supporting utilities. It serves as the main entry point for data
 * extraction operations.
 * 
 * @section design Design Principles
 * - Modularity: Separate extractors for different data sources
 * - Extensibility: Easy to add new extractor types
 * - Performance: Efficient data extraction with batching
 * - Error Handling: Robust error management
 * - Progress Tracking: Real-time extraction monitoring
 * 
 * @section components Components
 * - Core Interfaces: Base extraction interfaces
 * - File Extractors: CSV and JSON data extraction
 * - Database Extractors: PostgreSQL, MySQL, and ODBC support
 * - Batch Processing: Efficient record batch handling
 * - Parallel Extraction: Multi-threaded data extraction
 * 
 * @section usage Usage
 * To use the extraction module:
 * 1. Create appropriate extractor
 * 2. Configure extraction parameters
 * 3. Execute extraction process
 * 4. Process extracted records
 * 
 * @section example Example
 * @code
 * auto extractor = create_extractor_auto("data.csv");
 * BatchExtractor batch_extractor(std::move(extractor));
 * auto records = batch_extractor.extract_all();
 * @endcode
 */

#pragma once

// Core interfaces
#include "core/interfaces.h"

// Base extractor functionality
#include "extract/extractor_base.h"
#include "extract/extractor_factory.h"

// File-based extractors
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"

// Database extractors
#include "extract/database_connector.h"
#include "extract/postgresql_connector.h"
#ifdef OMOP_HAS_MYSQL
#include "extract/mysql_connector.h"
#endif
#ifdef OMOP_HAS_ODBC
#include "extract/odbc_connector.h"
#endif

// Utilities
#include "common/exceptions.h"
#include "common/logging.h"

/**
 * @namespace omop::extract
 * @brief Data extraction functionality for the OMOP ETL pipeline
 * 
 * @section description Description
 * The extract namespace contains all components responsible for extracting
 * data from various sources including files (CSV, JSON) and databases
 * (PostgreSQL, MySQL, ODBC). The module provides a unified interface for
 * data extraction with support for batch processing, error handling, and
 * progress monitoring.
 * 
 * @section features Features
 * - Multiple data source support
 * - Batch processing capabilities
 * - Parallel extraction
 * - Progress monitoring
 * - Error handling
 * - Configuration validation
 */
namespace omop::extract {

/**
 * @brief Simplified extractor creation with automatic type detection
 * @param source_path Path to data source (file or connection string)
 * @param config Additional configuration parameters
 * @return std::unique_ptr<core::IExtractor> Configured extractor instance
 * @throws ConfigurationException if source type cannot be determined
 * 
 * @section details Implementation Details
 * - Detects source type automatically
 * - Creates appropriate extractor
 * - Applies configuration
 * - Thread-safe
 */
std::unique_ptr<core::IExtractor> create_extractor_auto(
    const std::string& source_path,
    const std::unordered_map<std::string, std::any>& config = {});

/**
 * @brief Extract data source type from path or configuration
 * @param source_path Path to data source
 * @return std::string Extractor type identifier
 * 
 * @section details Implementation Details
 * - Analyzes path/configuration
 * - Determines source type
 * - Thread-safe
 */
std::string detect_source_type(const std::string& source_path);

/**
 * @brief Validate extractor configuration
 * @param type Extractor type
 * @param config Configuration parameters
 * @return std::pair<bool, std::string> Validation result and error message
 * 
 * @section details Implementation Details
 * - Validates parameters
 * - Checks compatibility
 * - Thread-safe
 */
std::pair<bool, std::string> validate_extractor_config(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config);

/**
 * @brief Convenience class for batch extraction operations
 * 
 * @section description Description
 * This class provides high-level functionality for extracting data
 * from various sources with automatic error handling and progress tracking.
 * 
 * @section features Features
 * - Batch processing
 * - Progress monitoring
 * - Error handling
 * - Statistics tracking
 * - Callback support
 */
class BatchExtractor {
public:
    /**
     * @brief Configuration for batch extraction
     * 
     * @section description Description
     * Defines parameters for controlling batch extraction behavior.
     * 
     * @section fields Fields
     * - batch_size: Number of records per batch
     * - max_records: Maximum records to extract (0 = unlimited)
     * - continue_on_error: Whether to continue on errors
     * - progress_callback: Progress reporting function
     * - error_callback: Error handling function
     */
    struct Config {
        size_t batch_size = 10000;  ///< Records per batch
        size_t max_records = 0;     ///< Maximum records (0 = unlimited)
        bool continue_on_error = true;  ///< Continue on errors
        std::function<void(size_t, size_t)> progress_callback;  ///< Progress callback
        std::function<void(const std::string&)> error_callback;  ///< Error callback
    };

    /**
     * @brief Constructor with default config
     * @param extractor Extractor instance
     * 
     * @section details Implementation Details
     * - Takes ownership of extractor
     * - Initializes with defaults
     * - Thread-safe
     */
    explicit BatchExtractor(std::unique_ptr<core::IExtractor> extractor);

    /**
     * @brief Constructor with custom config
     * @param extractor Extractor instance
     * @param config Batch extraction configuration
     * 
     * @section details Implementation Details
     * - Takes ownership of extractor
     * - Applies configuration
     * - Thread-safe
     */
    BatchExtractor(std::unique_ptr<core::IExtractor> extractor,
                  const Config& config);

    /**
     * @brief Extract all records
     * @return std::vector<core::Record> All extracted records
     * 
     * @section details Implementation Details
     * - Processes in batches
     * - Handles errors
     * - Reports progress
     * - Thread-safe
     */
    std::vector<core::Record> extract_all();

    /**
     * @brief Extract records with callback processing
     * @param processor Callback function for processing each batch
     * @return size_t Total number of records processed
     * 
     * @section details Implementation Details
     * - Streams batches to callback
     * - Handles errors
     * - Reports progress
     * - Thread-safe
     */
    size_t extract_with_callback(
        std::function<void(const core::RecordBatch&)> processor);

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     * 
     * @section details Implementation Details
     * - Collects metrics
     * - Thread-safe
     */
    std::unordered_map<std::string, std::any> get_statistics() const;

private:
    std::unique_ptr<core::IExtractor> extractor_;  ///< Extractor instance
    Config config_;  ///< Extraction configuration
    core::ProcessingContext context_;  ///< Processing context
};

/**
 * @brief Parallel extraction coordinator
 * 
 * @section description Description
 * This class manages parallel extraction from multiple sources,
 * coordinating thread pools and aggregating results.
 * 
 * @section features Features
 * - Multi-threaded extraction
 * - Result aggregation
 * - Order preservation
 * - Progress tracking
 * - Error handling
 */
class ParallelExtractor {
public:
    /**
     * @brief Configuration for parallel extraction
     * 
     * @section description Description
     * Defines parameters for controlling parallel extraction behavior.
     * 
     * @section fields Fields
     * - num_threads: Number of worker threads
     * - queue_size: Size of result queue
     * - preserve_order: Whether to preserve record order
     */
    struct Config {
        size_t num_threads = 4;  ///< Worker thread count
        size_t queue_size = 100;  ///< Result queue size
        bool preserve_order = false;  ///< Preserve record order
    };

    /**
     * @brief Constructor
     * @param config Parallel extraction configuration
     * 
     * @section details Implementation Details
     * - Initializes thread pool
     * - Sets up queues
     * - Thread-safe
     */
    explicit ParallelExtractor(const Config& config);

    /**
     * @brief Add extractor to parallel processing
     * @param extractor Extractor instance
     * @param name Optional name for the extractor
     * 
     * @section details Implementation Details
     * - Takes ownership
     * - Assigns worker
     * - Thread-safe
     */
    void add_extractor(std::unique_ptr<core::IExtractor> extractor,
                      const std::string& name = "");

    /**
     * @brief Extract records with streaming callback
     * @param processor Callback for processing batches
     * 
     * @section details Implementation Details
     * - Coordinates workers
     * - Streams results
     * - Handles errors
     * - Thread-safe
     */
    void extract_streaming(
        std::function<void(const core::RecordBatch&, const std::string&)> processor);

private:
    Config config_;  ///< Parallel extraction configuration
    std::vector<std::unique_ptr<core::IExtractor>> extractors_;  ///< Extractor instances
    std::vector<std::string> extractor_names_;  ///< Extractor names
};

/**
 * @brief Utility functions for common extraction patterns
 * 
 * @section description Description
 * Provides convenience functions for common data extraction scenarios.
 * 
 * @section features Features
 * - File extraction
 * - Database extraction
 * - Connection management
 * - Error handling
 */
namespace utils {

/**
 * @brief Extract records from CSV file
 * @param filepath Path to CSV file
 * @param options Additional CSV options
 * @return std::vector<core::Record> Extracted records
 * 
 * @section details Implementation Details
 * - Creates CSV extractor
 * - Processes file
 * - Handles errors
 * - Thread-safe
 */
std::vector<core::Record> extract_csv(
    const std::string& filepath,
    const CsvOptions& options = {});

/**
 * @brief Extract records from JSON file
 * @param filepath Path to JSON file
 * @param options Additional JSON options
 * @return std::vector<core::Record> Extracted records
 * 
 * @section details Implementation Details
 * - Creates JSON extractor
 * - Processes file
 * - Handles errors
 * - Thread-safe
 */
std::vector<core::Record> extract_json(
    const std::string& filepath,
    const JsonOptions& options = {});

/**
 * @brief Extract records from database table
 * @param connection Database connection
 * @param table_name Table name
 * @param filter Optional WHERE clause
 * @return std::vector<core::Record> Extracted records
 * 
 * @section details Implementation Details
 * - Creates table extractor
 * - Executes query
 * - Handles errors
 * - Thread-safe
 */
std::vector<core::Record> extract_table(
    std::unique_ptr<IDatabaseConnection> connection,
    const std::string& table_name,
    const std::string& filter = "");

/**
 * @brief Create database connection from URL
 * @param url Database URL (e.g., "postgresql://user:pass@host:port/db")
 * @return std::unique_ptr<IDatabaseConnection> Database connection
 * 
 * @section details Implementation Details
 * - Parses URL
 * - Creates connection
 * - Handles errors
 * - Thread-safe
 */
std::unique_ptr<IDatabaseConnection> create_connection_from_url(
    const std::string& url);

} // namespace utils

} // namespace omop::extract

File src/lib/extract/database_connector.h:

/**
 * @file database_connector.h
 * @brief Database connectivity and query execution for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header defines the database connectivity layer for the OMOP ETL pipeline,
 * providing interfaces and implementations for database operations. It supports
 * multiple database systems through a unified API.
 * 
 * @section design Design Principles
 * - Database Agnostic: Unified interface for different databases
 * - Resource Management: Connection pooling and cleanup
 * - Error Handling: Robust error management
 * - Performance: Efficient query execution
 * - Thread Safety: Safe concurrent access
 * 
 * @section components Components
 * - IResultSet: Query result access interface
 * - IPreparedStatement: Parameterized query interface
 * - IDatabaseConnection: Database connection interface
 * - ResultSetBase: Common result set functionality
 * - DatabaseExtractor: Database-specific data extraction
 * - ConnectionPool: Connection pooling management
 * 
 * @section usage Usage
 * To use the database connectivity:
 * 1. Create database connection
 * 2. Execute queries or prepare statements
 * 3. Process results
 * 4. Manage transactions
 * 
 * @section example Example
 * @code
 * auto connection = DatabaseConnectionFactory::instance()
 *     .create_connection("postgresql", params);
 * connection->connect();
 * auto result = connection->execute_query("SELECT * FROM patients");
 * while (result->next()) {
 *     auto record = result->to_record();
 *     // Process record
 * }
 * @endcode
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <optional>
#include <any>
#include <functional>
#include <unordered_map>
#include "core/interfaces.h"
#include "common/exceptions.h"

namespace omop::extract {

/**
 * @brief Result set interface for database queries
 * 
 * @section description Description
 * This interface provides access to query results in a database-agnostic manner.
 * Implementations handle the specifics of each database system.
 * 
 * @section features Features
 * - Row navigation
 * - Column access
 * - NULL handling
 * - Type conversion
 * - Record conversion
 */
class IResultSet {
public:
    /**
     * @brief Virtual destructor
     * 
     * @section details Implementation Details
     * - Ensures proper cleanup
     * - Thread-safe
     */
    virtual ~IResultSet() = default;

    /**
     * @brief Move to next row
     * @return bool True if successful, false if no more rows
     * 
     * @section details Implementation Details
     * - Advances cursor
     * - Thread-safe
     */
    virtual bool next() = 0;

    /**
     * @brief Get column value by index
     * @param index Column index (0-based)
     * @return std::any Column value
     * 
     * @section details Implementation Details
     * - Retrieves value
     * - Converts type
     * - Thread-safe
     */
    virtual std::any get_value(size_t index) const = 0;

    /**
     * @brief Get column value by name
     * @param column_name Column name
     * @return std::any Column value
     * 
     * @section details Implementation Details
     * - Looks up column
     * - Retrieves value
     * - Thread-safe
     */
    virtual std::any get_value(const std::string& column_name) const = 0;

    /**
     * @brief Check if column value is NULL
     * @param index Column index
     * @return bool True if NULL
     * 
     * @section details Implementation Details
     * - Checks nullity
     * - Thread-safe
     */
    virtual bool is_null(size_t index) const = 0;

    /**
     * @brief Check if column value is NULL
     * @param column_name Column name
     * @return bool True if NULL
     * 
     * @section details Implementation Details
     * - Looks up column
     * - Checks nullity
     * - Thread-safe
     */
    virtual bool is_null(const std::string& column_name) const = 0;

    /**
     * @brief Get column count
     * @return size_t Number of columns
     * 
     * @section details Implementation Details
     * - Returns count
     * - Thread-safe
     */
    virtual size_t column_count() const = 0;

    /**
     * @brief Get column name by index
     * @param index Column index
     * @return std::string Column name
     * 
     * @section details Implementation Details
     * - Returns name
     * - Thread-safe
     */
    virtual std::string column_name(size_t index) const = 0;

    /**
     * @brief Get column type by index
     * @param index Column index
     * @return std::string Column type name
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    virtual std::string column_type(size_t index) const = 0;

    /**
     * @brief Convert current row to Record
     * @return core::Record Record representation of current row
     * 
     * @section details Implementation Details
     * - Converts row
     * - Handles types
     * - Thread-safe
     */
    virtual core::Record to_record() const = 0;
};

/**
 * @brief Prepared statement interface
 * 
 * @section description Description
 * This interface provides parameterized query execution capabilities
 * for safe and efficient database operations.
 * 
 * @section features Features
 * - Parameter binding
 * - Query execution
 * - Update operations
 * - Parameter clearing
 */
class IPreparedStatement {
public:
    /**
     * @brief Virtual destructor
     * 
     * @section details Implementation Details
     * - Ensures cleanup
     * - Thread-safe
     */
    virtual ~IPreparedStatement() = default;

    /**
     * @brief Bind parameter by index
     * @param index Parameter index (1-based)
     * @param value Parameter value
     * 
     * @section details Implementation Details
     * - Binds value
     * - Converts type
     * - Thread-safe
     */
    virtual void bind(size_t index, const std::any& value) = 0;

    /**
     * @brief Execute query and return result set
     * @return std::unique_ptr<IResultSet> Query results
     * 
     * @section details Implementation Details
     * - Executes query
     * - Returns results
     * - Thread-safe
     */
    virtual std::unique_ptr<IResultSet> execute_query() = 0;

    /**
     * @brief Execute update/insert/delete
     * @return size_t Number of affected rows
     * 
     * @section details Implementation Details
     * - Executes update
     * - Returns count
     * - Thread-safe
     */
    virtual size_t execute_update() = 0;

    /**
     * @brief Clear all bound parameters
     * 
     * @section details Implementation Details
     * - Clears parameters
     * - Thread-safe
     */
    virtual void clear_parameters() = 0;
};

/**
 * @brief Database connection interface
 * 
 * @section description Description
 * This interface defines the contract for database connections,
 * providing a unified API for different database systems.
 * 
 * @section features Features
 * - Connection management
 * - Query execution
 * - Transaction control
 * - Metadata access
 * - Timeout handling
 */
class IDatabaseConnection {
public:
    /**
     * @brief Connection parameters
     * 
     * @section description Description
     * Defines the parameters required for database connection.
     * 
     * @section fields Fields
     * - host: Server hostname
     * - port: Server port
     * - database: Database name
     * - username: User credentials
     * - password: User credentials
     * - options: Additional options
     */
    struct ConnectionParams {
        std::string host;  ///< Server hostname
        int port;         ///< Server port
        std::string database;  ///< Database name
        std::string username;  ///< User credentials
        std::string password;  ///< User credentials
        std::unordered_map<std::string, std::string> options;  ///< Additional options
    };

    /**
     * @brief Virtual destructor
     * 
     * @section details Implementation Details
     * - Ensures cleanup
     * - Thread-safe
     */
    virtual ~IDatabaseConnection() = default;

    /**
     * @brief Connect to database
     * @param params Connection parameters
     * 
     * @section details Implementation Details
     * - Establishes connection
     * - Validates parameters
     * - Thread-safe
     */
    virtual void connect(const ConnectionParams& params) = 0;

    /**
     * @brief Disconnect from database
     * 
     * @section details Implementation Details
     * - Closes connection
     * - Cleans up resources
     * - Thread-safe
     */
    virtual void disconnect() = 0;

    /**
     * @brief Check if connected
     * @return bool True if connected
     * 
     * @section details Implementation Details
     * - Checks state
     * - Thread-safe
     */
    virtual bool is_connected() const = 0;

    /**
     * @brief Execute SQL query
     * @param sql SQL query string
     * @return std::unique_ptr<IResultSet> Query results
     * 
     * @section details Implementation Details
     * - Executes query
     * - Returns results
     * - Thread-safe
     */
    virtual std::unique_ptr<IResultSet> execute_query(const std::string& sql) = 0;

    /**
     * @brief Execute SQL update/insert/delete
     * @param sql SQL statement
     * @return size_t Number of affected rows
     * 
     * @section details Implementation Details
     * - Executes update
     * - Returns count
     * - Thread-safe
     */
    virtual size_t execute_update(const std::string& sql) = 0;

    /**
     * @brief Prepare SQL statement
     * @param sql SQL statement with parameter placeholders
     * @return std::unique_ptr<IPreparedStatement> Prepared statement
     * 
     * @section details Implementation Details
     * - Prepares statement
     * - Returns handle
     * - Thread-safe
     */
    virtual std::unique_ptr<IPreparedStatement> prepare_statement(const std::string& sql) = 0;

    /**
     * @brief Begin transaction
     * 
     * @section details Implementation Details
     * - Starts transaction
     * - Thread-safe
     */
    virtual void begin_transaction() = 0;

    /**
     * @brief Commit transaction
     * 
     * @section details Implementation Details
     * - Commits changes
     * - Thread-safe
     */
    virtual void commit() = 0;

    /**
     * @brief Rollback transaction
     * 
     * @section details Implementation Details
     * - Reverts changes
     * - Thread-safe
     */
    virtual void rollback() = 0;

    /**
     * @brief Get database type name
     * @return std::string Database type
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    virtual std::string get_database_type() const = 0;

    /**
     * @brief Get database version
     * @return std::string Database version string
     * 
     * @section details Implementation Details
     * - Returns version
     * - Thread-safe
     */
    virtual std::string get_version() const = 0;

    /**
     * @brief Set query timeout
     * @param seconds Timeout in seconds
     * 
     * @section details Implementation Details
     * - Sets timeout
     * - Thread-safe
     */
    virtual void set_query_timeout(int seconds) = 0;

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return bool True if table exists
     * 
     * @section details Implementation Details
     * - Checks existence
     * - Thread-safe
     */
    virtual bool table_exists(const std::string& table_name,
                            const std::string& schema = "") const = 0;
};

/**
 * @brief Base implementation of IResultSet with common functionality
 * 
 * @section description Description
 * Provides a base implementation of the IResultSet interface with
 * common functionality for converting rows to records.
 * 
 * @section features Features
 * - Record conversion
 * - NULL handling
 * - Type conversion
 */
class ResultSetBase : public IResultSet {
public:
    /**
     * @brief Convert current row to Record
     * @return core::Record Record representation
     * 
     * @section details Implementation Details
     * - Converts row
     * - Handles NULLs
     * - Thread-safe
     */
    core::Record to_record() const override {
        core::Record record;

        for (size_t i = 0; i < column_count(); ++i) {
            if (!is_null(i)) {
                std::string col_name = column_name(i);
                record.setField(col_name, get_value(i));
            }
        }

        return record;
    }
};

/**
 * @brief Database-specific data extractor
 * 
 * @section description Description
 * Extracts data from database tables using the database connection
 * interface.
 * 
 * @section features Features
 * - Table extraction
 * - Filter support
 * - Batch processing
 * - Error handling
 */
class DatabaseExtractor : public core::IExtractor {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     * 
     * @section details Implementation Details
     * - Takes ownership
     * - Initializes state
     * - Thread-safe
     */
    explicit DatabaseExtractor(std::unique_ptr<IDatabaseConnection> connection);

    /**
     * @brief Initialize extractor
     * @param config Configuration
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Validates config
     * - Sets up state
     * - Thread-safe
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract batch of records
     * @param batch_size Batch size
     * @param context Processing context
     * @return Record batch
     * 
     * @section details Implementation Details
     * - Executes query
     * - Processes results
     * - Thread-safe
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                  core::ProcessingContext& context) override;

    /**
     * @brief Check for more data
     * @return bool True if more data
     * 
     * @section details Implementation Details
     * - Checks state
     * - Thread-safe
     */
    bool has_more_data() const override { return has_more_data_; }

    /**
     * @brief Get extractor type
     * @return Type name
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override;

    /**
     * @brief Finalize extraction
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Cleans up
     * - Thread-safe
     */
    void finalize(core::ProcessingContext& context) override;

protected:
    /**
     * @brief Build extraction query
     * @return SQL query string
     *
     * @section details Implementation Details
     * - Builds database-specific query
     * - Can be overridden by derived classes
     */
    virtual std::string build_query() const;

    /**
     * @brief Apply filters to query
     * @param base_query Base query
     * @return Modified query
     *
     * @section details Implementation Details
     * - Applies filters
     * - Thread-safe
     */
    virtual std::string apply_filters(const std::string& base_query) const;

private:
    std::unique_ptr<IDatabaseConnection> connection_;  ///< Database connection
    std::string table_name_;  ///< Target table
    std::string schema_name_;  ///< Schema name
    bool has_more_data_{true};  ///< More data flag
    size_t current_offset_{0};  ///< Current offset
};

/**
 * @brief Connection pool for database connections
 * 
 * @section description Description
 * Manages a pool of database connections for efficient reuse.
 * 
 * @section features Features
 * - Connection pooling
 * - Resource management
 * - Statistics tracking
 * - Connection validation
 */
class ConnectionPool {
public:
    /**
     * @brief Constructor
     * @param min_size Minimum pool size
     * @param max_size Maximum pool size
     * @param timeout Connection timeout
     * 
     * @section details Implementation Details
     * - Initializes pool
     * - Thread-safe
     */
    ConnectionPool(size_t min_size, size_t max_size, std::chrono::seconds timeout);

    /**
     * @brief Virtual destructor
     * 
     * @section details Implementation Details
     * - Cleans up
     * - Thread-safe
     */
    virtual ~ConnectionPool();

    /**
     * @brief Acquire connection
     * @return Connection handle
     * 
     * @section details Implementation Details
     * - Gets connection
     * - Thread-safe
     */
    std::unique_ptr<IDatabaseConnection> acquire();

    /**
     * @brief Release connection
     * @param connection Connection to release
     * 
     * @section details Implementation Details
     * - Returns connection
     * - Thread-safe
     */
    void release(std::unique_ptr<IDatabaseConnection> connection);

    /**
     * @brief Pool statistics
     * 
     * @section description Description
     * Tracks pool usage and performance metrics.
     * 
     * @section fields Fields
     * - total_connections: Total connections
     * - active_connections: Active connections
     * - idle_connections: Idle connections
     * - total_acquisitions: Total acquisitions
     * - total_releases: Total releases
     * - wait_count: Wait count
     * - avg_wait_time: Average wait time
     */
    struct PoolStats {
        size_t total_connections;  ///< Total connections
        size_t active_connections;  ///< Active connections
        size_t idle_connections;  ///< Idle connections
        size_t total_acquisitions;  ///< Total acquisitions
        size_t total_releases;  ///< Total releases
        size_t wait_count;  ///< Wait count
        std::chrono::milliseconds avg_wait_time;  ///< Average wait time
    };

    /**
     * @brief Get pool statistics
     * @return Pool statistics
     * 
     * @section details Implementation Details
     * - Collects stats
     * - Thread-safe
     */
    [[nodiscard]] PoolStats get_statistics() const;

    /**
     * @brief Clear idle connections
     * 
     * @section details Implementation Details
     * - Removes idle
     * - Thread-safe
     */
    void clear_idle_connections();

    /**
     * @brief Validate connections
     * @return Number of invalid connections
     * 
     * @section details Implementation Details
     * - Validates connections
     * - Removes invalid
     * - Thread-safe
     */
    size_t validate_connections();

private:
    class Impl;  ///< Implementation details
    std::unique_ptr<Impl> impl_;  ///< Implementation instance
};

/**
 * @brief Factory for database connections
 * 
 * @section description Description
 * Creates database connections for different database types.
 * 
 * @section features Features
 * - Connection creation
 * - Type registration
 * - Singleton access
 */
class DatabaseConnectionFactory {
public:
    /**
     * @brief Creator function type
     */
    using Creator = std::function<std::unique_ptr<IDatabaseConnection>(
        const IDatabaseConnection::ConnectionParams&)>;

    /**
     * @brief Get factory instance
     * @return Factory instance
     *
     * @section details Implementation Details
     * - Returns singleton
     * - Thread-safe
     */
    static DatabaseConnectionFactory& instance() {
        static DatabaseConnectionFactory instance;
        return instance;
    }

    /**
     * @brief Register connection type
     * @param type Connection type
     * @param creator Creator function
     * 
     * @section details Implementation Details
     * - Registers type
     * - Thread-safe
     */
    void register_type(const std::string& type, Creator creator) {
        creators_[type] = std::move(creator);
    }

    /**
     * @brief Create connection by type
     * @param type Database type
     * @param params Connection parameters
     * @return std::unique_ptr<IDatabaseConnection> Database connection
     */
    [[nodiscard]] std::unique_ptr<IDatabaseConnection> create(const std::string& type,
                                                             const IDatabaseConnection::ConnectionParams& params) {
        auto it = creators_.find(type);
        if (it != creators_.end()) {
            return it->second(params);
        }
        throw common::DatabaseException(
            std::format("Unknown database type: '{}'", type), type, 0);
    }

    /**
     * @brief Create connection from configuration
     * @param config Database configuration
     * @return std::unique_ptr<IDatabaseConnection> Database connection
     */
    [[nodiscard]] std::unique_ptr<IDatabaseConnection> create_from_config(
        const std::unordered_map<std::string, std::any>& config);

private:
    std::unordered_map<std::string, Creator> creators_;  ///< Type creators
};

} // namespace omop::extract

File src/lib/extract/json_extractor.h:

#pragma once

#include "extract/database_connector.h"
#include "core/interfaces.h"
#include <nlohmann/json.hpp>
#include <fstream>
#include <filesystem>
#include <queue>
#include <stack>
#include <thread>
#include <condition_variable>

namespace omop::extract {

using json = nlohmann::json;

/**
 * @brief JSON parsing options
 */
struct JsonOptions {
    std::string root_path;              ///< JSON path to data array (e.g., "data.patients")
    bool flatten_nested{true};          ///< Flatten nested objects
    std::string array_delimiter{"_"};   ///< Delimiter for flattened array indices
    bool parse_dates{true};             ///< Automatically parse date strings
    std::vector<std::string> date_formats{
        "%Y-%m-%d",
        "%Y-%m-%dT%H:%M:%S",
        "%Y-%m-%d %H:%M:%S"
    };
    bool ignore_null{true};             ///< Skip null values
    size_t max_depth{10};               ///< Maximum nesting depth
};

/**
 * @brief JSON extractor for single JSON files
 *
 * Extracts data from JSON files, supporting both array and object formats,
 * with automatic flattening of nested structures.
 */
class JsonExtractor : public core::IExtractor {
public:
    /**
     * @brief Constructor
     */
    JsonExtractor() = default;

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "json"; }

    /**
     * @brief Finalize extraction
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Load JSON file
     * @param filepath File path
     */
    void load_file(const std::string& filepath);

    /**
     * @brief Navigate to data array using JSON path
     * @param root Root JSON object
     * @param path Dot-separated path (e.g., "data.patients")
     * @return json::const_iterator Iterator to array
     */
    json::const_iterator navigate_to_data(const json& root, const std::string& path);

public:
    /**
     * @brief Flatten JSON object to record
     * @param obj JSON object
     * @param prefix Field name prefix
     * @param depth Current nesting depth
     * @return core::Record Flattened record
     */
    core::Record flatten_json_object(const json& obj,
                                    const std::string& prefix = "",
                                    size_t depth = 0);

protected:

    /**
     * @brief Convert JSON value to std::any
     * @param value JSON value
     * @return std::any Converted value
     */
    std::any json_to_any(const json& value);

    /**
     * @brief Parse date string
     * @param date_str Date string
     * @return std::optional<std::chrono::system_clock::time_point> Parsed date
     */
    std::optional<std::chrono::system_clock::time_point> parse_date(const std::string& date_str);

public:
    JsonOptions options_;

protected:
    std::string filepath_;
    json json_data_;
    json::const_iterator current_iterator_;
    json::const_iterator end_iterator_;
    size_t total_records_{0};
    size_t extracted_count_{0};
    bool data_loaded_{false};
    std::chrono::steady_clock::time_point start_time_;
};

/**
 * @brief JSON Lines (JSONL) extractor
 *
 * Extracts data from JSON Lines files where each line is a separate JSON object.
 * More memory-efficient for large files.
 */
class JsonLinesExtractor : public core::IExtractor {
public:
    /**
     * @brief Constructor
     */
    JsonLinesExtractor() = default;

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "jsonl"; }

    /**
     * @brief Finalize extraction
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Open JSONL file
     * @param filepath File path
     */
    void open_file(const std::string& filepath);

    /**
     * @brief Read next line and parse JSON
     * @return std::optional<json> Parsed JSON object
     */
    std::optional<json> read_next_line();

    /**
     * @brief Convert JSON object to record
     * @param obj JSON object
     * @return core::Record Converted record
     */
    core::Record json_to_record(const json& obj);

private:
    std::ifstream file_stream_;
    std::string filepath_;
    JsonOptions options_;
    size_t current_line_{0};
    size_t extracted_count_{0};
    size_t error_count_{0};
    bool has_more_{true};
    std::chrono::steady_clock::time_point start_time_;
};

/**
 * @brief Streaming JSON extractor for very large files
 *
 * Uses a SAX-style parser to handle JSON files that don't fit in memory.
 */
class StreamingJsonExtractor : public core::IExtractor {
public:
    /**
     * @brief SAX event handler for JSON parsing
     */
    class JsonHandler : public nlohmann::json_sax<json> {
    public:
        JsonHandler(std::queue<core::Record>& record_queue,
                   const JsonOptions& options)
            : record_queue_(record_queue), options_(options) {}

        // SAX interface implementation
        bool null() override;
        bool boolean(bool val) override;
        bool number_integer(number_integer_t val) override;
        bool number_unsigned(number_unsigned_t val) override;
        bool number_float(number_float_t val, const string_t& s) override;
        bool string(string_t& val) override;
        bool binary(binary_t& val) override;
        bool start_object(std::size_t elements) override;
        bool end_object() override;
        bool start_array(std::size_t elements) override;
        bool end_array() override;
        bool key(string_t& val) override;
        bool parse_error(std::size_t position, const std::string& last_token,
                        const nlohmann::detail::exception& ex) override;

    private:
        std::queue<core::Record>& record_queue_;
        const JsonOptions& options_;
        std::stack<std::string> path_stack_;
        std::stack<json> object_stack_;
        std::string current_key_;
        bool in_data_array_{false};
    };

    /**
     * @brief Constructor
     */
    StreamingJsonExtractor() = default;

    // IExtractor interface implementation
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;
    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override;
    bool has_more_data() const override;
    std::string get_type() const override { return "streaming_json"; }
    void finalize(core::ProcessingContext& context) override;
    std::unordered_map<std::string, std::any> get_statistics() const override;

private:
    std::ifstream file_stream_;
    std::string filepath_;
    JsonOptions options_;
    std::queue<core::Record> record_queue_;
    std::unique_ptr<std::thread> parser_thread_;
    mutable std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    std::atomic<bool> parsing_complete_{false};
    std::atomic<bool> has_error_{false};
    std::string error_message_;
    size_t extracted_count_{0};
    std::chrono::steady_clock::time_point start_time_;

    void parse_file_async();
};

/**
 * @brief Factory for JSON extractors
 */
class JsonExtractorFactory {
public:
    /**
     * @brief Create JSON extractor
     * @param type Extractor type (json, jsonl, streaming_json)
     * @return std::unique_ptr<core::IExtractor> Extractor instance
     */
    static std::unique_ptr<core::IExtractor> create(const std::string& type);

    /**
     * @brief Register JSON extractors with the main factory
     */
    static void register_extractors();
};

// Implementation helpers

inline void JsonExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                     core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-json-extractor");
    logger->info("Initializing JSON extractor");

    // Get filepath
    if (config.find("filepath") == config.end()) {
        throw common::ExtractionException("JSON extractor requires 'filepath' parameter", "json");
    }
    filepath_ = std::any_cast<std::string>(config.at("filepath"));

    // Configure options
    if (config.find("root_path") != config.end()) {
        options_.root_path = std::any_cast<std::string>(config.at("root_path"));
    }
    if (config.find("flatten_nested") != config.end()) {
        options_.flatten_nested = std::any_cast<bool>(config.at("flatten_nested"));
    }
    if (config.find("array_delimiter") != config.end()) {
        options_.array_delimiter = std::any_cast<std::string>(config.at("array_delimiter"));
    }

    start_time_ = std::chrono::steady_clock::now();

    // Load JSON file
    load_file(filepath_);
}

inline void JsonExtractor::load_file(const std::string& filepath) {
    auto logger = common::Logger::get("omop-json-extractor");

    if (!std::filesystem::exists(filepath)) {
        throw common::ExtractionException(
            std::format("JSON file not found: '{}'", filepath), "json");
    }

    try {
        std::ifstream file(filepath);
        if (!file.is_open()) {
            throw common::ExtractionException(
                std::format("Failed to open JSON file: '{}'", filepath), "json");
        }

        // Parse JSON
        file >> json_data_;
        data_loaded_ = true;

        // Navigate to data array
        if (!options_.root_path.empty()) {
            current_iterator_ = navigate_to_data(json_data_, options_.root_path);

            // Find the parent array
            std::vector<std::string> path_parts;
            std::stringstream ss(options_.root_path);
            std::string part;
            while (std::getline(ss, part, '.')) {
                path_parts.push_back(part);
            }

            json* current = &json_data_;
            for (const auto& p : path_parts) {
                if (current->contains(p)) {
                    current = &(*current)[p];
                }
            }

            if (current->is_array()) {
                current_iterator_ = current->begin();
                end_iterator_ = current->end();
                total_records_ = current->size();
            } else {
                throw common::ExtractionException(
                    std::format("Path '{}' does not point to an array", options_.root_path), "json");
            }
        } else if (json_data_.is_array()) {
            current_iterator_ = json_data_.begin();
            end_iterator_ = json_data_.end();
            total_records_ = json_data_.size();
        } else {
            // Single object
            total_records_ = 1;
        }

        logger->info("Loaded JSON file with {} records", total_records_);

    } catch (const json::exception& e) {
        throw common::ExtractionException(
            std::format("Failed to parse JSON file: {}", e.what()), "json");
    }
}

inline core::RecordBatch JsonExtractor::extract_batch(size_t batch_size,
                                                     core::ProcessingContext& context) {
    core::RecordBatch batch(batch_size);
    batch.reserve(batch_size);

    if (!data_loaded_) {
        return batch;
    }

    size_t count = 0;

    // Handle array of objects
    if (json_data_.is_array() || !options_.root_path.empty()) {
        while (current_iterator_ != end_iterator_ && count < batch_size) {
            try {
                if (current_iterator_->is_object()) {
                    auto record = flatten_json_object(*current_iterator_);
                    batch.addRecord(std::move(record));
                    count++;
                    extracted_count_++;
                }
            } catch (const std::exception& e) {
                context.log("warning",
                    std::format("Failed to extract record: {}", e.what()));
                context.increment_errors();
            }

            ++current_iterator_;
        }
    } else if (json_data_.is_object() && extracted_count_ == 0) {
        // Single object
        try {
            auto record = flatten_json_object(json_data_);
            batch.addRecord(std::move(record));
            extracted_count_++;
        } catch (const std::exception& e) {
            context.log("error",
                std::format("Failed to extract single object: {}", e.what()));
            context.increment_errors();
        }
    }

    return batch;
}

inline core::Record JsonExtractor::flatten_json_object(const json& obj,
                                                      const std::string& prefix,
                                                      size_t depth) {
    core::Record record;

    if (depth > options_.max_depth) {
        return record;
    }

    for (auto& [key, value] : obj.items()) {
        std::string field_name = prefix.empty() ? key : prefix + options_.array_delimiter + key;

        if (value.is_null() && options_.ignore_null) {
            continue;
        }

        if (value.is_object() && options_.flatten_nested) {
            // Recursively flatten nested object
            auto nested_record = flatten_json_object(value, field_name, depth + 1);
            for (const auto& nested_field : nested_record.getFieldNames()) {
                try {
                    auto nested_value = nested_record.getField(nested_field);
                    record.setField(nested_field, nested_value);
                } catch (const std::exception&) {
                    // Field doesn't exist, skip it
                }
            }
        } else if (value.is_array() && options_.flatten_nested) {
            // Flatten array
            for (size_t i = 0; i < value.size(); ++i) {
                std::string array_field = field_name + options_.array_delimiter + std::to_string(i);
                if (value[i].is_object()) {
                    auto nested_record = flatten_json_object(value[i], array_field, depth + 1);
                    for (const auto& nested_field : nested_record.getFieldNames()) {
                        try {
                            auto nested_value = nested_record.getField(nested_field);
                            record.setField(nested_field, nested_value);
                        } catch (const std::exception&) {
                            // Field doesn't exist, skip it
                        }
                    }
                } else {
                    record.setField(array_field, json_to_any(value[i]));
                }
            }
        } else {
            // Direct value
            record.setField(field_name, json_to_any(value));
        }
    }

    return record;
}

inline std::any JsonExtractor::json_to_any(const json& value) {
    if (value.is_null()) {
        return std::any{};
    } else if (value.is_boolean()) {
        return value.get<bool>();
    } else if (value.is_number_integer()) {
        return value.get<int64_t>();
    } else if (value.is_number_float()) {
        return value.get<double>();
    } else if (value.is_string()) {
        std::string str_val = value.get<std::string>();

        // Try to parse as date if enabled
        if (options_.parse_dates) {
            auto date = parse_date(str_val);
            if (date) {
                return *date;
            }
        }

        return str_val;
    } else {
        // Complex type, convert to string representation
        return value.dump();
    }
}

inline bool JsonExtractor::has_more_data() const {
    if (!data_loaded_) return false;

    if (json_data_.is_array() || !options_.root_path.empty()) {
        return current_iterator_ != end_iterator_;
    } else {
        return extracted_count_ == 0;
    }
}

} // namespace omop::extract

File src/lib/extract/extractor_base.h:

/**
 * @file extractor_base.h
 * @brief Base class for data extractors in the OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This file contains the abstract base class for all data extractors,
 * defining the interface for extracting data from various sources. It
 * provides a common foundation for implementing specific data source
 * extractors with shared functionality.
 * 
 * @section design Design Principles
 * - Extensibility: Easy to add new extractor types
 * - Consistency: Common interface for all extractors
 * - Error Handling: Robust error management
 * - Progress Tracking: Real-time extraction monitoring
 * - Resource Management: Proper cleanup and initialization
 * 
 * @section components Components
 * - ExtractionStats: Statistics tracking structure
 * - ExtractionOptions: Configuration options
 * - SourceSchema: Data source schema definition
 * - ExtractorBase: Abstract base class
 * 
 * @section usage Usage
 * To implement a new extractor:
 * 1. Inherit from ExtractorBase
 * 2. Implement pure virtual methods
 * 3. Configure extraction options
 * 4. Handle errors appropriately
 * 
 * @section example Example
 * @code
 * class MyExtractor : public ExtractorBase {
 * public:
 *     MyExtractor(const std::string& name,
 *                 std::shared_ptr<ConfigurationManager> config,
 *                 std::shared_ptr<Logger> logger)
 *         : ExtractorBase(name, config, logger) {}
 *     
 *     SourceSchema getSchema() const override {
 *         // Implement schema retrieval
 *     }
 *     
 *     ValidationResult validateSource() override {
 *         // Implement source validation
 *     }
 *     
 * protected:
 *     bool connect() override {
 *         // Implement connection logic
 *     }
 *     
 *     void disconnect() override {
 *         // Implement disconnection logic
 *     }
 *     
 *     std::vector<Record> extractBatchImpl(size_t batch_size) override {
 *         // Implement batch extraction
 *     }
 * };
 * @endcode
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>
#include <any>
#include <optional>

#include "core/interfaces.h"
#include "core/record.h"
#include "common/exceptions.h"
#include "common/logging.h"
#include "common/configuration.h"

namespace omop::extract {

/**
 * @brief Extraction statistics
 * 
 * @section description Description
 * Tracks various metrics and statistics during the extraction process.
 * 
 * @section fields Fields
 * - total_records: Total records processed
 * - successful_records: Successfully extracted records
 * - failed_records: Failed records
 * - skipped_records: Skipped records
 * - extraction_time_seconds: Total extraction time
 * - error_counts: Counts by error type
 */
struct ExtractionStats {
    size_t total_records{0};          ///< Total records processed
    size_t successful_records{0};     ///< Successfully extracted records
    size_t failed_records{0};         ///< Failed records
    size_t skipped_records{0};        ///< Skipped records
    double extraction_time_seconds{0.0}; ///< Total extraction time
    std::unordered_map<std::string, size_t> error_counts; ///< Error type counts
};

/**
 * @brief Extraction options
 * 
 * @section description Description
 * Configures the behavior of the extraction process.
 * 
 * @section fields Fields
 * - batch_size: Records per batch
 * - max_records: Maximum records to extract
 * - skip_records: Records to skip
 * - continue_on_error: Continue on errors
 * - validate_schema: Validate source schema
 * - columns: Specific columns to extract
 * - filter_expression: Filter expression
 * - custom_options: Custom extractor options
 */
struct ExtractionOptions {
    size_t batch_size{10000};         ///< Number of records per batch
    size_t max_records{0};            ///< Maximum records to extract (0 = no limit)
    size_t skip_records{0};           ///< Number of records to skip
    bool continue_on_error{true};     ///< Continue extraction on errors
    bool validate_schema{true};       ///< Validate source schema
    std::vector<std::string> columns; ///< Specific columns to extract (empty = all)
    std::string filter_expression;    ///< Filter expression (source-specific)
    std::unordered_map<std::string, std::any> custom_options; ///< Custom extractor options
};

/**
 * @brief Schema information for a data source
 * 
 * @section description Description
 * Defines the structure and metadata of a data source.
 * 
 * @section fields Fields
 * - source_name: Source identifier
 * - source_type: Source type
 * - columns: Column definitions
 * - primary_keys: Primary key columns
 * - metadata: Additional metadata
 */
struct SourceSchema {
    /**
     * @brief Column information
     * 
     * @section description Description
     * Defines the properties of a data source column.
     * 
     * @section fields Fields
     * - name: Column name
     * - data_type: Data type
     * - nullable: Nullability
     * - max_length: Maximum length
     * - default_value: Default value
     * - description: Column description
     */
    struct Column {
        std::string name;             ///< Column name
        std::string data_type;        ///< Data type
        bool nullable{true};          ///< Whether column is nullable
        std::optional<size_t> max_length; ///< Maximum length for string types
        std::optional<std::string> default_value; ///< Default value
        std::string description;      ///< Column description
    };

    std::string source_name;          ///< Source name/identifier
    std::string source_type;          ///< Source type (table, file, etc.)
    std::vector<Column> columns;      ///< Column definitions
    std::vector<std::string> primary_keys; ///< Primary key columns
    std::unordered_map<std::string, std::string> metadata; ///< Additional metadata
};

/**
 * @brief Abstract base class for data extractors
 * 
 * @section description Description
 * This class defines the interface that all concrete extractors must implement.
 * It provides common functionality for batch processing, error handling, and
 * progress tracking.
 * 
 * @section features Features
 * - Batch processing
 * - Error handling
 * - Progress tracking
 * - Schema validation
 * - Resource management
 * - Statistics collection
 */
class ExtractorBase : public core::IExtractor {
public:
    /**
     * @brief Constructor
     * @param name Extractor name
     * @param config Configuration object
     * @param logger Logger instance
     * 
     * @section details Implementation Details
     * - Initializes base class
     * - Sets up configuration
     * - Configures logging
     * - Thread-safe
     */
    ExtractorBase(const std::string& name,
                  std::shared_ptr<common::ConfigurationManager> config,
                  std::shared_ptr<common::Logger> logger);

    /**
     * @brief Virtual destructor
     * 
     * @section details Implementation Details
     * - Ensures proper cleanup
     * - Thread-safe
     */
    virtual ~ExtractorBase() = default;

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Validates configuration
     * - Sets up extractor
     * - Thread-safe
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return RecordBatch Extracted records (empty if no more data)
     * 
     * @section details Implementation Details
     * - Manages batch extraction
     * - Handles errors
     * - Updates statistics
     * - Thread-safe
     */
    core::RecordBatch extract_batch(size_t batch_size, core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     * 
     * @section details Implementation Details
     * - Checks state
     * - Thread-safe
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     * 
     * @section details Implementation Details
     * - Returns type identifier
     * - Thread-safe
     */
    std::string get_type() const override;

    /**
     * @brief Finalize extraction and clean up resources
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Cleans up resources
     * - Finalizes statistics
     * - Thread-safe
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     * 
     * @section details Implementation Details
     * - Collects statistics
     * - Thread-safe
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

    /**
     * @brief Get the schema of the data source
     * @return Source schema
     * 
     * @section details Implementation Details
     * - Pure virtual method
     * - Must be implemented
     * - Thread-safe
     */
    virtual SourceSchema getSchema() const = 0;

    /**
     * @brief Validate the data source
     * @return Validation result
     * 
     * @section details Implementation Details
     * - Pure virtual method
     * - Must be implemented
     * - Thread-safe
     */
    virtual core::ValidationResult validateSource() = 0;

    /**
     * @brief Get extraction statistics (internal)
     * @return Extraction statistics
     * 
     * @section details Implementation Details
     * - Returns internal stats
     * - Thread-safe
     */
    ExtractionStats getStatistics() const { return stats_; }

    /**
     * @brief Reset the extractor to initial state
     * 
     * @section details Implementation Details
     * - Resets state
     * - Clears statistics
     * - Thread-safe
     */
    virtual void reset();

    /**
     * @brief Close the extractor and release resources
     * 
     * @section details Implementation Details
     * - Releases resources
     * - Disconnects if needed
     * - Thread-safe
     */
    virtual void close();

    /**
     * @brief Set progress callback
     * @param callback Progress callback function
     * 
     * @section details Implementation Details
     * - Sets callback
     * - Thread-safe
     */
    void setProgressCallback(std::function<void(size_t, size_t)> callback) {
        progress_callback_ = callback;
    }

    /**
     * @brief Get extractor name
     * @return Extractor name
     * 
     * @section details Implementation Details
     * - Returns name
     * - Thread-safe
     */
    const std::string& getName() const { return name_; }

protected:
    /**
     * @brief Connect to the data source
     * @return true if connection successful
     * 
     * @section details Implementation Details
     * - Pure virtual method
     * - Must be implemented
     * - Thread-safe
     */
    virtual bool connect() = 0;

    /**
     * @brief Disconnect from the data source
     * 
     * @section details Implementation Details
     * - Pure virtual method
     * - Must be implemented
     * - Thread-safe
     */
    virtual void disconnect() = 0;

    /**
     * @brief Extract a single batch of records (implementation)
     * @param batch_size Size of batch to extract
     * @return Vector of records
     * 
     * @section details Implementation Details
     * - Pure virtual method
     * - Must be implemented
     * - Thread-safe
     */
    virtual std::vector<core::Record> extractBatchImpl(size_t batch_size) = 0;

    /**
     * @brief Convert source data to Record format
     * @param source_data Source data in native format
     * @return Converted record
     * 
     * @section details Implementation Details
     * - Pure virtual method
     * - Must be implemented
     * - Thread-safe
     */
    virtual core::Record convertToRecord(const std::any& source_data) = 0;

    /**
     * @brief Handle extraction error
     * @param error Error message
     * @param record_context Record context (if applicable)
     * 
     * @section details Implementation Details
     * - Logs error
     * - Updates statistics
     * - Thread-safe
     */
    void handleError(const std::string& error,
                    const std::optional<std::any>& record_context = std::nullopt);

    /**
     * @brief Update progress
     * @param current Current record count
     * @param total Total record count (0 if unknown)
     * 
     * @section details Implementation Details
     * - Calls callback
     * - Thread-safe
     */
    void updateProgress(size_t current, size_t total = 0);

    /**
     * @brief Apply filter to record
     * @param record Record to filter
     * @return true if record passes filter
     * 
     * @section details Implementation Details
     * - Applies filter
     * - Thread-safe
     */
    virtual bool applyFilter(const core::Record& record);

    /**
     * @brief Apply column selection to record
     * @param record Record to process
     * @return Record with selected columns
     * 
     * @section details Implementation Details
     * - Selects columns
     * - Thread-safe
     */
    virtual core::Record selectColumns(const core::Record& record);

protected:
    std::string name_;                              ///< Extractor name
    std::shared_ptr<common::ConfigurationManager> config_; ///< Configuration
    std::shared_ptr<common::Logger> logger_;        ///< Logger
    ExtractionStats stats_;                         ///< Extraction statistics
    ExtractionOptions options_;                     ///< Current extraction options
    bool is_connected_{false};                      ///< Connection status
    bool is_initialized_{false};                    ///< Initialization status
    size_t current_position_{0};                    ///< Current position in data source
    std::function<void(size_t, size_t)> progress_callback_; ///< Progress callback

private:
    /**
     * @brief Register extractor type
     * @param type Extractor type
     * @param creator Creator function
     * 
     * @section details Implementation Details
     * - Registers type
     * - Thread-safe
     */
    static void registerExtractorType(
        const std::string& type,
        std::function<std::unique_ptr<ExtractorBase>(
            const std::string&,
            std::shared_ptr<common::ConfigurationManager>,
            std::shared_ptr<common::Logger>)> creator);

    /**
     * @brief Create extractor instance
     * @param type Extractor type
     * @param name Extractor name
     * @param config Configuration
     * @param logger Logger
     * @return Extractor instance
     * 
     * @section details Implementation Details
     * - Creates extractor
     * - Thread-safe
     */
    static std::unique_ptr<ExtractorBase> createExtractor(
        const std::string& type,
        const std::string& name,
        std::shared_ptr<common::ConfigurationManager> config,
        std::shared_ptr<common::Logger> logger);

    /**
     * @brief Get registered extractor types
     * @return Vector of type names
     * 
     * @section details Implementation Details
     * - Returns types
     * - Thread-safe
     */
    static std::vector<std::string> getRegisteredTypes();
};

} // namespace omop::extract

File src/lib/extract/odbc_connector.h:

/**
 * @file odbc_connector.h
 * @brief ODBC database connector interface
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file provides the ODBC implementation of the database connector
 * interface, supporting various databases through ODBC drivers.
 */

#pragma once

#include "extract/database_connector.h"
#include <sql.h>
#include <sqlext.h>
#include <memory>
#include <mutex>
#include <atomic>

namespace omop::extract {

/**
 * @brief ODBC error information
 */
struct OdbcError {
    std::string state;       ///< SQL state
    SQLINTEGER native_error; ///< Native error code
    std::string message;     ///< Error message
};

/**
 * @brief ODBC handle wrapper with RAII
 */
template<typename HandleType>
class OdbcHandle {
public:
    /**
     * @brief Constructor
     * @param handle_type SQL handle type
     * @param parent_handle Parent handle (if applicable)
     */
    OdbcHandle(SQLSMALLINT handle_type, SQLHANDLE parent_handle = SQL_NULL_HANDLE)
        : handle_type_(handle_type) {
        
        SQLRETURN ret = SQLAllocHandle(handle_type_, parent_handle, &handle_);
        if (!SQL_SUCCEEDED(ret)) {
            throw common::DatabaseException("Failed to allocate ODBC handle", "ODBC", ret);
        }
    }

    /**
     * @brief Destructor
     */
    ~OdbcHandle() {
        if (handle_ != SQL_NULL_HANDLE) {
            SQLFreeHandle(handle_type_, handle_);
        }
    }

    // Delete copy operations
    OdbcHandle(const OdbcHandle&) = delete;
    OdbcHandle& operator=(const OdbcHandle&) = delete;

    /**
     * @brief Move constructor
     */
    OdbcHandle(OdbcHandle&& other) noexcept
        : handle_(other.handle_), handle_type_(other.handle_type_) {
        other.handle_ = SQL_NULL_HANDLE;
    }

    /**
     * @brief Move assignment operator
     */
    OdbcHandle& operator=(OdbcHandle&& other) noexcept {
        if (this != &other) {
            if (handle_ != SQL_NULL_HANDLE) {
                SQLFreeHandle(handle_type_, handle_);
            }
            handle_ = other.handle_;
            handle_type_ = other.handle_type_;
            other.handle_ = SQL_NULL_HANDLE;
        }
        return *this;
    }

    /**
     * @brief Get raw handle
     * @return SQLHANDLE Raw ODBC handle
     */
    SQLHANDLE get() const { return handle_; }

    /**
     * @brief Implicit conversion to raw handle
     * @return SQLHANDLE Raw ODBC handle
     */
    operator SQLHANDLE() const { return handle_; }

private:
    SQLHANDLE handle_{SQL_NULL_HANDLE};
    SQLSMALLINT handle_type_;
};

using OdbcEnvironment = OdbcHandle<SQLHENV>;
using OdbcConnection = OdbcHandle<SQLHDBC>;
using OdbcStatement = OdbcHandle<SQLHSTMT>;

/**
 * @brief ODBC result set implementation
 *
 * This class provides access to ODBC query results through the
 * IResultSet interface, handling type conversions and NULL values.
 */
class OdbcResultSet : public ResultSetBase {
public:
    /**
     * @brief Constructor
     * @param statement ODBC statement handle
     */
    explicit OdbcResultSet(std::shared_ptr<OdbcStatement> statement);

    /**
     * @brief Destructor
     */
    ~OdbcResultSet() override;

    /**
     * @brief Move to next row
     * @return bool True if successful
     */
    bool next() override;

    /**
     * @brief Get column value by index
     * @param index Column index (0-based)
     * @return std::any Column value
     */
    std::any get_value(size_t index) const override;

    /**
     * @brief Get column value by name
     * @param column_name Column name
     * @return std::any Column value
     */
    std::any get_value(const std::string& column_name) const override;

    /**
     * @brief Check if column value is NULL
     * @param index Column index
     * @return bool True if NULL
     */
    bool is_null(size_t index) const override;

    /**
     * @brief Check if column value is NULL
     * @param column_name Column name
     * @return bool True if NULL
     */
    bool is_null(const std::string& column_name) const override;

    /**
     * @brief Get column count
     * @return size_t Number of columns
     */
    size_t column_count() const override;

    /**
     * @brief Get column name by index
     * @param index Column index
     * @return std::string Column name
     */
    std::string column_name(size_t index) const override;

    /**
     * @brief Get column type by index
     * @param index Column index
     * @return std::string Column type name
     */
    std::string column_type(size_t index) const override;

private:
    /**
     * @brief Column metadata
     */
    struct ColumnInfo {
        std::string name;
        SQLSMALLINT sql_type;
        SQLULEN size;
        SQLSMALLINT decimal_digits;
        SQLSMALLINT nullable;
    };

    /**
     * @brief Get column index by name
     * @param column_name Column name
     * @return size_t Column index
     * @throws DatabaseException if column not found
     */
    size_t get_column_index(const std::string& column_name) const;

    /**
     * @brief Convert ODBC value to appropriate type
     * @param index Column index
     * @return std::any Converted value
     */
    std::any convert_value(size_t index) const;

    /**
     * @brief Load column metadata
     */
    void load_metadata();

    std::shared_ptr<OdbcStatement> statement_;
    std::vector<ColumnInfo> columns_;
    mutable std::unordered_map<std::string, size_t> column_index_cache_;
    bool metadata_loaded_{false};
    mutable std::vector<SQLLEN> indicators_;  // For NULL checking
};

/**
 * @brief ODBC prepared statement implementation
 *
 * This class implements prepared statements for ODBC,
 * providing parameterized query execution across different databases.
 */
class OdbcPreparedStatement : public IPreparedStatement {
public:
    /**
     * @brief Constructor
     * @param connection ODBC connection handle
     * @param sql SQL query
     */
    OdbcPreparedStatement(std::shared_ptr<OdbcConnection> connection,
                         const std::string& sql);

    /**
     * @brief Destructor
     */
    ~OdbcPreparedStatement() override;

    /**
     * @brief Bind parameter by index
     * @param index Parameter index (1-based)
     * @param value Parameter value
     */
    void bind(size_t index, const std::any& value) override;

    /**
     * @brief Execute query and return result set
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query() override;

    /**
     * @brief Execute update/insert/delete
     * @return size_t Number of affected rows
     */
    size_t execute_update() override;

    /**
     * @brief Clear all bound parameters
     */
    void clear_parameters() override;

private:
    /**
     * @brief Parameter binding information
     */
    struct ParameterBinding {
        std::any value;
        SQLSMALLINT c_type;
        SQLSMALLINT sql_type;
        std::vector<char> buffer;
        SQLLEN indicator;
    };

    /**
     * @brief Bind parameter to statement
     * @param index Parameter index
     * @param binding Parameter binding info
     */
    void bind_parameter(size_t index, ParameterBinding& binding);

    std::shared_ptr<OdbcConnection> connection_;
    std::shared_ptr<OdbcStatement> statement_;
    std::string sql_;
    std::unordered_map<size_t, ParameterBinding> parameters_;
};

/**
 * @brief ODBC database connection implementation
 *
 * This class provides the concrete implementation of IDatabaseConnection
 * for ODBC databases, supporting various database systems through ODBC drivers.
 */
class OdbcDatabaseConnection : public IDatabaseConnection {
public:
    /**
     * @brief Constructor
     */
    OdbcDatabaseConnection();

    /**
     * @brief Destructor
     */
    ~OdbcDatabaseConnection() override;

    /**
     * @brief Connect to database
     * @param params Connection parameters
     */
    void connect(const ConnectionParams& params) override;

    /**
     * @brief Disconnect from database
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    bool is_connected() const override;

    /**
     * @brief Execute SQL query
     * @param sql SQL query string
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query(const std::string& sql) override;

    /**
     * @brief Execute SQL update/insert/delete
     * @param sql SQL statement
     * @return size_t Number of affected rows
     */
    size_t execute_update(const std::string& sql) override;

    /**
     * @brief Prepare SQL statement
     * @param sql SQL statement with parameter placeholders
     * @return std::unique_ptr<IPreparedStatement> Prepared statement
     */
    std::unique_ptr<IPreparedStatement> prepare_statement(const std::string& sql) override;

    /**
     * @brief Begin transaction
     */
    void begin_transaction() override;

    /**
     * @brief Commit transaction
     */
    void commit() override;

    /**
     * @brief Rollback transaction
     */
    void rollback() override;

    /**
     * @brief Get database type name
     * @return std::string Database type
     */
    std::string get_database_type() const override;

    /**
     * @brief Get database version
     * @return std::string Database version string
     */
    std::string get_version() const override;

    /**
     * @brief Set query timeout
     * @param seconds Timeout in seconds
     */
    void set_query_timeout(int seconds) override;

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return bool True if table exists
     */
    bool table_exists(const std::string& table_name,
                     const std::string& schema = "") const override;

    /**
     * @brief Get ODBC error information
     * @param handle_type Handle type
     * @param handle ODBC handle
     * @return std::vector<OdbcError> Error information
     */
    static std::vector<OdbcError> get_odbc_errors(SQLSMALLINT handle_type,
                                                  SQLHANDLE handle);

private:
    /**
     * @brief Build connection string from parameters
     * @param params Connection parameters
     * @return std::string ODBC connection string
     */
    std::string build_connection_string(const ConnectionParams& params) const;

    /**
     * @brief Build DSN connection string
     * @param params Connection parameters
     * @return std::string DSN connection string
     */
    std::string build_dsn_string(const ConnectionParams& params) const;

    /**
     * @brief Check for ODBC errors and throw if needed
     * @param ret ODBC return code
     * @param operation Operation description
     * @param handle_type Handle type
     * @param handle ODBC handle
     */
    void check_error(SQLRETURN ret, const std::string& operation,
                    SQLSMALLINT handle_type, SQLHANDLE handle) const;

public:
    /**
     * @brief Get SQL type name
     * @param sql_type SQL type code
     * @return std::string Type name
     */
    static std::string get_sql_type_name(SQLSMALLINT sql_type);

private:

    std::shared_ptr<OdbcEnvironment> environment_;
    std::shared_ptr<OdbcConnection> connection_;
    bool connected_{false};
    bool in_transaction_{false};
    std::string database_name_;
    std::string driver_name_;
    mutable std::mutex connection_mutex_;
};

/**
 * @brief ODBC-specific database extractor
 *
 * This class extends DatabaseExtractor with ODBC-specific optimizations
 * for different database systems.
 */
class OdbcExtractor : public DatabaseExtractor {
public:
    /**
     * @brief Constructor
     * @param connection ODBC connection
     */
    explicit OdbcExtractor(std::unique_ptr<IDatabaseConnection> connection)
        : DatabaseExtractor(std::move(connection)) {}

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "odbc"; }

protected:
    /**
     * @brief Build extraction query with ODBC-specific optimizations
     * @return std::string SQL query
     */
    std::string build_query() const override;
};

/**
 * @brief ODBC driver manager
 *
 * Manages ODBC driver discovery and configuration
 */
class OdbcDriverManager {
public:
    /**
     * @brief Driver information
     */
    struct DriverInfo {
        std::string name;
        std::string description;
        std::unordered_map<std::string, std::string> attributes;
    };

    /**
     * @brief Data source information
     */
    struct DataSourceInfo {
        std::string name;
        std::string description;
        std::string driver;
    };

    /**
     * @brief Get available ODBC drivers
     * @return std::vector<DriverInfo> Available drivers
     */
    static std::vector<DriverInfo> get_available_drivers();

    /**
     * @brief Get configured data sources
     * @return std::vector<DataSourceInfo> Data sources
     */
    static std::vector<DataSourceInfo> get_data_sources();

    /**
     * @brief Test ODBC connection
     * @param connection_string Connection string
     * @return std::pair<bool, std::string> Success flag and message
     */
    static std::pair<bool, std::string> test_connection(
        const std::string& connection_string);
};

} // namespace omop::extract

File src/lib/extract/postgresql_connector.h:

#pragma once

#include "extract/database_connector.h"
#include <libpq-fe.h>
#include <memory>
#include <mutex>

namespace omop::extract {

/**
 * @brief PostgreSQL result set implementation
 *
 * This class provides access to PostgreSQL query results through the
 * IResultSet interface, handling type conversions and NULL values.
 */
class PostgreSQLResultSet : public ResultSetBase {
public:
    /**
     * @brief Constructor
     * @param result PostgreSQL result handle
     */
    explicit PostgreSQLResultSet(PGresult* result);

    /**
     * @brief Destructor
     */
    ~PostgreSQLResultSet() override;

    /**
     * @brief Move to next row
     * @return bool True if successful
     */
    bool next() override;

    /**
     * @brief Get column value by index
     * @param index Column index
     * @return std::any Column value
     */
    std::any get_value(size_t index) const override;

    /**
     * @brief Get column value by name
     * @param column_name Column name
     * @return std::any Column value
     */
    std::any get_value(const std::string& column_name) const override;

    /**
     * @brief Check if column value is NULL
     * @param index Column index
     * @return bool True if NULL
     */
    bool is_null(size_t index) const override;

    /**
     * @brief Check if column value is NULL
     * @param column_name Column name
     * @return bool True if NULL
     */
    bool is_null(const std::string& column_name) const override;

    /**
     * @brief Get column count
     * @return size_t Number of columns
     */
    size_t column_count() const override;

    /**
     * @brief Get column name by index
     * @param index Column index
     * @return std::string Column name
     */
    std::string column_name(size_t index) const override;

    /**
     * @brief Get column type by index
     * @param index Column index
     * @return std::string Column type name
     */
    std::string column_type(size_t index) const override;

private:
    /**
     * @brief Get column index by name
     * @param column_name Column name
     * @return size_t Column index
     * @throws DatabaseException if column not found
     */
    size_t get_column_index(const std::string& column_name) const;

    /**
     * @brief Convert PostgreSQL value to appropriate type
     * @param value String value from PostgreSQL
     * @param oid PostgreSQL type OID
     * @return std::any Converted value
     */
    std::any convert_value(const char* value, Oid oid) const;

    PGresult* result_;
    int row_count_;
    int current_row_;
    int column_count_;
    mutable std::unordered_map<std::string, size_t> column_index_cache_;
};

/**
 * @brief PostgreSQL prepared statement implementation
 *
 * This class implements prepared statements for PostgreSQL,
 * providing parameterized query execution.
 */
class PostgreSQLPreparedStatement : public IPreparedStatement {
public:
    /**
     * @brief Constructor
     * @param connection PostgreSQL connection
     * @param statement_name Prepared statement name
     * @param sql SQL query
     */
    PostgreSQLPreparedStatement(PGconn* connection,
                              const std::string& statement_name,
                              const std::string& sql);

    /**
     * @brief Destructor
     */
    ~PostgreSQLPreparedStatement() override;

    /**
     * @brief Bind parameter by index
     * @param index Parameter index (1-based)
     * @param value Parameter value
     */
    void bind(size_t index, const std::any& value) override;

    /**
     * @brief Execute query and return result set
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query() override;

    /**
     * @brief Execute update/insert/delete
     * @return size_t Number of affected rows
     */
    size_t execute_update() override;

    /**
     * @brief Clear all bound parameters
     */
    void clear_parameters() override;

private:
    /**
     * @brief Convert parameter value to string
     * @param value Parameter value
     * @return std::string String representation
     */
    std::string convert_parameter(const std::any& value) const;

    PGconn* connection_;
    std::string statement_name_;
    std::string sql_;
    std::vector<std::string> parameters_;
    std::vector<const char*> param_values_;
    std::vector<int> param_lengths_;
    std::vector<int> param_formats_;
};

/**
 * @brief PostgreSQL database connection implementation
 *
 * This class provides the concrete implementation of IDatabaseConnection
 * for PostgreSQL databases, using libpq for database operations.
 */
class PostgreSQLConnection : public IDatabaseConnection {
public:
    /**
     * @brief Constructor
     */
    PostgreSQLConnection();

    /**
     * @brief Destructor
     */
    ~PostgreSQLConnection() override;

    /**
     * @brief Connect to database
     * @param params Connection parameters
     */
    void connect(const ConnectionParams& params) override;

    /**
     * @brief Disconnect from database
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    bool is_connected() const override;

    /**
     * @brief Execute SQL query
     * @param sql SQL query string
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query(const std::string& sql) override;

    /**
     * @brief Execute SQL update/insert/delete
     * @param sql SQL statement
     * @return size_t Number of affected rows
     */
    size_t execute_update(const std::string& sql) override;

    /**
     * @brief Prepare SQL statement
     * @param sql SQL statement with parameter placeholders
     * @return std::unique_ptr<IPreparedStatement> Prepared statement
     */
    std::unique_ptr<IPreparedStatement> prepare_statement(const std::string& sql) override;

    /**
     * @brief Begin transaction
     */
    void begin_transaction() override;

    /**
     * @brief Commit transaction
     */
    void commit() override;

    /**
     * @brief Rollback transaction
     */
    void rollback() override;

    /**
     * @brief Get database type name
     * @return std::string Database type
     */
    std::string get_database_type() const override { return "PostgreSQL"; }

    /**
     * @brief Get database version
     * @return std::string Database version string
     */
    std::string get_version() const override;

    /**
     * @brief Set query timeout
     * @param seconds Timeout in seconds
     */
    void set_query_timeout(int seconds) override;

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return bool True if table exists
     */
    bool table_exists(const std::string& table_name,
                     const std::string& schema = "") const override;

    /**
     * @brief Get raw PostgreSQL connection handle
     * @return PGconn* Connection handle (for internal use)
     */
    PGconn* get_raw_connection() { return connection_; }

private:
    /**
     * @brief Build connection string from parameters
     * @param params Connection parameters
     * @return std::string PostgreSQL connection string
     */
    std::string build_connection_string(const ConnectionParams& params) const;

    /**
     * @brief Check for PostgreSQL errors
     * @param result Query result
     * @param operation Operation description
     */
    void check_error(PGresult* result, const std::string& operation) const;

    /**
     * @brief Generate unique statement name
     * @return std::string Unique statement name
     */
    std::string generate_statement_name();

    PGconn* connection_;
    bool in_transaction_;
    int query_timeout_;
    mutable std::mutex connection_mutex_;
    std::atomic<int> statement_counter_;
};

/**
 * @brief PostgreSQL-specific database extractor
 *
 * This class extends DatabaseExtractor with PostgreSQL-specific optimizations
 * such as cursor-based extraction for large result sets.
 */
class PostgreSQLExtractor : public DatabaseExtractor {
public:
    /**
     * @brief Constructor
     * @param connection PostgreSQL connection
     */
    explicit PostgreSQLExtractor(std::unique_ptr<IDatabaseConnection> connection)
        : DatabaseExtractor(std::move(connection)) {}

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "postgresql"; }

protected:
    /**
     * @brief Build extraction query with PostgreSQL-specific optimizations
     * @return std::string SQL query
     */
    std::string build_query() const override;

    /**
     * @brief Apply PostgreSQL-specific query hints
     * @param query Base query
     * @return std::string Query with hints
     */
    std::string apply_query_hints(const std::string& query) const;
};

/**
 * @brief Registration helper for PostgreSQL components
 *
 * This class handles the registration of PostgreSQL components
 * with the appropriate factories during application startup.
 */
class PostgreSQLRegistrar {
public:
    /**
     * @brief Register all PostgreSQL components
     */
    static void register_components() {
        DatabaseConnectionFactory::instance().register_type(
            "postgresql",
            [](const IDatabaseConnection::ConnectionParams& params) {
                auto conn = std::make_unique<PostgreSQLConnection>();
                conn->connect(params);
                return conn;
            }
        );

        DatabaseConnectionFactory::instance().register_type(
            "postgres",  // Alias
            [](const IDatabaseConnection::ConnectionParams& params) {
                auto conn = std::make_unique<PostgreSQLConnection>();
                conn->connect(params);
                return conn;
            }
        );
    }

private:
    PostgreSQLRegistrar() = default;
};

// Implementation file content (postgresql_connector.cpp)
namespace {
    // PostgreSQL type OIDs
    constexpr Oid BOOLOID = 16;
    constexpr Oid INT2OID = 21;
    constexpr Oid INT4OID = 23;
    constexpr Oid INT8OID = 20;
    constexpr Oid FLOAT4OID = 700;
    constexpr Oid FLOAT8OID = 701;
    constexpr Oid TEXTOID = 25;
    constexpr Oid VARCHAROID = 1043;
    constexpr Oid DATEOID = 1082;
    constexpr Oid TIMESTAMPOID = 1114;
    constexpr Oid TIMESTAMPTZOID = 1184;
}

} // namespace omop::extract

File src/lib/service/etl_service.h:

#pragma once

#include "core/pipeline.h"
#include "common/configuration.h"
#include "common/logging.h"
#include "common/utilities.h"
#include <memory>
#include <functional>
#include <chrono>

namespace omop::service {

/**
 * @brief ETL job request
 */
struct ETLJobRequest {
    std::string name;
    std::string description;
    std::string source_table;
    std::string target_table;
    std::string extractor_type{"database"};
    std::string loader_type{"omop_database"};
    std::unordered_map<std::string, std::any> extractor_config;
    std::unordered_map<std::string, std::any> loader_config;
    core::PipelineConfig pipeline_config;
    bool dry_run{false};
    std::optional<std::chrono::system_clock::time_point> scheduled_time;
};

/**
 * @brief ETL job result
 */
struct ETLJobResult {
    std::string job_id;
    core::JobStatus status;
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point end_time;
    size_t total_records{0};
    size_t processed_records{0};
    size_t error_records{0};
    std::vector<std::string> errors;
    std::unordered_map<std::string, std::any> metrics;
};

/**
 * @brief ETL service for managing ETL operations
 *
 * This service provides high-level ETL operations, job management,
 * and coordination between different components.
 */
class ETLService {
public:
    /**
     * @brief Constructor
     * @param config Configuration manager
     * @param pipeline_manager Pipeline manager
     */
    ETLService(std::shared_ptr<common::ConfigurationManager> config,
               std::shared_ptr<core::PipelineManager> pipeline_manager);

    /**
     * @brief Create and start an ETL job
     * @param request Job request
     * @return Job ID
     */
    std::string create_job(const ETLJobRequest& request);

    /**
     * @brief Get job status
     * @param job_id Job ID
     * @return Job result
     */
    std::optional<ETLJobResult> get_job_result(const std::string& job_id);

    /**
     * @brief Get all job results
     * @return Vector of job results
     */
    std::vector<ETLJobResult> get_all_job_results() const;

    /**
     * @brief Cancel a job
     * @param job_id Job ID
     * @return True if cancelled
     */
    bool cancel_job(const std::string& job_id);

    /**
     * @brief Pause a job
     * @param job_id Job ID
     * @return True if paused
     */
    bool pause_job(const std::string& job_id);

    /**
     * @brief Resume a job
     * @param job_id Job ID
     * @return True if resumed
     */
    bool resume_job(const std::string& job_id);

    /**
     * @brief Schedule a job
     * @param request Job request with scheduled time
     * @return Job ID
     */
    std::string schedule_job(const ETLJobRequest& request);

    /**
     * @brief Run ETL for all configured tables
     * @param parallel Whether to run tables in parallel
     * @return Map of table name to job ID
     */
    std::unordered_map<std::string, std::string> run_all_tables(bool parallel = false);

    /**
     * @brief Validate ETL configuration for a table
     * @param table_name Table name
     * @return Validation errors (empty if valid)
     */
    std::vector<std::string> validate_table_config(const std::string& table_name);

    /**
     * @brief Get ETL statistics
     * @return Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const;

    /**
     * @brief Set job completion callback
     * @param callback Callback function
     */
    void set_completion_callback(
        std::function<void(const std::string&, const ETLJobResult&)> callback);

    /**
     * @brief Set job error callback
     * @param callback Callback function
     */
    void set_error_callback(
        std::function<void(const std::string&, const std::exception&)> callback);

protected:
    /**
     * @brief Build pipeline from request
     * @param request Job request
     * @return ETL pipeline
     */
    std::unique_ptr<core::ETLPipeline> build_pipeline(const ETLJobRequest& request);

    /**
     * @brief Create extractor
     * @param type Extractor type
     * @param config Configuration
     * @return Extractor instance
     */
    std::unique_ptr<core::IExtractor> create_extractor(
        const std::string& type,
        const std::unordered_map<std::string, std::any>& config);

    /**
     * @brief Create transformer
     * @param table_name Target table name
     * @return Transformer instance
     */
    std::unique_ptr<core::ITransformer> create_transformer(
        const std::string& table_name);

    /**
     * @brief Create loader
     * @param type Loader type
     * @param config Configuration
     * @return Loader instance
     */
    std::unique_ptr<core::ILoader> create_loader(
        const std::string& type,
        const std::unordered_map<std::string, std::any>& config);

    /**
     * @brief Generate job ID
     * @return Unique job ID
     */
    std::string generate_job_id();

private:
    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<common::Logger> logger_;

    // Job tracking
    std::unordered_map<std::string, ETLJobResult> job_results_;
    mutable std::mutex results_mutex_;

    // Callbacks
    std::function<void(const std::string&, const ETLJobResult&)> completion_callback_;
    std::function<void(const std::string&, const std::exception&)> error_callback_;

    // Statistics
    std::atomic<size_t> total_jobs_created_{0};
    std::atomic<size_t> total_jobs_completed_{0};
    std::atomic<size_t> total_jobs_failed_{0};
};

/**
 * @brief ETL scheduler service
 *
 * Manages scheduled ETL jobs and recurring tasks.
 */
class ETLScheduler {
public:
    /**
     * @brief Schedule type
     */
    enum class ScheduleType {
        Once,
        Daily,
        Weekly,
        Monthly,
        Cron
    };

    /**
     * @brief Schedule definition
     */
    struct Schedule {
        ScheduleType type;
        std::chrono::system_clock::time_point start_time;
        std::optional<std::chrono::system_clock::time_point> end_time;
        std::string cron_expression;
        std::chrono::minutes interval{0};
    };

    /**
     * @brief Constructor
     * @param etl_service ETL service
     */
    explicit ETLScheduler(std::shared_ptr<ETLService> etl_service);

    /**
     * @brief Destructor
     */
    ~ETLScheduler();

    /**
     * @brief Start the scheduler
     */
    void start();

    /**
     * @brief Stop the scheduler
     */
    void stop();

    /**
     * @brief Schedule a job
     * @param job_id Unique job identifier
     * @param request Job request
     * @param schedule Schedule definition
     */
    void schedule_job(const std::string& job_id,
                     const ETLJobRequest& request,
                     const Schedule& schedule);

    /**
     * @brief Cancel a scheduled job
     * @param job_id Job identifier
     * @return True if cancelled
     */
    bool cancel_scheduled_job(const std::string& job_id);

    /**
     * @brief Get scheduled jobs
     * @return Map of job ID to schedule
     */
    std::unordered_map<std::string, Schedule> get_scheduled_jobs() const;

    /**
     * @brief Get next run time for a job
     * @param job_id Job identifier
     * @return Next run time
     */
    std::optional<std::chrono::system_clock::time_point> get_next_run_time(
        const std::string& job_id) const;

private:
    struct ScheduledJob {
        ETLJobRequest request;
        Schedule schedule;
        std::chrono::system_clock::time_point next_run;
        size_t run_count{0};
    };

    std::shared_ptr<ETLService> etl_service_;
    std::shared_ptr<common::Logger> logger_;

    // Scheduler state
    std::unordered_map<std::string, ScheduledJob> scheduled_jobs_;
    mutable std::mutex jobs_mutex_;

    // Scheduler thread
    std::unique_ptr<std::thread> scheduler_thread_;
    std::condition_variable scheduler_cv_;
    std::atomic<bool> running_{false};

    /**
     * @brief Scheduler loop
     */
    void scheduler_loop();

    /**
     * @brief Calculate next run time
     * @param schedule Schedule definition
     * @param last_run Last run time
     * @return Next run time
     */
    std::chrono::system_clock::time_point calculate_next_run(
        const Schedule& schedule,
        const std::chrono::system_clock::time_point& last_run) const;

    /**
     * @brief Parse cron expression
     * @param expression Cron expression
     * @param reference_time Reference time
     * @return Next run time
     */
    std::chrono::system_clock::time_point parse_cron(
        const std::string& expression,
        const std::chrono::system_clock::time_point& reference_time) const;
};

/**
 * @brief ETL monitoring service
 *
 * Monitors ETL jobs and provides metrics and alerts.
 */
class ETLMonitor {
public:
    /**
     * @brief Alert type
     */
    enum class AlertType {
        JobFailed,
        HighErrorRate,
        SlowPerformance,
        ResourceUsage,
        DataQuality
    };

    /**
     * @brief Alert definition
     */
    struct Alert {
        AlertType type;
        std::string job_id;
        std::string message;
        std::chrono::system_clock::time_point timestamp;
        std::unordered_map<std::string, std::any> details;
    };

    /**
     * @brief Constructor
     * @param etl_service ETL service
     */
    explicit ETLMonitor(std::shared_ptr<ETLService> etl_service);

    /**
     * @brief Start monitoring
     */
    void start();

    /**
     * @brief Stop monitoring
     */
    void stop();

    /**
     * @brief Get current alerts
     * @return Vector of alerts
     */
    std::vector<Alert> get_alerts() const;

    /**
     * @brief Clear alerts
     */
    void clear_alerts();

    /**
     * @brief Set alert callback
     * @param callback Callback function
     */
    void set_alert_callback(std::function<void(const Alert&)> callback);

    /**
     * @brief Get job metrics
     * @param job_id Job ID
     * @return Metrics map
     */
    std::unordered_map<std::string, double> get_job_metrics(
        const std::string& job_id) const;

    /**
     * @brief Get system metrics
     * @return Metrics map
     */
    std::unordered_map<std::string, double> get_system_metrics() const;

    /**
     * @brief Set alert thresholds
     * @param error_rate_threshold Error rate threshold (0-1)
     * @param performance_threshold Performance threshold in records/sec
     * @param memory_threshold Memory usage threshold in MB
     */
    void set_thresholds(double error_rate_threshold = 0.05,
                       double performance_threshold = 100.0,
                       size_t memory_threshold = 1024);

private:
    std::shared_ptr<ETLService> etl_service_;
    std::shared_ptr<common::Logger> logger_;

    // Monitoring state
    std::vector<Alert> alerts_;
    mutable std::mutex alerts_mutex_;

    // Metrics
    std::unordered_map<std::string, std::unordered_map<std::string, double>> job_metrics_;
    mutable std::mutex metrics_mutex_;

    // Thresholds
    double error_rate_threshold_{0.05};
    double performance_threshold_{100.0};
    size_t memory_threshold_{1024};

    // Monitoring thread
    std::unique_ptr<std::thread> monitor_thread_;
    std::atomic<bool> running_{false};

    // Callback
    std::function<void(const Alert&)> alert_callback_;

    /**
     * @brief Monitor loop
     */
    void monitor_loop();

    /**
     * @brief Check job health
     * @param job_id Job ID
     * @param result Job result
     */
    void check_job_health(const std::string& job_id, const ETLJobResult& result);

    /**
     * @brief Create alert
     * @param type Alert type
     * @param job_id Job ID
     * @param message Alert message
     * @param details Additional details
     */
    void create_alert(AlertType type,
                     const std::string& job_id,
                     const std::string& message,
                     const std::unordered_map<std::string, std::any>& details = {});

    /**
     * @brief Collect system metrics
     */
    void collect_system_metrics();
};





} // namespace omop::service

File src/lib/transform/vocabulary_service.h:

/**
 * @file vocabulary_service.h
 * @brief Vocabulary service for OMOP concept management and mapping
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header provides vocabulary management functionality for the OMOP ETL pipeline,
 * including concept lookups, source-to-concept mappings, and vocabulary validation.
 * 
 * @section design Design Principles
 * - Caching: Efficient concept and mapping lookups
 * - Thread Safety: Concurrent access support
 * - Validation: Concept and mapping validation
 * - Extensibility: Custom mapping support
 * - Performance: Optimized database queries
 * 
 * @section components Components
 * - Concept: OMOP concept representation
 * - VocabularyMapping: Source-to-concept mapping
 * - VocabularyService: Core vocabulary functionality
 * - VocabularyValidator: Concept validation
 * 
 * @section usage Usage
 * To use the vocabulary service:
 * 1. Initialize service with database connection
 * 2. Load vocabulary mappings
 * 3. Perform concept lookups and mappings
 * 4. Validate concepts and mappings
 * 
 * @section example Example
 * @code
 * auto service = VocabularyService::create(connection);
 * service->initialize();
 * service->load_mappings(config);
 * auto concept = service->get_concept(123);
 * @endcode
 */

#pragma once

#include <string>
#include <unordered_map>
#include <vector>
#include <optional>
#include <memory>
#include <shared_mutex>
#include "extract/database_connector.h"
#include "common/exceptions.h"

namespace omop::transform {

/**
 * @brief Represents an OMOP concept
 *
 * This class encapsulates the information about a single OMOP concept,
 * including its ID, name, domain, and vocabulary information.
 */
class Concept {
public:
    /**
     * @brief Default constructor
     */
    Concept() = default;

    /**
     * @brief Constructor with all fields
     */
    Concept(int concept_id,
            std::string concept_name,
            std::string domain_id,
            std::string vocabulary_id,
            std::string concept_class_id,
            std::string concept_code)
        : concept_id_(concept_id),
          concept_name_(std::move(concept_name)),
          domain_id_(std::move(domain_id)),
          vocabulary_id_(std::move(vocabulary_id)),
          concept_class_id_(std::move(concept_class_id)),
          concept_code_(std::move(concept_code)) {}

    // Getters
    [[nodiscard]] int concept_id() const noexcept { return concept_id_; }
    [[nodiscard]] const std::string& concept_name() const noexcept { return concept_name_; }
    [[nodiscard]] const std::string& domain_id() const noexcept { return domain_id_; }
    [[nodiscard]] const std::string& vocabulary_id() const noexcept { return vocabulary_id_; }
    [[nodiscard]] const std::string& concept_class_id() const noexcept { return concept_class_id_; }
    [[nodiscard]] const std::string& concept_code() const noexcept { return concept_code_; }
    [[nodiscard]] bool is_standard() const noexcept { return standard_concept_ == "S"; }
    [[nodiscard]] bool is_valid() const noexcept { return valid_end_date_.empty(); }

    // Setters
    void set_standard_concept(const std::string& standard) { standard_concept_ = standard; }
    void set_valid_dates(const std::string& start, const std::string& end) {
        valid_start_date_ = start;
        valid_end_date_ = end;
    }

private:
    int concept_id_{0};
    std::string concept_name_;
    std::string domain_id_;
    std::string vocabulary_id_;
    std::string concept_class_id_;
    std::string concept_code_;
    std::string standard_concept_;
    std::string valid_start_date_;
    std::string valid_end_date_;
};

/**
 * @brief Vocabulary mapping entry
 *
 * Represents a mapping from a source value to an OMOP concept.
 */
struct VocabularyMapping {
    std::string source_value;
    std::string source_vocabulary;
    int target_concept_id;
    std::string target_vocabulary;
    float mapping_confidence{1.0f};
    std::string mapping_type;
    std::optional<std::string> context;
};

/**
 * @brief Vocabulary service for concept lookups and mappings
 *
 * This service manages vocabulary data, provides concept lookups,
 * and handles source-to-concept mappings for the ETL pipeline.
 */
class VocabularyService {
public:
    /**
     * @brief Constructor
     * @param connection Database connection for vocabulary tables
     */
    explicit VocabularyService(std::unique_ptr<extract::IDatabaseConnection> connection);

    /**
     * @brief Initialize the service
     * @param cache_size Maximum number of cached concepts
     */
    void initialize(size_t cache_size = 10000);

    /**
     * @brief Load vocabulary mappings from configuration
     * @param mapping_config YAML configuration node
     */
    void load_mappings(const std::string& mapping_config);

    /**
     * @brief Load vocabulary mappings from database
     * @param mapping_table Name of the mapping table
     */
    void load_mappings_from_db(const std::string& mapping_table);

    /**
     * @brief Look up concept by ID
     * @param concept_id Concept ID
     * @return std::optional<Concept> Concept if found
     */
    [[nodiscard]] std::optional<Concept> get_concept(int concept_id);

    /**
     * @brief Look up concept by code and vocabulary
     * @param concept_code Concept code
     * @param vocabulary_id Vocabulary ID
     * @return std::optional<Concept> Concept if found
     */
    [[nodiscard]] std::optional<Concept> get_concept_by_code(
        const std::string& concept_code,
        const std::string& vocabulary_id);

    /**
     * @brief Map source value to concept ID
     * @param source_value Source value to map
     * @param vocabulary_name Vocabulary name for mapping
     * @param context Optional context for disambiguation
     * @return int Concept ID (0 if not found)
     */
    [[nodiscard]] int map_to_concept_id(
        const std::string& source_value,
        const std::string& vocabulary_name,
        const std::optional<std::string>& context = std::nullopt);

    /**
     * @brief Get all mappings for a source value
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @return std::vector<VocabularyMapping> All matching mappings
     */
    [[nodiscard]] std::vector<VocabularyMapping> get_mappings(
        const std::string& source_value,
        const std::string& vocabulary_name);

    /**
     * @brief Find standard concept for a source concept
     * @param source_concept_id Source concept ID
     * @return int Standard concept ID (0 if not found)
     */
    [[nodiscard]] int get_standard_concept(int source_concept_id);

    /**
     * @brief Get descendant concepts
     * @param ancestor_concept_id Ancestor concept ID
     * @param max_levels Maximum levels of descendants (-1 for all)
     * @return std::vector<int> Descendant concept IDs
     */
    [[nodiscard]] std::vector<int> get_descendants(
        int ancestor_concept_id,
        int max_levels = -1);

    /**
     * @brief Get ancestor concepts
     * @param descendant_concept_id Descendant concept ID
     * @param max_levels Maximum levels of ancestors (-1 for all)
     * @return std::vector<int> Ancestor concept IDs
     */
    [[nodiscard]] std::vector<int> get_ancestors(
        int descendant_concept_id,
        int max_levels = -1);

    /**
     * @brief Check if concept is in domain
     * @param concept_id Concept ID
     * @param domain_id Domain ID
     * @return bool True if concept is in domain
     */
    [[nodiscard]] bool is_in_domain(int concept_id, const std::string& domain_id);

    /**
     * @brief Add custom mapping
     * @param mapping Vocabulary mapping to add
     */
    void add_mapping(const VocabularyMapping& mapping);

    /**
     * @brief Clear all cached data
     */
    void clear_cache();

    /**
     * @brief Get cache statistics
     * @return Cache hit rate, size, etc.
     */
    struct CacheStats {
        size_t cache_size;
        size_t max_cache_size;
        size_t hits;
        size_t misses;
        double hit_rate;
    };

    [[nodiscard]] CacheStats get_cache_stats() const;

    /**
     * @brief Validate vocabulary tables exist
     * @return bool True if all required tables exist
     */
    [[nodiscard]] bool validate_vocabulary_tables();

    /**
     * @brief Get vocabulary version information
     * @return std::string Version string
     */
    [[nodiscard]] std::string get_vocabulary_version();

private:
    /**
     * @brief Load concept from database
     * @param concept_id Concept ID
     * @return std::optional<Concept> Concept if found
     */
    std::optional<Concept> load_concept_from_db(int concept_id);

    /**
     * @brief Load concept by code from database
     * @param concept_code Concept code
     * @param vocabulary_id Vocabulary ID
     * @return std::optional<Concept> Concept if found
     */
    std::optional<Concept> load_concept_by_code_from_db(
        const std::string& concept_code,
        const std::string& vocabulary_id);

    /**
     * @brief Normalize source value for matching
     * @param value Source value
     * @param case_sensitive Whether to preserve case
     * @return std::string Normalized value
     */
    std::string normalize_value(const std::string& value, bool case_sensitive) const;

    /**
     * @brief Build mapping key
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @param context Optional context
     * @return std::string Mapping key
     */
    std::string build_mapping_key(
        const std::string& source_value,
        const std::string& vocabulary_name,
        const std::optional<std::string>& context) const;

    // Database connection
    std::unique_ptr<extract::IDatabaseConnection> connection_;

    // Caches
    std::unordered_map<int, Concept> concept_cache_;
    std::unordered_map<std::string, int> code_to_concept_cache_;
    std::unordered_map<std::string, int> mapping_cache_;
    std::unordered_map<int, int> standard_concept_cache_;

    // Cache management
    mutable std::shared_mutex cache_mutex_;
    size_t max_cache_size_{10000};
    mutable size_t cache_hits_{0};
    mutable size_t cache_misses_{0};

    // Vocabulary mappings
    std::unordered_map<std::string, std::vector<VocabularyMapping>> vocabulary_mappings_;
    mutable std::shared_mutex mapping_mutex_;

    // Configuration
    bool case_sensitive_matching_{false};
    std::string vocabulary_schema_{"cdm"};
};

/**
 * @brief Vocabulary-based validator
 *
 * Validates that values can be mapped to valid OMOP concepts.
 */
class VocabularyValidator {
public:
    /**
     * @brief Constructor
     * @param vocabulary_service Reference to vocabulary service
     */
    explicit VocabularyValidator(VocabularyService& vocabulary_service)
        : vocabulary_service_(vocabulary_service) {}

    /**
     * @brief Validate concept ID
     * @param concept_id Concept ID to validate
     * @param expected_domain Expected domain (optional)
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_concept_id(
        int concept_id,
        const std::optional<std::string>& expected_domain = std::nullopt);

    /**
     * @brief Validate source value can be mapped
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @return bool True if can be mapped
     */
    [[nodiscard]] bool validate_mapping_exists(
        const std::string& source_value,
        const std::string& vocabulary_name);

    /**
     * @brief Validate concept is standard
     * @param concept_id Concept ID
     * @return bool True if standard concept
     */
    [[nodiscard]] bool validate_standard_concept(int concept_id);

    /**
     * @brief Get validation errors for a value
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @param expected_domain Expected domain
     * @return std::vector<std::string> List of validation errors
     */
    [[nodiscard]] std::vector<std::string> get_validation_errors(
        const std::string& source_value,
        const std::string& vocabulary_name,
        const std::optional<std::string>& expected_domain = std::nullopt);

private:
    VocabularyService& vocabulary_service_;
};

/**
 * @brief Singleton accessor for global vocabulary service
 */
class VocabularyServiceManager {
public:
    /**
     * @brief Get the singleton instance
     * @return VocabularyService& Reference to the vocabulary service
     */
    [[nodiscard]] static VocabularyService& instance() {
        if (!instance_) {
            throw common::ConfigurationException(
                "VocabularyService not initialized. Call initialize() first.");
        }
        return *instance_;
    }

    /**
     * @brief Initialize the vocabulary service
     * @param connection Database connection
     * @param cache_size Cache size
     */
    static void initialize(
        std::unique_ptr<extract::IDatabaseConnection> connection,
        size_t cache_size = 10000) {
        instance_ = std::make_unique<VocabularyService>(std::move(connection));
        instance_->initialize(cache_size);
    }

    /**
     * @brief Check if initialized
     * @return bool True if initialized
     */
    [[nodiscard]] static bool is_initialized() {
        return instance_ != nullptr;
    }

    /**
     * @brief Reset the service
     */
    static void reset() {
        instance_.reset();
    }

private:
    static std::unique_ptr<VocabularyService> instance_;
};

} // namespace omop::transform

File src/lib/transform/transformations.h:

/**
 * @file transformations.h
 * @brief Common header for all transformation types in the OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header provides a centralized include point for all transformation
 * classes and utilities used in the transform module. It defines common
 * transformation utilities, result structures, and base classes for
 * implementing complex transformations.
 * 
 * @section design Design Principles
 * - Modularity: Independent transformation components
 * - Extensibility: Custom transformation support
 * - Error Handling: Detailed transformation results
 * - Performance: Efficient data processing
 * - Thread Safety: Safe concurrent transformations
 * 
 * @section components Components
 * - TransformationUtils: Common utility functions
 * - TransformationResult: Result structure with metadata
 * - ComplexTransformation: Base class for complex transformations
 * - TransformationRegistry: Registry for custom transformations
 * 
 * @section usage Usage
 * To use the transformation system:
 * 1. Include this header
 * 2. Use utility functions as needed
 * 3. Implement custom transformations
 * 4. Register transformations
 * 
 * @section example Example
 * @code
 * auto result = TransformationUtils::parse_date("2024-01-01", 
 *     {constants::DEFAULT_DATE_FORMAT});
 * if (result) {
 *     auto transformed = TransformationUtils::format_date(
 *         *result, constants::DEFAULT_DATETIME_FORMAT);
 * }
 * @endcode
 */

#pragma once

/**
 * @file transformations.h
 * @brief Common header for all transformation types in the OMOP ETL pipeline
 * 
 * This header provides a centralized include point for all transformation
 * classes and utilities used in the transform module.
 */

#include "transform/transformation_engine.h"
#include "transform/vocabulary_service.h"
#include "common/exceptions.h"
#include "common/logging.h"
#include "core/interfaces.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <optional>
#include <regex>
#include <algorithm>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <cmath>

namespace omop::transform {

/**
 * @brief Transform configuration constants
 * 
 * @section description Description
 * Defines common constants used throughout the transformation system.
 * 
 * @section constants Constants
 * - DEFAULT_BATCH_SIZE: Default batch size for processing
 * - MAX_VALIDATION_ERRORS: Maximum allowed validation errors
 * - NUMERIC_EPSILON: Epsilon for numeric comparisons
 * - DEFAULT_DATE_FORMAT: Default date format string
 * - DEFAULT_DATETIME_FORMAT: Default datetime format string
 * - DEFAULT_TIME_FORMAT: Default time format string
 */
namespace constants {
    constexpr size_t DEFAULT_BATCH_SIZE = 1000;  ///< Default batch size
    constexpr size_t MAX_VALIDATION_ERRORS = 100;  ///< Max validation errors
    constexpr double NUMERIC_EPSILON = 0.0001;  ///< Numeric comparison epsilon
    constexpr char DEFAULT_DATE_FORMAT[] = "%Y-%m-%d";  ///< Default date format
    constexpr char DEFAULT_DATETIME_FORMAT[] = "%Y-%m-%d %H:%M:%S";  ///< Default datetime format
    constexpr char DEFAULT_TIME_FORMAT[] = "%H:%M:%S";  ///< Default time format
}

/**
 * @brief Common transformation utilities
 * 
 * @section description Description
 * Provides utility functions for common transformation operations
 * such as date parsing, string normalization, and numeric conversions.
 * 
 * @section features Features
 * - Date/time handling
 * - String manipulation
 * - Numeric conversions
 * - Pattern matching
 * - Unit conversions
 */
class TransformationUtils {
public:
    /**
     * @brief Parse date string with multiple format attempts
     * @param date_str Date string to parse
     * @param formats Vector of format strings to try
     * @return std::optional<std::chrono::system_clock::time_point> Parsed date
     * 
     * @section details Implementation Details
     * - Tries each format in sequence
     * - Returns first successful parse
     * - Thread-safe
     */
    static std::optional<std::chrono::system_clock::time_point> parse_date(
        const std::string& date_str,
        const std::vector<std::string>& formats);

    /**
     * @brief Format date to string
     * @param time_point Time point to format
     * @param format Format string
     * @return std::string Formatted date string
     * 
     * @section details Implementation Details
     * - Uses strftime formatting
     * - Thread-safe
     */
    static std::string format_date(
        const std::chrono::system_clock::time_point& time_point,
        const std::string& format);

    /**
     * @brief Convert numeric value with unit conversion
     * @param value Input value
     * @param from_unit Source unit
     * @param to_unit Target unit
     * @return double Converted value
     * 
     * @section details Implementation Details
     * - Uses conversion factors
     * - Thread-safe
     */
    static double convert_units(
        double value,
        const std::string& from_unit,
        const std::string& to_unit);

    /**
     * @brief Normalize string value
     * @param value Input string
     * @param case_sensitive Whether to preserve case
     * @param trim_whitespace Whether to trim whitespace
     * @return std::string Normalized string
     * 
     * @section details Implementation Details
     * - Case conversion
     * - Whitespace handling
     * - Thread-safe
     */
    static std::string normalize_string(
        const std::string& value,
        bool case_sensitive = false,
        bool trim_whitespace = true);

    /**
     * @brief Validate numeric range
     * @param value Value to validate
     * @param min_value Minimum allowed value
     * @param max_value Maximum allowed value
     * @return bool True if within range
     * 
     * @section details Implementation Details
     * - Range checking
     * - Optional bounds
     * - Thread-safe
     */
    static bool validate_numeric_range(
        double value,
        std::optional<double> min_value,
        std::optional<double> max_value);

    /**
     * @brief Extract numeric value from string
     * @param str String containing numeric value
     * @param default_value Default if extraction fails
     * @return double Extracted value
     * 
     * @section details Implementation Details
     * - Regex extraction
     * - Default handling
     * - Thread-safe
     */
    static double extract_numeric(
        const std::string& str,
        double default_value = 0.0);

    /**
     * @brief Check if string matches pattern
     * @param value String to check
     * @param pattern Regex pattern
     * @return bool True if matches
     * 
     * @section details Implementation Details
     * - Regex matching
     * - Thread-safe
     */
    static bool matches_pattern(
        const std::string& value,
        const std::string& pattern);

    /**
     * @brief Split string by delimiter
     * @param str String to split
     * @param delimiter Delimiter character
     * @return std::vector<std::string> Split parts
     * 
     * @section details Implementation Details
     * - String splitting
     * - Empty handling
     * - Thread-safe
     */
    static std::vector<std::string> split_string(
        const std::string& str,
        char delimiter);

    /**
     * @brief Join strings with delimiter
     * @param parts String parts
     * @param delimiter Delimiter string
     * @return std::string Joined string
     * 
     * @section details Implementation Details
     * - String joining
     * - Empty handling
     * - Thread-safe
     */
    static std::string join_strings(
        const std::vector<std::string>& parts,
        const std::string& delimiter);

    /**
     * @brief Calculate age from birthdate
     * @param birthdate Birth date
     * @param reference_date Reference date (default: now)
     * @return int Age in years
     * 
     * @section details Implementation Details
     * - Age calculation
     * - Date handling
     * - Thread-safe
     */
    static int calculate_age(
        const std::chrono::system_clock::time_point& birthdate,
        const std::chrono::system_clock::time_point& reference_date = 
            std::chrono::system_clock::now());

    /**
     * @brief Calculate date difference
     * @param start_date Start date
     * @param end_date End date
     * @param unit Unit of difference (days, months, years)
     * @return int Difference in specified unit
     * 
     * @section details Implementation Details
     * - Date arithmetic
     * - Unit conversion
     * - Thread-safe
     */
    static int calculate_date_difference(
        const std::chrono::system_clock::time_point& start_date,
        const std::chrono::system_clock::time_point& end_date,
        const std::string& unit = "days");

private:
    static std::unordered_map<std::string, double> unit_conversion_factors_;  ///< Unit conversion factors
};

/**
 * @brief Transformation result with metadata
 * 
 * @section description Description
 * Structure for holding transformation results with success status,
 * warnings, errors, and additional metadata.
 * 
 * @section features Features
 * - Success tracking
 * - Warning collection
 * - Error handling
 * - Metadata storage
 */
struct TransformationResult {
    std::any value;  ///< Transformed value
    bool success{true};  ///< Success flag
    std::vector<std::string> warnings;  ///< Warning messages
    std::optional<std::string> error_message;  ///< Error message
    std::unordered_map<std::string, std::any> metadata;  ///< Additional metadata

    /**
     * @brief Check if transformation succeeded
     * @return bool True if successful
     * 
     * @section details Implementation Details
     * - Checks success flag
     * - Verifies no error
     * - Thread-safe
     */
    bool is_success() const { return success && !error_message.has_value(); }

    /**
     * @brief Add warning message
     * @param warning Warning message
     * 
     * @section details Implementation Details
     * - Adds warning
     * - Thread-safe
     */
    void add_warning(const std::string& warning) {
        warnings.push_back(warning);
    }

    /**
     * @brief Set error state
     * @param error Error message
     * 
     * @section details Implementation Details
     * - Sets error
     * - Updates success
     * - Thread-safe
     */
    void set_error(const std::string& error) {
        success = false;
        error_message = error;
    }
};

/**
 * @brief Base class for complex transformations
 * 
 * @section description Description
 * Abstract base class for implementing complex transformations
 * with detailed result handling.
 * 
 * @section features Features
 * - Detailed results
 * - Error handling
 * - Context awareness
 * - Standard interface
 */
class ComplexTransformation : public FieldTransformation {
public:
    /**
     * @brief Transform with detailed result
     * @param input Input value
     * @param context Processing context
     * @return TransformationResult Detailed result
     * 
     * @section details Implementation Details
     * - Performs transformation
     * - Collects metadata
     * - Thread-safe
     */
    virtual TransformationResult transform_detailed(
        const std::any& input,
        core::ProcessingContext& context) = 0;

    /**
     * @brief Standard transform implementation
     * @param input Input value
     * @param context Processing context
     * @return std::any Transformed value
     * 
     * @section details Implementation Details
     * - Uses detailed transform
     * - Handles errors
     * - Thread-safe
     */
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override {
        auto result = transform_detailed(input, context);
        if (!result.is_success()) {
            throw common::TransformationException(
                result.error_message.value_or("Unknown transformation error"),
                get_type(), "transform");
        }
        return result.value;
    }
};

/**
 * @brief Registry for custom transformations
 * 
 * @section description Description
 * Singleton registry for managing custom transformation types
 * and their factory functions.
 * 
 * @section features Features
 * - Type registration
 * - Factory management
 * - Singleton access
 * - Thread safety
 */
class TransformationRegistry {
public:
    /**
     * @brief Get singleton instance
     * @return TransformationRegistry& Registry instance
     * 
     * @section details Implementation Details
     * - Returns singleton
     * - Thread-safe
     */
    static TransformationRegistry& instance() {
        static TransformationRegistry instance;
        return instance;
    }

    /**
     * @brief Register transformation factory
     * @param type_name Transformation type name
     * @param factory Factory function
     * 
     * @section details Implementation Details
     * - Registers factory
     * - Thread-safe
     */
    void register_transformation(
        const std::string& type_name,
        std::function<std::unique_ptr<FieldTransformation>(
            const std::unordered_map<std::string, std::any>&)> factory);

    /**
     * @brief Check if transformation type exists
     * @param type_name Transformation type name
     * @return bool True if exists
     * 
     * @section details Implementation Details
     * - Checks registry
     * - Thread-safe
     */
    bool has_transformation(const std::string& type_name) const;

private:
    std::unordered_map<std::string, 
        std::function<std::unique_ptr<FieldTransformation>(
            const std::unordered_map<std::string, std::any>&)>> factories_;  ///< Factory functions
};

} // namespace omop::transform

File src/lib/transform/field_transformations.h:

/**
 * @file field_transformations.h
 * @brief Field transformation definitions for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header provides field-level transformation utilities and helper classes
 * for the transform module. It defines structures and classes for managing
 * field mappings, transformation chains, and batch field transformations.
 * 
 * @section design Design Principles
 * - Fluent Interface: Builder pattern for field mappings
 * - Chain of Responsibility: Sequential transformations
 * - Batch Processing: Efficient field transformations
 * - Error Handling: Robust error management
 * - Performance: Caching and optimization
 * 
 * @section components Components
 * - FieldMapping: Field mapping metadata
 * - TransformationChain: Sequential transformations
 * - FieldTransformationBuilder: Fluent builder interface
 * - BatchFieldTransformer: Batch field processing
 * 
 * @section usage Usage
 * To use the field transformation system:
 * 1. Create field mappings
 * 2. Build transformation chains
 * 3. Apply transformations
 * 4. Handle results
 * 
 * @section example Example
 * @code
 * auto mapping = FieldTransformationBuilder()
 *     .from_field("source_field")
 *     .to_field("target_field")
 *     .with_transformation("date_transform")
 *     .required()
 *     .build();
 * @endcode
 */

#pragma once

/**
 * @brief Field mapping information
 * 
 * @section description Description
 * Contains metadata about how a field should be mapped from source to target,
 * including transformation rules, validation requirements, and default values.
 * 
 * @section features Features
 * - Source/target mapping
 * - Transformation rules
 * - Validation rules
 * - Default values
 * - Required flags
 */
struct FieldMapping {
    std::string source_field;  ///< Source field name
    std::string target_field;  ///< Target field name
    std::string transformation_type;  ///< Transformation type
    YAML::Node transformation_params;  ///< Transformation parameters
    bool is_required{false};  ///< Required flag
    std::string default_value;  ///< Default value
    std::vector<std::string> validation_rules;  ///< Validation rules
};

/**
 * @brief Transformation chain
 * 
 * @section description Description
 * Represents a sequence of transformations to apply to a field,
 * executed in order with error handling and logging.
 * 
 * @section features Features
 * - Sequential execution
 * - Error handling
 * - Logging
 * - Chain management
 */
class TransformationChain {
public:
    /**
     * @brief Add transformation to the chain
     * @param transformation Transformation to add
     * 
     * @section details Implementation Details
     * - Adds transformation
     * - Thread-safe
     */
    void add_transformation(std::unique_ptr<FieldTransformation> transformation) {
        transformations_.push_back(std::move(transformation));
    }

    /**
     * @brief Apply all transformations in sequence
     * @param input Input value
     * @param context Processing context
     * @return std::any Transformed value
     * 
     * @section details Implementation Details
     * - Applies transformations
     * - Handles errors
     * - Thread-safe
     */
    std::any apply(const std::any& input, core::ProcessingContext& context) {
        std::any result = input;
        
        for (const auto& transformation : transformations_) {
            try {
                result = transformation->transform(result, context);
            } catch (const std::exception& e) {
                context.log("error", 
                    std::format("Transformation {} failed: {}", 
                              transformation->get_type(), e.what()));
                throw;
            }
        }
        
        return result;
    }

    /**
     * @brief Get number of transformations in chain
     * @return size_t Number of transformations
     * 
     * @section details Implementation Details
     * - Returns count
     * - Thread-safe
     */
    size_t size() const { return transformations_.size(); }

    /**
     * @brief Check if chain is empty
     * @return bool True if empty
     * 
     * @section details Implementation Details
     * - Checks emptiness
     * - Thread-safe
     */
    bool empty() const { return transformations_.empty(); }

    /**
     * @brief Clear all transformations
     * 
     * @section details Implementation Details
     * - Clears chain
     * - Thread-safe
     */
    void clear() { transformations_.clear(); }

private:
    std::vector<std::unique_ptr<FieldTransformation>> transformations_;  ///< Transformation list
};

/**
 * @brief Field transformation builder
 * 
 * @section description Description
 * Fluent interface for building field transformations with a
 * chainable API for configuring field mappings.
 * 
 * @section features Features
 * - Fluent interface
 * - Field configuration
 * - Transformation setup
 * - Validation rules
 * - Default values
 */
class FieldTransformationBuilder {
public:
    /**
     * @brief Set source field
     * @param field Source field name
     * @return FieldTransformationBuilder& Builder reference
     * 
     * @section details Implementation Details
     * - Sets source
     * - Thread-safe
     */
    FieldTransformationBuilder& from_field(const std::string& field) {
        mapping_.source_field = field;
        return *this;
    }

    /**
     * @brief Set target field
     * @param field Target field name
     * @return FieldTransformationBuilder& Builder reference
     * 
     * @section details Implementation Details
     * - Sets target
     * - Thread-safe
     */
    FieldTransformationBuilder& to_field(const std::string& field) {
        mapping_.target_field = field;
        return *this;
    }

    /**
     * @brief Add transformation
     * @param type Transformation type
     * @param params Transformation parameters
     * @return FieldTransformationBuilder& Builder reference
     * 
     * @section details Implementation Details
     * - Sets type
     * - Sets params
     * - Thread-safe
     */
    FieldTransformationBuilder& with_transformation(const std::string& type,
                                                   const YAML::Node& params = {}) {
        mapping_.transformation_type = type;
        mapping_.transformation_params = params;
        return *this;
    }

    /**
     * @brief Mark field as required
     * @param required Whether field is required
     * @return FieldTransformationBuilder& Builder reference
     * 
     * @section details Implementation Details
     * - Sets required
     * - Thread-safe
     */
    FieldTransformationBuilder& required(bool required = true) {
        mapping_.is_required = required;
        return *this;
    }

    /**
     * @brief Set default value
     * @param value Default value
     * @return FieldTransformationBuilder& Builder reference
     * 
     * @section details Implementation Details
     * - Sets default
     * - Thread-safe
     */
    FieldTransformationBuilder& with_default(const std::string& value) {
        mapping_.default_value = value;
        return *this;
    }

    /**
     * @brief Add validation rule
     * @param rule Validation rule name
     * @return FieldTransformationBuilder& Builder reference
     * 
     * @section details Implementation Details
     * - Adds rule
     * - Thread-safe
     */
    FieldTransformationBuilder& validate_with(const std::string& rule) {
        mapping_.validation_rules.push_back(rule);
        return *this;
    }

    /**
     * @brief Build the field mapping
     * @return FieldMapping Completed field mapping
     * 
     * @section details Implementation Details
     * - Returns mapping
     * - Thread-safe
     */
    FieldMapping build() const {
        return mapping_;
    }

private:
    FieldMapping mapping_;  ///< Field mapping
};

/**
 * @brief Batch field transformer
 * 
 * @section description Description
 * Applies transformations to multiple fields in a record with
 * error handling, logging, and performance tracking.
 * 
 * @section features Features
 * - Batch processing
 * - Error handling
 * - Field copying
 * - Performance tracking
 * - Statistics collection
 */
class BatchFieldTransformer {
public:
    /**
     * @brief Add field mapping
     * @param mapping Field mapping to add
     * 
     * @section details Implementation Details
     * - Adds mapping
     * - Thread-safe
     */
    void add_mapping(const FieldMapping& mapping) {
        mappings_.push_back(mapping);
    }

    /**
     * @brief Transform all fields in a record
     * @param input_record Input record
     * @param context Processing context
     * @return core::Record Transformed record
     * 
     * @section details Implementation Details
     * - Transforms fields
     * - Handles errors
     * - Copies unmapped
     * - Thread-safe
     */
    core::Record transform(const core::Record& input_record,
                         core::ProcessingContext& context) {
        core::Record output_record;

        for (const auto& mapping : mappings_) {
            try {
                // Get source value
                auto source_value = input_record.getField(mapping.source_field);
                
                if (source_value.type() == typeid(void) && mapping.is_required) {
                    if (!mapping.default_value.empty()) {
                        source_value = mapping.default_value;
                    } else {
                        context.log("error", 
                            std::format("Required field '{}' is missing", 
                                      mapping.source_field));
                        continue;
                    }
                }

                if (source_value.type() != typeid(void)) {
                    // Apply transformation
                    auto& registry = TransformationRegistry::instance();
                    auto transformation = registry.create_transformation(
                        mapping.transformation_type);
                    
                    transformation->configure(mapping.transformation_params);
                    auto result = transformation->transform(source_value, context);
                    
                    // Set in output record
                    output_record.setField(mapping.target_field, result);
                }

            } catch (const std::exception& e) {
                context.log("error", 
                    std::format("Failed to transform field '{}': {}", 
                              mapping.source_field, e.what()));
                context.increment_errors();
            }
        }

        // Copy unmapped fields if configured
        if (copy_unmapped_fields_) {
            for (const auto& field_name : input_record.getFieldNames()) {
                if (!output_record.hasField(field_name)) {
                    auto value = input_record.getField(field_name);
                    if (value.type() != typeid(void)) {
                        output_record.setField(field_name, value);
                    }
                }
            }
        }

        return output_record;
    }

    /**
     * @brief Clear all mappings
     * 
     * @section details Implementation Details
     * - Clears mappings
     * - Thread-safe
     */
    void clear() {
        mappings_.clear();
    }

    /**
     * @brief Cache statistics
     * 
     * @section description Description
     * Tracks cache performance metrics.
     * 
     * @section fields Fields
     * - size: Cache size
     * - hits: Cache hits
     * - misses: Cache misses
     * - hit_rate: Hit rate
     */
    struct CacheStats {
        size_t size;  ///< Cache size
        size_t hits;  ///< Cache hits
        size_t misses;  ///< Cache misses
        double hit_rate;  ///< Hit rate
    };

    /**
     * @brief Get cache statistics
     * @return CacheStats Cache statistics
     * 
     * @section details Implementation Details
     * - Returns stats
     * - Thread-safe
     */
    CacheStats get_stats() const {
        return cache_stats_;
    }

    /**
     * @brief Record transformation execution
     * @param field_name Field name
     * @param transformation_type Transformation type
     * @param duration Execution duration
     * @param success Success flag
     * 
     * @section details Implementation Details
     * - Records metrics
     * - Thread-safe
     */
    void record_execution(const std::string& field_name,
                         const std::string& transformation_type,
                         std::chrono::duration<double> duration,
                         bool success) {
        std::lock_guard<std::mutex> lock(mutex_);
        // Record metrics
    }

    /**
     * @brief Field statistics
     * 
     * @section description Description
     * Tracks field-level performance metrics.
     * 
     * @section fields Fields
     * - total_count: Total transformations
     * - success_count: Successful transformations
     * - error_count: Failed transformations
     * - total_duration: Total duration
     */
    struct FieldStats {
        size_t total_count{0};  ///< Total transformations
        size_t success_count{0};  ///< Successful transformations
        size_t error_count{0};  ///< Failed transformations
        std::chrono::duration<double> total_duration{0};  ///< Total duration

        /**
         * @brief Calculate average duration
         * @return double Average duration
         * 
         * @section details Implementation Details
         * - Calculates average
         * - Thread-safe
         */
        double average_duration() const {
            return total_count > 0 ? 
                total_duration.count() / total_count : 0.0;
        }

        /**
         * @brief Calculate success rate
         * @return double Success rate
         * 
         * @section details Implementation Details
         * - Calculates rate
         * - Thread-safe
         */
        double success_rate() const {
            return total_count > 0 ? 
                static_cast<double>(success_count) / total_count : 0.0;
        }
    };

    /**
     * @brief Get field statistics
     * @param field_name Field name
     * @return FieldStats Field statistics
     * 
     * @section details Implementation Details
     * - Returns stats
     * - Thread-safe
     */
    FieldStats get_field_stats(const std::string& field_name) const {
        std::lock_guard<std::mutex> lock(mutex_);
        return field_metrics_.at(field_name);
    }

    /**
     * @brief Get transformation statistics
     * @param transformation_type Transformation type
     * @return FieldStats Transformation statistics
     * 
     * @section details Implementation Details
     * - Returns stats
     * - Thread-safe
     */
    FieldStats get_transformation_stats(const std::string& transformation_type) const {
        std::lock_guard<std::mutex> lock(mutex_);
        return transformation_metrics_.at(transformation_type);
    }

    /**
     * @brief Get all field names
     * @return std::vector<std::string> Field names
     * 
     * @section details Implementation Details
     * - Returns names
     * - Thread-safe
     */
    std::vector<std::string> get_field_names() const {
        std::lock_guard<std::mutex> lock(mutex_);
        std::vector<std::string> names;
        for (const auto& [name, _] : field_metrics_) {
            names.push_back(name);
        }
        return names;
    }

    /**
     * @brief Reset all statistics
     * 
     * @section details Implementation Details
     * - Clears stats
     * - Thread-safe
     */
    void reset() {
        std::lock_guard<std::mutex> lock(mutex_);
        field_metrics_.clear();
        transformation_metrics_.clear();
        cache_stats_ = CacheStats{};
    }

private:
    std::vector<FieldMapping> mappings_;  ///< Field mappings
    bool copy_unmapped_fields_{false};  ///< Copy unmapped flag
    std::unordered_map<std::string, FieldStats> field_metrics_;  ///< Field metrics
    std::unordered_map<std::string, FieldStats> transformation_metrics_;  ///< Transformation metrics
    CacheStats cache_stats_;  ///< Cache statistics
    mutable std::mutex mutex_;  ///< Mutex for thread safety
};

} // namespace omop::transform

File src/lib/transform/transformation_engine.h:

/**
 * @file transformation_engine.h
 * @brief Core transformation engine and field transformation interfaces
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * @version 1.0
 * 
 * @section overview Overview
 * This header defines the core transformation engine and field transformation
 * interfaces for the OMOP ETL pipeline. It provides a flexible framework for
 * implementing and applying various types of data transformations.
 * 
 * @section design Design Principles
 * - Extensibility: Custom transformation support
 * - Composability: Chain multiple transformations
 * - Validation: Input validation and error handling
 * - Configuration: Flexible parameter configuration
 * - Thread Safety: Safe concurrent transformations
 * 
 * @section components Components
 * - FieldTransformation: Base interface for field transformations
 * - DirectTransformation: No-op transformation
 * - DateTransformation: Date/time format conversion
 * - VocabularyTransformation: Concept mapping
 * - NumericTransformation: Numeric operations
 * - StringConcatenationTransformation: String combination
 * - ConditionalTransformation: Rule-based transformation
 * - TransformationEngine: Main transformation orchestrator
 * 
 * @section usage Usage
 * To use the transformation system:
 * 1. Create transformation instances
 * 2. Configure transformations
 * 3. Apply transformations to data
 * 4. Handle transformation results
 * 
 * @section example Example
 * @code
 * auto engine = TransformationEngine::create_for_table("patients", config);
 * engine->initialize(config, context);
 * auto result = engine->transform_record(record, context);
 * @endcode
 */

#pragma once

#include "core/interfaces.h"
#include "common/configuration.h"
#include "common/exceptions.h"
#include <memory>
#include <string>
#include <unordered_map>
#include <functional>
#include <regex>
#include <chrono>

namespace omop::transform {

/**
 * @brief Base class for field transformations
 * 
 * @section description Description
 * This abstract class provides the interface for all field-level transformations
 * in the ETL pipeline. Concrete implementations handle specific transformation types.
 * 
 * @section features Features
 * - Value transformation
 * - Input validation
 * - Type identification
 * - Configuration support
 */
class FieldTransformation {
public:
    /**
     * @brief Virtual destructor
     * 
     * @section details Implementation Details
     * - Ensures cleanup
     * - Thread-safe
     */
    virtual ~FieldTransformation() = default;

    /**
     * @brief Apply transformation to a field value
     * @param input Input value
     * @param context Processing context
     * @return std::any Transformed value
     * 
     * @section details Implementation Details
     * - Transforms value
     * - Thread-safe
     */
    virtual std::any transform(const std::any& input,
                              core::ProcessingContext& context) = 0;

    /**
     * @brief Validate input value before transformation
     * @param input Input value
     * @return bool True if valid
     * 
     * @section details Implementation Details
     * - Validates input
     * - Thread-safe
     */
    virtual bool validate_input(const std::any& input) const = 0;

    /**
     * @brief Get transformation type name
     * @return std::string Type identifier
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Configure transformation with parameters
     * @param params Configuration parameters
     * 
     * @section details Implementation Details
     * - Configures transformation
     * - Thread-safe
     */
    virtual void configure(const YAML::Node& params) = 0;
};

/**
 * @brief Direct field mapping (no transformation)
 * 
 * @section description Description
 * Implements a pass-through transformation that returns the input value
 * unchanged. Useful for direct field mappings.
 * 
 * @section features Features
 * - No transformation
 * - Input validation
 * - Simple configuration
 */
class DirectTransformation : public FieldTransformation {
public:
    /**
     * @brief Transform implementation
     * @param input Input value
     * @param context Processing context
     * @return std::any Input value unchanged
     * 
     * @section details Implementation Details
     * - Returns input
     * - Thread-safe
     */
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override {
        return input;
    }

    /**
     * @brief Validate input
     * @param input Input value
     * @return bool True if input has value
     * 
     * @section details Implementation Details
     * - Checks value
     * - Thread-safe
     */
    bool validate_input(const std::any& input) const override {
        return input.has_value();
    }

    /**
     * @brief Get type
     * @return std::string "direct"
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "direct"; }

    /**
     * @brief Configure transformation
     * @param params Configuration parameters
     * 
     * @section details Implementation Details
     * - No configuration needed
     * - Thread-safe
     */
    void configure(const YAML::Node& params) override {
        // No configuration needed for direct mapping
    }
};

/**
 * @brief Date format transformation
 * 
 * @section description Description
 * Converts date/time values between different formats and handles
 * timezone conversions for OMOP CDM compliance.
 * 
 * @section features Features
 * - Format conversion
 * - Timezone handling
 * - Default time
 * - Format validation
 */
class DateTransformation : public FieldTransformation {
public:
    /**
     * @brief Constructor
     * 
     * @section details Implementation Details
     * - Initializes defaults
     * - Thread-safe
     */
    DateTransformation() = default;

    /**
     * @brief Transform implementation
     * @param input Input value
     * @param context Processing context
     * @return std::any Transformed date
     * 
     * @section details Implementation Details
     * - Converts format
     * - Handles timezone
     * - Thread-safe
     */
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    /**
     * @brief Validate input
     * @param input Input value
     * @return bool True if valid date
     * 
     * @section details Implementation Details
     * - Validates format
     * - Thread-safe
     */
    bool validate_input(const std::any& input) const override;

    /**
     * @brief Get type
     * @return std::string "date_transform"
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "date_transform"; }

    /**
     * @brief Configure transformation
     * @param params Configuration parameters
     * 
     * @section details Implementation Details
     * - Sets formats
     * - Sets timezone
     * - Thread-safe
     */
    void configure(const YAML::Node& params) override;

private:
    std::string input_format_{"%Y-%m-%d"};  ///< Input date format
    std::string output_format_{"%Y-%m-%d %H:%M:%S"};  ///< Output date format
    std::string timezone_{"UTC"};  ///< Timezone
    bool add_time_{false};  ///< Add time flag
    std::string default_time_{"00:00:00"};  ///< Default time
};

/**
 * @brief Vocabulary mapping transformation
 * 
 * @section description Description
 * Maps source values to OMOP concept IDs using vocabulary lookups.
 * 
 * @section features Features
 * - Concept mapping
 * - Vocabulary lookup
 * - Default handling
 * - Case sensitivity
 */
class VocabularyTransformation : public FieldTransformation {
public:
    /**
     * @brief Constructor
     * @param vocabulary_service Reference to vocabulary service
     * 
     * @section details Implementation Details
     * - Stores service
     * - Thread-safe
     */
    explicit VocabularyTransformation(class VocabularyService& vocabulary_service);

    /**
     * @brief Transform implementation
     * @param input Input value
     * @param context Processing context
     * @return std::any Mapped concept ID
     * 
     * @section details Implementation Details
     * - Looks up concept
     * - Handles defaults
     * - Thread-safe
     */
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    /**
     * @brief Validate input
     * @param input Input value
     * @return bool True if valid
     * 
     * @section details Implementation Details
     * - Validates input
     * - Thread-safe
     */
    bool validate_input(const std::any& input) const override;

    /**
     * @brief Get type
     * @return std::string "vocabulary_mapping"
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "vocabulary_mapping"; }

    /**
     * @brief Configure transformation
     * @param params Configuration parameters
     * 
     * @section details Implementation Details
     * - Sets vocabularies
     * - Sets defaults
     * - Thread-safe
     */
    void configure(const YAML::Node& params) override;

private:
    VocabularyService& vocabulary_service_;  ///< Vocabulary service
    std::string vocabulary_name_;  ///< Vocabulary name
    std::string source_vocabulary_;  ///< Source vocabulary
    std::string target_vocabulary_{"OMOP"};  ///< Target vocabulary
    int default_concept_id_{0};  ///< Default concept ID
    bool case_sensitive_{false};  ///< Case sensitivity flag
};

/**
 * @brief Numeric value transformation
 * 
 * @section description Description
 * Handles numeric conversions, unit conversions, and calculations.
 * 
 * @section features Features
 * - Arithmetic operations
 * - Unit conversion
 * - Range validation
 * - Precision control
 */
class NumericTransformation : public FieldTransformation {
public:
    /**
     * @brief Operation types
     */
    enum class Operation {
        None,      ///< No operation
        Multiply,  ///< Multiplication
        Divide,    ///< Division
        Add,       ///< Addition
        Subtract,  ///< Subtraction
        Round,     ///< Rounding
        Floor,     ///< Floor
        Ceiling,   ///< Ceiling
        Absolute   ///< Absolute value
    };

    /**
     * @brief Transform implementation
     * @param input Input value
     * @param context Processing context
     * @return std::any Transformed number
     * 
     * @section details Implementation Details
     * - Applies operation
     * - Converts units
     * - Thread-safe
     */
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    /**
     * @brief Validate input
     * @param input Input value
     * @return bool True if valid number
     * 
     * @section details Implementation Details
     * - Validates number
     * - Thread-safe
     */
    bool validate_input(const std::any& input) const override;

    /**
     * @brief Get type
     * @return std::string "numeric_transform"
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "numeric_transform"; }

    /**
     * @brief Configure transformation
     * @param params Configuration parameters
     * 
     * @section details Implementation Details
     * - Sets operation
     * - Sets ranges
     * - Thread-safe
     */
    void configure(const YAML::Node& params) override;

private:
    Operation operation_{Operation::None};  ///< Operation type
    double operand_{1.0};  ///< Operation operand
    int precision_{2};  ///< Decimal precision
    std::optional<double> min_value_;  ///< Minimum value
    std::optional<double> max_value_;  ///< Maximum value
    std::string unit_conversion_;  ///< Unit conversion
};

/**
 * @brief String concatenation transformation
 * 
 * @section description Description
 * Combines multiple fields into a single string value.
 * 
 * @section features Features
 * - Field combination
 * - Separator control
 * - Empty handling
 * - Prefix/suffix
 */
class StringConcatenationTransformation : public FieldTransformation {
public:
    /**
     * @brief Transform implementation
     * @param input Input value
     * @param context Processing context
     * @return std::any Concatenated string
     * 
     * @section details Implementation Details
     * - Combines fields
     * - Thread-safe
     */
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    /**
     * @brief Validate input
     * @param input Input value
     * @return bool True if valid
     * 
     * @section details Implementation Details
     * - Validates input
     * - Thread-safe
     */
    bool validate_input(const std::any& input) const override;

    /**
     * @brief Get type
     * @return std::string "string_concatenation"
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "string_concatenation"; }

    /**
     * @brief Configure transformation
     * @param params Configuration parameters
     * 
     * @section details Implementation Details
     * - Sets fields
     * - Sets options
     * - Thread-safe
     */
    void configure(const YAML::Node& params) override;

    /**
     * @brief Set source fields for concatenation
     * @param fields Vector of field names
     * 
     * @section details Implementation Details
     * - Sets fields
     * - Thread-safe
     */
    void set_source_fields(const std::vector<std::string>& fields) {
        source_fields_ = fields;
    }

    /**
     * @brief Transform multiple values
     * @param values Map of field names to values
     * @param context Processing context
     * @return std::any Concatenated string
     * 
     * @section details Implementation Details
     * - Combines values
     * - Thread-safe
     */
    std::any transform_multiple(const std::unordered_map<std::string, std::any>& values,
                               core::ProcessingContext& context);

private:
    std::vector<std::string> source_fields_;  ///< Source fields
    std::string separator_{" "};  ///< Field separator
    bool skip_empty_{true};  ///< Skip empty flag
    std::string prefix_;  ///< String prefix
    std::string suffix_;  ///< String suffix
};

/**
 * @brief Conditional transformation based on rules
 * 
 * @section description Description
 * Applies different transformations based on conditions.
 * 
 * @section features Features
 * - Rule evaluation
 * - Multiple conditions
 * - Default handling
 * - Value comparison
 */
class ConditionalTransformation : public FieldTransformation {
public:
    /**
     * @brief Condition structure
     */
    struct Condition {
        std::string field;  ///< Field name
        std::string operator_type;  ///< Operator type
        std::any value;  ///< Comparison value
        std::string then_value;  ///< Value if true
        std::optional<std::string> else_value;  ///< Value if false
    };

    /**
     * @brief Transform implementation
     * @param input Input value
     * @param context Processing context
     * @return std::any Conditional result
     * 
     * @section details Implementation Details
     * - Evaluates conditions
     * - Returns result
     * - Thread-safe
     */
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    /**
     * @brief Validate input
     * @param input Input value
     * @return bool True if valid
     * 
     * @section details Implementation Details
     * - Validates input
     * - Thread-safe
     */
    bool validate_input(const std::any& input) const override;

    /**
     * @brief Get type
     * @return std::string "conditional"
     * 
     * @section details Implementation Details
     * - Returns type
     * - Thread-safe
     */
    std::string get_type() const override { return "conditional"; }

    /**
     * @brief Configure transformation
     * @param params Configuration parameters
     * 
     * @section details Implementation Details
     * - Sets conditions
     * - Sets defaults
     * - Thread-safe
     */
    void configure(const YAML::Node& params) override;

private:
    std::vector<Condition> conditions_;  ///< Condition list
    std::string default_value_;  ///< Default value

    /**
     * @brief Evaluate condition
     * @param condition Condition to evaluate
     * @param value Value to check
     * @return bool True if condition met
     * 
     * @section details Implementation Details
     * - Evaluates condition
     * - Thread-safe
     */
    bool evaluate_condition(const Condition& condition,
                          const std::any& value) const;
};

/**
 * @brief Main transformation engine
 * 
 * @section description Description
 * Orchestrates the application of transformations to records and fields.
 * 
 * @section features Features
 * - Record transformation
 * - Field mapping
 * - Filter application
 * - Error handling
 */
class TransformationEngine : public core::ITransformer {
public:
    /**
     * @brief Initialize engine
     * @param config Configuration
     * @param context Processing context
     * 
     * @section details Implementation Details
     * - Sets up engine
     * - Thread-safe
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Register transformation
     * @param type Transformation type
     * @param factory Factory function
     * 
     * @section details Implementation Details
     * - Registers type
     * - Thread-safe
     */
    void register_transformation(const std::string& type,
        std::function<std::unique_ptr<FieldTransformation>()> factory);

    /**
     * @brief Apply filters
     * @param record Record to filter
     * @param filters Filter string
     * @return bool True if passes filters
     * 
     * @section details Implementation Details
     * - Evaluates filters
     * - Thread-safe
     */
    bool apply_filters(const core::Record& record,
                      const std::string& filters) const;

    /**
     * @brief Create engine for table
     * @param table_name Table name
     * @param config Configuration
     * @return std::unique_ptr<TransformationEngine> Engine instance
     * 
     * @section details Implementation Details
     * - Creates engine
     * - Thread-safe
     */
    static std::unique_ptr<TransformationEngine> create_for_table(
        const std::string& table_name,
        const common::ConfigurationManager& config);

    /**
     * @brief Register engine factory
     * @param table_name Table name
     * @param factory Factory function
     * 
     * @section details Implementation Details
     * - Registers factory
     * - Thread-safe
     */
    static void register_engine(const std::string& table_name,
        std::function<std::unique_ptr<TransformationEngine>()> factory);

private:
    std::unordered_map<std::string, 
        std::function<std::unique_ptr<FieldTransformation>()>> factories_;  ///< Transformation factories
};

} // namespace omop::transform

