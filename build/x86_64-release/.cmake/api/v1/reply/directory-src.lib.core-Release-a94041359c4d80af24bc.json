{"backtraceGraph": {"commands": ["install"], "files": ["src/lib/core/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 71, "parent": 0}, {"command": 0, "file": 0, "line": 78, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["lib/libomop_core.a"], "targetId": "omop_core::@a0f0ea145c6a42f32922", "targetIndex": 4, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "destination": "include/omop/core", "paths": ["src/lib/core/interfaces.h", "src/lib/core/job_manager.h", "src/lib/core/job_scheduler.h", "src/lib/core/pipeline.h", "src/lib/core/record.h"], "type": "file"}], "paths": {"build": "src/lib/core", "source": "src/lib/core"}}