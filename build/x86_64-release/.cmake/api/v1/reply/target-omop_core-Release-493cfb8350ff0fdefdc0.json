{"archive": {}, "artifacts": [{"path": "lib/libomop_core.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "target_compile_options", "include_directories", "target_include_directories", "target_compile_features"], "files": ["src/lib/core/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 25, "parent": 0}, {"command": 1, "file": 0, "line": 68, "parent": 0}, {"command": 2, "file": 0, "line": 42, "parent": 0}, {"command": 3, "file": 0, "line": 61, "parent": 0}, {"file": 1}, {"command": 4, "file": 1, "line": 489, "parent": 5}, {"command": 4, "file": 0, "line": 20, "parent": 0}, {"command": 5, "file": 0, "line": 29, "parent": 0}, {"command": 6, "file": 0, "line": 55, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden"}, {"backtrace": 4, "fragment": "-Wall"}, {"backtrace": 4, "fragment": "-Wextra"}, {"backtrace": 4, "fragment": "-Wpedantic"}], "defines": [{"backtrace": 3, "define": "FMT_SHARED"}, {"backtrace": 3, "define": "SPDLOG_COMPILED_LIB"}, {"backtrace": 3, "define": "SPDLOG_SHARED_LIB"}], "includes": [{"backtrace": 6, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib"}, {"backtrace": 6, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include"}, {"backtrace": 6, "path": "/opt/homebrew/opt/postgresql@15/include"}, {"backtrace": 6, "path": "/opt/homebrew/opt/postgresql@15/include/postgresql/server"}, {"backtrace": 6, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src"}, {"backtrace": 7, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/core"}, {"backtrace": 7, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/common"}, {"backtrace": 8, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.."}, {"backtrace": 8, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include"}, {"backtrace": 3, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.."}, {"backtrace": 3, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlo<PERSON>_json-src/include"}, {"backtrace": 3, "isSystem": true, "path": "/opt/homebrew/include"}], "language": "CXX", "languageStandard": {"backtraces": [9, 3], "standard": "20"}, "sourceIndexes": [0, 1, 2, 3, 4, 5]}], "dependencies": [{"backtrace": 3, "id": "fmt::@976f4f0bee90b99ecdb6"}, {"backtrace": 3, "id": "spdlog::@eb35cfce7893ccfeff1e"}, {"backtrace": 3, "id": "omop_common::@a72075f8f48ba5338622"}], "id": "omop_core::@a0f0ea145c6a42f32922", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "/Users/<USER>/uclwork/etl/omop-etl/install/x86_64-release"}}, "name": "omop_core", "nameOnDisk": "libomop_core.a", "paths": {"build": "src/lib/core", "source": "src/lib/core"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [6, 7, 8, 9, 10]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/core/component_factory.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/core/interfaces.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/core/job_manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/core/job_scheduler.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/core/pipeline.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/core/record.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/lib/core/interfaces.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/lib/core/job_manager.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/lib/core/job_scheduler.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/lib/core/pipeline.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/lib/core/record.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}