{"backtraceGraph": {"commands": ["install", "include"], "files": ["src/lib/cdm/sql/schema_config.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 472, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 44, "parent": 2}, {"command": 0, "file": 1, "line": 516, "parent": 0}, {"command": 0, "file": 1, "line": 522, "parent": 0}, {"command": 0, "file": 1, "line": 559, "parent": 0}]}, "installers": [{"backtrace": 3, "component": "Unspecified", "destination": "/omop-etl/sql", "paths": ["build/x86_64-release/create_tables.sql", "build/x86_64-release/create_indexes.sql", "build/x86_64-release/create_constraints.sql", "build/x86_64-release/create_provider_care_site.sql", "build/x86_64-release/create_location.sql"], "type": "file"}, {"backtrace": 4, "component": "Unspecified", "destination": "etc/omop-etl", "paths": [{"from": "config", "to": "."}], "type": "directory"}, {"backtrace": 5, "component": "Unspecified", "destination": "share/omop-etl/sql", "paths": [{"from": "build/x86_64-release/src/lib/cdm/sql", "to": "."}], "type": "directory"}, {"backtrace": 6, "component": "Unspecified", "destination": "lib/cmake/omop", "paths": ["build/x86_64-release/omop-config.cmake", "build/x86_64-release/omop-config-version.cmake"], "type": "file"}], "paths": {"build": ".", "source": "."}}