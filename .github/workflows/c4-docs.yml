name: C4 Documentation Validation

on:
  push:
    paths:
      - 'src/**'
      - 'docs/**'
      - '.github/workflows/c4-docs.yml'
  pull_request:
    paths:
      - 'src/**'
      - 'docs/**'
      - '.github/workflows/c4-docs.yml'

jobs:
  validate-docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.x'
      
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install markdownlint
          pip install pymarkdown
      
      - name: Make scripts executable
        run: |
          chmod +x scripts/validate_c4_docs.sh
          chmod +x scripts/generate_c4_docs.sh
      
      - name: Validate C4 Documentation
        run: ./scripts/validate_c4_docs.sh --strict
      
      - name: Generate C4 Documentation
        run: ./scripts/generate_c4_docs.sh --generate
      
      - name: Update Traceability Matrix
        run: ./scripts/generate_c4_docs.sh --update
      
      - name: Check for documentation changes
        run: |
          if [[ -n $(git status -s) ]]; then
            echo "Documentation changes detected. Please commit the changes."
            git status
            exit 1
          fi
      
      - name: Upload generated documentation
        if: always()
        uses: actions/upload-artifact@v2
        with:
          name: c4-documentation
          path: docs/generated/
      
      - name: Create documentation report
        if: always()
        run: |
          echo "## C4 Documentation Validation Report" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Validation Results" >> $GITHUB_STEP_SUMMARY
          ./scripts/validate_c4_docs.sh >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Generated Documentation" >> $GITHUB_STEP_SUMMARY
          echo "Documentation has been generated and uploaded as an artifact." >> $GITHUB_STEP_SUMMARY 