#!/bin/bash

# Script to validate C4 documentation
# Usage: ./validate_c4_docs.sh [--strict]

set -e

# Configuration
SRC_DIR="src/lib"
DOCS_DIR="docs/omop-etl"
TEMPLATES_DIR="docs/templates"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Validation rules
declare -A REQUIRED_TAGS=(
    ["@c4component"]="Component definition"
    ["@c4name"]="Component name"
    ["@c4description"]="Component description"
    ["@c4responsibilities"]="Component responsibilities"
)

declare -A REQUIRED_SECTIONS=(
    ["## Overview"]="Overview section"
    ["## Components"]="Components section"
    ["## Configuration"]="Configuration section"
)

# Function to check for required tags
check_required_tags() {
    local file=$1
    local missing_tags=0
    
    for tag in "${!REQUIRED_TAGS[@]}"; do
        if ! grep -q "$tag" "$file"; then
            echo -e "${RED}Missing $tag (${REQUIRED_TAGS[$tag]}) in $file${NC}"
            missing_tags=$((missing_tags + 1))
        fi
    done
    
    return $missing_tags
}

# Function to check for required sections
check_required_sections() {
    local file=$1
    local missing_sections=0
    
    for section in "${!REQUIRED_SECTIONS[@]}"; do
        if ! grep -q "$section" "$file"; then
            echo -e "${RED}Missing $section (${REQUIRED_SECTIONS[$section]}) in $file${NC}"
            missing_sections=$((missing_sections + 1))
        fi
    done
    
    return $missing_sections
}

# Function to validate component documentation
validate_component_docs() {
    local file=$1
    local errors=0
    
    # Check for required tags
    if ! check_required_tags "$file"; then
        errors=$((errors + 1))
    fi
    
    # Check for valid component name
    if grep -q "@c4name" "$file"; then
        local name=$(grep -A 1 "@c4name" "$file" | tail -n 1 | tr -d ' ')
        if [[ ! "$name" =~ ^[A-Z][a-zA-Z0-9]*$ ]]; then
            echo -e "${RED}Invalid component name format in $file: $name${NC}"
            errors=$((errors + 1))
        fi
    fi
    
    # Check for valid description
    if grep -q "@c4description" "$file"; then
        local desc=$(grep -A 1 "@c4description" "$file" | tail -n 1)
        if [ ${#desc} -lt 10 ]; then
            echo -e "${RED}Description too short in $file${NC}"
            errors=$((errors + 1))
        fi
    fi
    
    return $errors
}

# Function to validate markdown documentation
validate_markdown_docs() {
    local file=$1
    local errors=0
    
    # Check for required sections
    if ! check_required_sections "$file"; then
        errors=$((errors + 1))
    fi
    
    # Check for valid links
    if grep -q "\[.*\](" "$file"; then
        while read -r line; do
            if [[ ! "$line" =~ \[.*\]\(.*\) ]]; then
                echo -e "${RED}Invalid markdown link format in $file: $line${NC}"
                errors=$((errors + 1))
            fi
        done < <(grep "\[.*\](" "$file")
    fi
    
    return $errors
}

# Main validation
main() {
    local total_errors=0
    local strict_mode=false
    
    # Parse arguments
    if [ "$1" == "--strict" ]; then
        strict_mode=true
    fi
    
    echo -e "${YELLOW}Starting C4 documentation validation...${NC}"
    
    # Validate component documentation
    for file in $(find $SRC_DIR -name "*.h" -o -name "*.cpp"); do
        echo -e "${YELLOW}Validating $file...${NC}"
        if ! validate_component_docs "$file"; then
            total_errors=$((total_errors + 1))
        fi
    done
    
    # Validate markdown documentation
    for file in $(find $DOCS_DIR -name "*.md"); do
        echo -e "${YELLOW}Validating $file...${NC}"
        if ! validate_markdown_docs "$file"; then
            total_errors=$((total_errors + 1))
        fi
    done
    
    # Report results
    if [ $total_errors -eq 0 ]; then
        echo -e "${GREEN}C4 documentation validation passed!${NC}"
        exit 0
    else
        echo -e "${RED}C4 documentation validation failed with $total_errors errors${NC}"
        if [ "$strict_mode" = true ]; then
            exit 1
        else
            exit 0
        fi
    fi
}

# Run main validation
main "$@" 