#!/bin/bash

# Script to generate all documentation for the OMOP ETL project

# Exit on error
set -e

# Check if Doxygen is installed
if ! command -v doxygen &> /dev/null; then
    echo "Error: Doxygen is not installed"
    echo "Please install Doxygen to generate documentation"
    exit 1
fi

# Create documentation directories if they don't exist
mkdir -p docs/core/html
mkdir -p docs/common/html
mkdir -p docs/main/html

# Generate core library documentation
echo "Generating core library documentation..."
doxygen docs/core/Doxyfile

# Generate common library documentation
echo "Generating common library documentation..."
doxygen docs/common/Doxyfile

# Generate main project documentation
echo "Generating main project documentation..."
doxygen docs/main/Doxyfile

echo "Documentation generation complete!"
echo "Documentation can be found in:"
echo "  - Core library: docs/core/html/index.html"
echo "  - Common library: docs/common/html/index.html"
echo "  - Main project: docs/main/html/index.html" 