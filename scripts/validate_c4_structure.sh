#!/bin/bash

# Script to validate C4 documentation structure
# Usage: ./validate_c4_structure.sh [--strict]

set -e

# Configuration
SRC_DIR="src/lib"
DOCS_DIR="docs/omop-etl"
TEMPLATES_DIR="docs/templates"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Validation rules
declare -A REQUIRED_SECTIONS=(
    ["## Overview"]="Overview section"
    ["## Components"]="Components section"
    ["## Configuration"]="Configuration section"
    ["## Usage Examples"]="Usage examples section"
)

declare -A REQUIRED_TAGS=(
    ["@c4component"]="Component definition"
    ["@c4name"]="Component name"
    ["@c4description"]="Component description"
    ["@c4responsibilities"]="Component responsibilities"
    ["@c4dependencies"]="Component dependencies"
    ["@c4config"]="Configuration definition"
)

# Function to validate markdown structure
validate_markdown_structure() {
    local file=$1
    local errors=0
    
    # Check for required sections
    for section in "${!REQUIRED_SECTIONS[@]}"; do
        if ! grep -q "$section" "$file"; then
            echo -e "${RED}Missing $section (${REQUIRED_SECTIONS[$section]}) in $file${NC}"
            errors=$((errors + 1))
        fi
    done
    
    # Check for valid section order
    local last_line=0
    for section in "${!REQUIRED_SECTIONS[@]}"; do
        local current_line=$(grep -n "$section" "$file" | cut -d: -f1)
        if [ -n "$current_line" ] && [ "$current_line" -lt "$last_line" ]; then
            echo -e "${RED}Invalid section order in $file: $section should come after previous section${NC}"
            errors=$((errors + 1))
        fi
        last_line=$current_line
    done
    
    return $errors
}

# Function to validate code documentation
validate_code_documentation() {
    local file=$1
    local errors=0
    
    # Check for required tags
    for tag in "${!REQUIRED_TAGS[@]}"; do
        if ! grep -q "$tag" "$file"; then
            echo -e "${RED}Missing $tag (${REQUIRED_TAGS[$tag]}) in $file${NC}"
            errors=$((errors + 1))
        fi
    done
    
    # Check for valid tag order
    local last_line=0
    for tag in "${!REQUIRED_TAGS[@]}"; do
        local current_line=$(grep -n "$tag" "$file" | cut -d: -f1)
        if [ -n "$current_line" ] && [ "$current_line" -lt "$last_line" ]; then
            echo -e "${RED}Invalid tag order in $file: $tag should come after previous tag${NC}"
            errors=$((errors + 1))
        fi
        last_line=$current_line
    done
    
    return $errors
}

# Function to validate configuration documentation
validate_config_documentation() {
    local file=$1
    local errors=0
    
    # Check for required configuration tags
    if ! grep -q "@c4config" "$file"; then
        echo -e "${RED}Missing @c4config tag in $file${NC}"
        errors=$((errors + 1))
    fi
    
    # Check for field documentation
    if grep -q "^[[:space:]]*[a-zA-Z_][a-zA-Z0-9_]*:" "$file"; then
        while read -r line; do
            local field=$(echo "$line" | cut -d: -f1 | tr -d ' ')
            if ! grep -q "@c4field.*$field" "$file"; then
                echo -e "${RED}Missing @c4field documentation for $field in $file${NC}"
                errors=$((errors + 1))
            fi
        done < <(grep "^[[:space:]]*[a-zA-Z_][a-zA-Z0-9_]*:" "$file")
    fi
    
    return $errors
}

# Function to validate examples
validate_examples() {
    local file=$1
    local errors=0
    
    # Check for code examples
    if ! grep -q "```cpp" "$file"; then
        echo -e "${RED}Missing code examples in $file${NC}"
        errors=$((errors + 1))
    fi
    
    # Check for configuration examples
    if ! grep -q "```yaml" "$file"; then
        echo -e "${RED}Missing configuration examples in $file${NC}"
        errors=$((errors + 1))
    fi
    
    return $errors
}

# Main validation
main() {
    local total_errors=0
    local strict_mode=false
    
    # Parse arguments
    if [ "$1" == "--strict" ]; then
        strict_mode=true
    fi
    
    echo -e "${YELLOW}Starting C4 documentation structure validation...${NC}"
    
    # Validate markdown documentation
    for file in $(find $DOCS_DIR -name "*.md"); do
        echo -e "${YELLOW}Validating $file...${NC}"
        if ! validate_markdown_structure "$file"; then
            total_errors=$((total_errors + 1))
        fi
        if ! validate_examples "$file"; then
            total_errors=$((total_errors + 1))
        fi
    done
    
    # Validate code documentation
    for file in $(find $SRC_DIR -name "*.h" -o -name "*.cpp"); do
        echo -e "${YELLOW}Validating $file...${NC}"
        if ! validate_code_documentation "$file"; then
            total_errors=$((total_errors + 1))
        fi
    done
    
    # Validate configuration documentation
    for file in $(find $SRC_DIR -name "*.yaml" -o -name "*.yml"); do
        echo -e "${YELLOW}Validating $file...${NC}"
        if ! validate_config_documentation "$file"; then
            total_errors=$((total_errors + 1))
        fi
    done
    
    # Report results
    if [ $total_errors -eq 0 ]; then
        echo -e "${GREEN}C4 documentation structure validation passed!${NC}"
        exit 0
    else
        echo -e "${RED}C4 documentation structure validation failed with $total_errors errors${NC}"
        if [ "$strict_mode" = true ]; then
            exit 1
        else
            exit 0
        fi
    fi
}

# Run main validation
main "$@" 