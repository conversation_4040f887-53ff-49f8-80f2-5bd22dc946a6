#!/bin/bash

# Script to generate and validate C4 documentation
# Usage: ./generate_c4_docs.sh [--validate] [--generate] [--update]

set -e

# Configuration
DOCS_DIR="docs/omop-etl"
SRC_DIR="src/lib"
TEMPLATES_DIR="docs/templates"
OUTPUT_DIR="docs/generated"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Function to validate C4 documentation
validate_c4_docs() {
    echo -e "${YELLOW}Validating C4 documentation...${NC}"
    
    # Check for required C4 tags
    local missing_tags=0
    for file in $(find $SRC_DIR -name "*.h" -o -name "*.cpp"); do
        if ! grep -q "@c4component" "$file"; then
            echo -e "${RED}Missing @c4component tag in $file${NC}"
            missing_tags=$((missing_tags + 1))
        fi
    done
    
    # Check for required documentation sections
    local missing_sections=0
    for file in $(find $DOCS_DIR -name "*.md"); do
        if ! grep -q "## Overview" "$file"; then
            echo -e "${RED}Missing Overview section in $file${NC}"
            missing_sections=$((missing_sections + 1))
        fi
    done
    
    # Report validation results
    if [ $missing_tags -eq 0 ] && [ $missing_sections -eq 0 ]; then
        echo -e "${GREEN}C4 documentation validation passed!${NC}"
        return 0
    else
        echo -e "${RED}C4 documentation validation failed!${NC}"
        return 1
    fi
}

# Function to generate C4 documentation
generate_c4_docs() {
    echo -e "${YELLOW}Generating C4 documentation...${NC}"
    
    # Create output directory
    mkdir -p $OUTPUT_DIR
    
    # Generate component documentation
    for file in $(find $SRC_DIR -name "*.h" -o -name "*.cpp"); do
        local component_name=$(basename "$file" | cut -d. -f1)
        local output_file="$OUTPUT_DIR/${component_name}_c4.md"
        
        # Extract C4 documentation
        echo "# $component_name C4 Documentation" > "$output_file"
        echo "" >> "$output_file"
        
        # Extract component information
        if grep -q "@c4component" "$file"; then
            echo "## Component Information" >> "$output_file"
            grep -A 10 "@c4component" "$file" | sed 's/^[ \t]*//' >> "$output_file"
        fi
        
        # Extract dependencies
        if grep -q "@c4dependencies" "$file"; then
            echo "## Dependencies" >> "$output_file"
            grep -A 5 "@c4dependencies" "$file" | sed 's/^[ \t]*//' >> "$output_file"
        fi
        
        # Extract configuration
        if grep -q "@c4config" "$file"; then
            echo "## Configuration" >> "$output_file"
            grep -A 5 "@c4config" "$file" | sed 's/^[ \t]*//' >> "$output_file"
        fi
    done
    
    echo -e "${GREEN}C4 documentation generation complete!${NC}"
}

# Function to update traceability matrix
update_traceability() {
    echo -e "${YELLOW}Updating traceability matrix...${NC}"
    
    # Generate traceability matrix
    local matrix_file="$OUTPUT_DIR/traceability_matrix.md"
    echo "# C4 Traceability Matrix" > "$matrix_file"
    echo "" >> "$matrix_file"
    
    # Add context level mapping
    echo "## Context Level" >> "$matrix_file"
    echo "" >> "$matrix_file"
    echo "| External System | Implementation | Files |" >> "$matrix_file"
    echo "|----------------|----------------|-------|" >> "$matrix_file"
    
    # Add container level mapping
    echo "" >> "$matrix_file"
    echo "## Container Level" >> "$matrix_file"
    echo "" >> "$matrix_file"
    echo "| Container | Implementation | Components |" >> "$matrix_file"
    echo "|-----------|----------------|------------|" >> "$matrix_file"
    
    # Add component level mapping
    echo "" >> "$matrix_file"
    echo "## Component Level" >> "$matrix_file"
    echo "" >> "$matrix_file"
    echo "| Component | Class | Files | Responsibilities |" >> "$matrix_file"
    echo "|-----------|-------|-------|------------------|" >> "$matrix_file"
    
    echo -e "${GREEN}Traceability matrix updated!${NC}"
}

# Main script execution
case "$1" in
    "--validate")
        validate_c4_docs
        ;;
    "--generate")
        generate_c4_docs
        ;;
    "--update")
        update_traceability
        ;;
    *)
        echo "Usage: $0 [--validate] [--generate] [--update]"
        exit 1
        ;;
esac

exit 0 