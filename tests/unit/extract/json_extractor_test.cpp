/**
 * @file json_extractor_test.cpp
 * @brief Unit tests for JSON extractor functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/json_extractor.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <filesystem>
#include <fstream>
#include <thread>

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;
using json = nlohmann::json;

// Test fixture for JSON extractor tests
class JsonExtractorTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary directory for test files
        test_dir_ = std::filesystem::temp_directory_path() / "omop_json_test";
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        // Clean up test files
        std::filesystem::remove_all(test_dir_);
    }

    // Helper function to create test JSON file
    std::string createTestJson(const std::string& filename, const json& content) {
        std::filesystem::path filepath = test_dir_ / filename;
        std::ofstream file(filepath);
        file << content.dump(2);
        file.close();
        return filepath.string();
    }

    std::filesystem::path test_dir_;
};

// Tests basic JSON array extraction
TEST_F(JsonExtractorTest, ExtractJsonArray) {
    json content = json::array({
        {{"name", "John"}, {"age", 30}, {"city", "New York"}},
        {{"name", "Jane"}, {"age", 25}, {"city", "Los Angeles"}}
    });
    
    std::string filepath = createTestJson("array.json", content);
    
    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    
    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));
    
    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(2, batch.size());
    
    auto records = batch.get_records();
    EXPECT_EQ("John", std::any_cast<std::string>(*records[0].get_field("name")));
    EXPECT_EQ(30LL, std::any_cast<int64_t>(*records[0].get_field("age")));
}

// Tests JSON extraction with root path
TEST_F(JsonExtractorTest, ExtractWithRootPath) {
    json content = {
        {"metadata", {{"version", "1.0"}, {"created", "2024-01-01"}}},
        {"data", {
            {"patients", json::array({
                {{"id", 1}, {"name", "John Doe"}},
                {{"id", 2}, {"name", "Jane Smith"}}
            })}
        }}
    };
    
    std::string filepath = createTestJson("nested.json", content);
    
    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["root_path"] = "data.patients";
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(2, batch.size());
    
    auto records = batch.get_records();
    EXPECT_EQ(1LL, std::any_cast<int64_t>(*records[0].get_field("id")));
    EXPECT_EQ("Jane Smith", std::any_cast<std::string>(*records[1].get_field("name")));
}

// Tests JSON flattening of nested objects
TEST_F(JsonExtractorTest, FlattenNestedObjects) {
    json content = json::array({
        {
            {"id", 1},
            {"person", {
                {"name", "John"},
                {"address", {
                    {"street", "123 Main St"},
                    {"city", "New York"}
                }}
            }}
        }
    });
    
    std::string filepath = createTestJson("nested_flatten.json", content);
    
    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["flatten_nested"] = true;
    config["array_delimiter"] = "_";
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    auto batch = extractor.extract_batch(10, context);
    auto records = batch.get_records();
    
    EXPECT_EQ("John", std::any_cast<std::string>(*records[0].get_field("person_name")));
    EXPECT_EQ("123 Main St", std::any_cast<std::string>(*records[0].get_field("person_address_street")));
    EXPECT_EQ("New York", std::any_cast<std::string>(*records[0].get_field("person_address_city")));
}

// Tests JSON array flattening
TEST_F(JsonExtractorTest, FlattenArrays) {
    json content = json::array({
        {
            {"id", 1},
            {"tags", json::array({"tag1", "tag2", "tag3"})},
            {"scores", json::array({85, 90, 95})}
        }
    });
    
    std::string filepath = createTestJson("array_flatten.json", content);
    
    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["flatten_nested"] = true;
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    auto batch = extractor.extract_batch(10, context);
    auto records = batch.get_records();
    
    EXPECT_EQ("tag1", std::any_cast<std::string>(*records[0].get_field("tags_0")));
    EXPECT_EQ("tag2", std::any_cast<std::string>(*records[0].get_field("tags_1")));
    EXPECT_EQ(85LL, std::any_cast<int64_t>(*records[0].get_field("scores_0")));
}

// Tests date parsing in JSON
TEST_F(JsonExtractorTest, ParseDates) {
    json content = json::array({
        {
            {"id", 1},
            {"created_date", "2024-01-15"},
            {"updated_time", "2024-01-15 10:30:45"}
        }
    });
    
    std::string filepath = createTestJson("dates.json", content);
    
    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["parse_dates"] = true;
    config["date_formats"] = std::vector<std::string>{"%Y-%m-%d", "%Y-%m-%d %H:%M:%S"};
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    auto batch = extractor.extract_batch(10, context);
    auto records = batch.get_records();
    
    auto date_field = records[0].get_field("created_date");
    EXPECT_TRUE(date_field->type() == typeid(std::chrono::system_clock::time_point));
}

// Tests handling of null values
TEST_F(JsonExtractorTest, HandleNullValues) {
    json content = json::array({
        {
            {"id", 1},
            {"name", "John"},
            {"age", nullptr},
            {"city", json::value_t::null}
        }
    });
    
    std::string filepath = createTestJson("nulls.json", content);
    
    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["ignore_null"] = true;
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    auto batch = extractor.extract_batch(10, context);
    auto records = batch.get_records();
    
    EXPECT_TRUE(records[0].get_field("id").has_value());
    EXPECT_TRUE(records[0].get_field("name").has_value());
    EXPECT_FALSE(records[0].get_field("age").has_value());
    EXPECT_FALSE(records[0].get_field("city").has_value());
}

// Tests single object extraction
TEST_F(JsonExtractorTest, ExtractSingleObject) {
    json content = {
        {"id", 1},
        {"name", "Single Record"},
        {"value", 100}
    };
    
    std::string filepath = createTestJson("single.json", content);
    
    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(1, batch.size());
    
    auto records = batch.get_records();
    EXPECT_EQ("Single Record", std::any_cast<std::string>(*records[0].get_field("name")));
}

// Tests maximum depth limitation
TEST_F(JsonExtractorTest, MaxDepthLimit) {
    json content = json::array({
        {
            {"level1", {
                {"level2", {
                    {"level3", {
                        {"level4", {
                            {"level5", "too deep"}
                        }}
                    }}
                }}
            }}
        }
    });
    
    std::string filepath = createTestJson("deep.json", content);
    
    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["flatten_nested"] = true;
    config["max_depth"] = size_t(3);
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    auto batch = extractor.extract_batch(10, context);
    auto records = batch.get_records();
    
    // Should have fields up to level3 but not beyond
    EXPECT_TRUE(records[0].get_field("level1_level2_level3").has_value());
    EXPECT_FALSE(records[0].get_field("level1_level2_level3_level4").has_value());
}

// Tests JSON Lines extractor
TEST_F(JsonExtractorTest, JsonLinesExtractor) {
    std::string content = 
        R"({"id": 1, "name": "John", "age": 30})" "\n"
        R"({"id": 2, "name": "Jane", "age": 25})" "\n"
        R"({"id": 3, "name": "Bob", "age": 35})" "\n";
    
    std::filesystem::path filepath = test_dir_ / "lines.jsonl";
    std::ofstream file(filepath);
    file << content;
    file.close();
    
    JsonLinesExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath.string();
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(3, batch.size());
    
    auto records = batch.get_records();
    EXPECT_EQ(2LL, std::any_cast<int64_t>(*records[1].get_field("id")));
    EXPECT_EQ("Bob", std::any_cast<std::string>(*records[2].get_field("name")));
}

// Tests JSON Lines with parsing errors
TEST_F(JsonExtractorTest, JsonLinesWithErrors) {
    std::string content = 
        R"({"id": 1, "name": "John"})" "\n"
        R"(invalid json line)" "\n"
        R"({"id": 2, "name": "Jane"})" "\n";
    
    std::filesystem::path filepath = test_dir_ / "lines_with_errors.jsonl";
    std::ofstream file(filepath);
    file << content;
    file.close();
    
    JsonLinesExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath.string();
    
    ProcessingContext context;
    context.set_continue_on_error(true);
    extractor.initialize(config, context);
    
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(2, batch.size()); // Should skip the invalid line
    
    auto stats = extractor.get_statistics();
    EXPECT_EQ(1, std::any_cast<size_t>(stats["error_count"]));
}

// Tests streaming JSON extractor
TEST_F(JsonExtractorTest, StreamingJsonExtractor) {
    json content = {
        {"data", json::array({
            {{"id", 1}, {"value", 100}},
            {{"id", 2}, {"value", 200}},
            {{"id", 3}, {"value", 300}}
        })}
    };
    
    std::string filepath = createTestJson("streaming.json", content);
    
    StreamingJsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["root_path"] = "data";
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    // Give parser thread time to start
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    auto batch = extractor.extract_batch(10, context);
    EXPECT_GE(batch.size(), 1); // Should have at least one record
    
    // Extract remaining records
    while (extractor.has_more_data()) {
        extractor.extract_batch(10, context);
    }
    
    extractor.finalize(context);
}

// Tests error handling for missing file
TEST_F(JsonExtractorTest, HandleMissingFile) {
    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = "/nonexistent/file.json";
    
    ProcessingContext context;
    EXPECT_THROW(extractor.initialize(config, context), ExtractionException);
}

// Tests error handling for invalid JSON
TEST_F(JsonExtractorTest, HandleInvalidJson) {
    std::filesystem::path filepath = test_dir_ / "invalid.json";
    std::ofstream file(filepath);
    file << "{ invalid json content";
    file.close();
    
    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath.string();
    
    ProcessingContext context;
    EXPECT_THROW(extractor.initialize(config, context), ExtractionException);
}

// Tests statistics collection
TEST_F(JsonExtractorTest, GetStatistics) {
    json content = json::array({
        {{"id", 1}}, {{"id", 2}}, {{"id", 3}}
    });
    
    std::string filepath = createTestJson("stats.json", content);
    
    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    
    ProcessingContext context;
    extractor.initialize(config, context);
    extractor.extract_batch(10, context);
    extractor.finalize(context);
    
    auto stats = extractor.get_statistics();
    EXPECT_EQ(filepath, std::any_cast<std::string>(stats["filepath"]));
    EXPECT_EQ(3, std::any_cast<size_t>(stats["extracted_count"]));
    EXPECT_EQ(3, std::any_cast<size_t>(stats["total_records"]));
}

// Tests JSON factory
TEST_F(JsonExtractorTest, JsonExtractorFactory) {
    // Test creating different JSON extractor types
    auto json_extractor = JsonExtractorFactory::create("json");
    EXPECT_NE(nullptr, json_extractor);
    EXPECT_EQ("json", json_extractor->get_type());
    
    auto jsonl_extractor = JsonExtractorFactory::create("jsonl");
    EXPECT_NE(nullptr, jsonl_extractor);
    EXPECT_EQ("jsonl", jsonl_extractor->get_type());
    
    auto streaming_extractor = JsonExtractorFactory::create("streaming_json");
    EXPECT_NE(nullptr, streaming_extractor);
    EXPECT_EQ("streaming_json", streaming_extractor->get_type());
    
    // Test invalid type
    EXPECT_THROW(JsonExtractorFactory::create("invalid_type"), ConfigurationException);
}