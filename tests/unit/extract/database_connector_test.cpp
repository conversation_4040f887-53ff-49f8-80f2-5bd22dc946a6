/**
 * @file database_connector_test.cpp
 * @brief Unit tests for database connector functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/database_connector.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <chrono>
#include <thread>

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;
using ::testing::_;
using ::testing::Return;
using ::testing::Throw;
using ::testing::NiceMock;

// Mock database connection for testing
class MockDatabaseConnection : public IDatabaseConnection {
public:
    MOCK_METHOD(void, connect, (const ConnectionParams& params), (override));
    MOCK_METHOD(void, disconnect, (), (override));
    MOCK_METHOD(bool, is_connected, (), (const, override));
    MOCK_METHOD((std::unique_ptr<IResultSet>), execute_query, (const std::string& sql), (override));
    MOCK_METHOD(size_t, execute_update, (const std::string& sql), (override));
    MOCK_METHOD((std::unique_ptr<IPreparedStatement>), prepare_statement, (const std::string& sql), (override));
    MOCK_METHOD(void, begin_transaction, (), (override));
    MOCK_METHOD(void, commit, (), (override));
    MOCK_METHOD(void, rollback, (), (override));
    MOCK_METHOD(std::string, get_database_type, (), (const, override));
    MOCK_METHOD(std::string, get_version, (), (const, override));
    MOCK_METHOD(void, set_query_timeout, (int seconds), (override));
    MOCK_METHOD(bool, table_exists, (const std::string& table_name, const std::string& schema), (const, override));
};

// Mock result set for testing
class MockResultSet : public IResultSet {
public:
    MOCK_METHOD(bool, next, (), (override));
    MOCK_METHOD(std::any, get_value, (size_t index), (const, override));
    MOCK_METHOD(std::any, get_value, (const std::string& column_name), (const, override));
    MOCK_METHOD(bool, is_null, (size_t index), (const, override));
    MOCK_METHOD(bool, is_null, (const std::string& column_name), (const, override));
    MOCK_METHOD(size_t, column_count, (), (const, override));
    MOCK_METHOD(std::string, column_name, (size_t index), (const, override));
    MOCK_METHOD(std::string, column_type, (size_t index), (const, override));
    MOCK_METHOD(Record, to_record, (), (const, override));
};

// Mock prepared statement for testing
class MockPreparedStatement : public IPreparedStatement {
public:
    MOCK_METHOD(void, bind, (size_t index, const std::any& value), (override));
    MOCK_METHOD((std::unique_ptr<IResultSet>), execute_query, (), (override));
    MOCK_METHOD(size_t, execute_update, (), (override));
    MOCK_METHOD(void, clear_parameters, (), (override));
};

// Test fixture for database extractor tests
class DatabaseExtractorTest : public ::testing::Test {
protected:
    void SetUp() override {
        mock_connection_ = std::make_unique<NiceMock<MockDatabaseConnection>>();
        mock_connection_ptr_ = mock_connection_.get();
        
        // Set up default expectations
        ON_CALL(*mock_connection_ptr_, is_connected())
            .WillByDefault(Return(true));
        ON_CALL(*mock_connection_ptr_, get_database_type())
            .WillByDefault(Return("MockDB"));
    }

    std::unique_ptr<MockDatabaseConnection> mock_connection_;
    MockDatabaseConnection* mock_connection_ptr_;
};

// Tests basic database extraction initialization
TEST_F(DatabaseExtractorTest, InitializeExtractor) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists("person", ""))
        .WillOnce(Return(true));
    
    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    EXPECT_CALL(*mock_connection_ptr_, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));
    
    DatabaseExtractor extractor(std::move(mock_connection_));
    
    std::unordered_map<std::string, std::any> config;
    config["table"] = "person";
    
    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));
}

// Tests initialization with schema
TEST_F(DatabaseExtractorTest, InitializeWithSchema) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists("person", "cdm"))
        .WillOnce(Return(true));
    
    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    EXPECT_CALL(*mock_connection_ptr_, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));
    
    DatabaseExtractor extractor(std::move(mock_connection_));
    
    std::unordered_map<std::string, std::any> config;
    config["table"] = "person";
    config["schema"] = "cdm";
    
    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));
}

// Tests initialization with column selection
TEST_F(DatabaseExtractorTest, InitializeWithColumns) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));
    
    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    std::string expected_query = "SELECT person_id, birth_datetime, gender_concept_id FROM person";
    
    EXPECT_CALL(*mock_connection_ptr_, execute_query(expected_query))
        .WillOnce(Return(std::move(mock_result)));
    
    DatabaseExtractor extractor(std::move(mock_connection_));
    
    std::unordered_map<std::string, std::any> config;
    config["table"] = "person";
    config["columns"] = std::vector<std::string>{"person_id", "birth_datetime", "gender_concept_id"};
    
    ProcessingContext context;
    extractor.initialize(config, context);
}

// Tests initialization with filter condition
TEST_F(DatabaseExtractorTest, InitializeWithFilter) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));
    
    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    std::string expected_query = "SELECT * FROM person WHERE gender_concept_id = 8507";
    
    EXPECT_CALL(*mock_connection_ptr_, execute_query(expected_query))
        .WillOnce(Return(std::move(mock_result)));
    
    DatabaseExtractor extractor(std::move(mock_connection_));
    
    std::unordered_map<std::string, std::any> config;
    config["table"] = "person";
    config["filter"] = "gender_concept_id = 8507";
    
    ProcessingContext context;
    extractor.initialize(config, context);
}

// Tests initialization failure for missing table
TEST_F(DatabaseExtractorTest, InitializeMissingTable) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists("nonexistent", ""))
        .WillOnce(Return(false));
    
    DatabaseExtractor extractor(std::move(mock_connection_));
    
    std::unordered_map<std::string, std::any> config;
    config["table"] = "nonexistent";
    
    ProcessingContext context;
    EXPECT_THROW(extractor.initialize(config, context), DatabaseException);
}

// Tests batch extraction
TEST_F(DatabaseExtractorTest, ExtractBatch) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));
    
    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    auto result_ptr = mock_result.get();
    
    // Set up result set behavior
    EXPECT_CALL(*result_ptr, next())
        .WillOnce(Return(true))
        .WillOnce(Return(true))
        .WillOnce(Return(false));
    
    Record record1;
    record1.setField("id", 1);
    record1.setField("name", std::string("John"));

    Record record2;
    record2.setField("id", 2);
    record2.setField("name", std::string("Jane"));
    
    EXPECT_CALL(*result_ptr, to_record())
        .WillOnce(Return(record1))
        .WillOnce(Return(record2));
    
    EXPECT_CALL(*mock_connection_ptr_, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));
    
    DatabaseExtractor extractor(std::move(mock_connection_));
    
    std::unordered_map<std::string, std::any> config;
    config["table"] = "test_table";
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(2, batch.size());
    
    auto records = batch.getRecords();
    EXPECT_EQ(1, std::any_cast<int>(records[0].getField("id")));
    EXPECT_EQ("Jane", std::any_cast<std::string>(records[1].getField("name")));
}

// Tests query building with all options
TEST_F(DatabaseExtractorTest, BuildComplexQuery) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));
    
    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    std::string expected_query = 
        "SELECT id, name, value FROM myschema.mytable WHERE active = true ORDER BY id ASC";
    
    EXPECT_CALL(*mock_connection_ptr_, execute_query(expected_query))
        .WillOnce(Return(std::move(mock_result)));
    
    DatabaseExtractor extractor(std::move(mock_connection_));
    
    std::unordered_map<std::string, std::any> config;
    config["table"] = "mytable";
    config["schema"] = "myschema";
    config["columns"] = std::vector<std::string>{"id", "name", "value"};
    config["filter"] = "active = true";
    config["order_by"] = "id ASC";
    
    ProcessingContext context;
    extractor.initialize(config, context);
}

// Tests statistics collection
TEST_F(DatabaseExtractorTest, GetStatistics) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));
    
    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    EXPECT_CALL(*mock_connection_ptr_, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));
    
    DatabaseExtractor extractor(std::move(mock_connection_));
    
    std::unordered_map<std::string, std::any> config;
    config["table"] = "test_table";
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    auto stats = extractor.get_statistics();
    EXPECT_EQ("test_table", std::any_cast<std::string>(stats["table_name"]));
    EXPECT_EQ("MockDB", std::any_cast<std::string>(stats["database_type"]));
}

// Test fixture for connection pool tests
class ConnectionPoolTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Factory function that creates mock connections
        connection_factory_ = []() {
            auto conn = std::make_unique<NiceMock<MockDatabaseConnection>>();
            ON_CALL(*conn, is_connected()).WillByDefault(Return(true));
            return conn;
        };
    }

    std::function<std::unique_ptr<IDatabaseConnection>()> connection_factory_;
};

// Tests basic connection pool operations
TEST_F(ConnectionPoolTest, AcquireAndRelease) {
    ConnectionPool pool(2, 5, connection_factory_);
    
    // Acquire connection
    auto conn1 = pool.acquire();
    ASSERT_NE(nullptr, conn1);
    EXPECT_TRUE(conn1->is_connected());
    
    // Release connection
    pool.release(std::move(conn1));
    
    // Acquire again - should get the same connection
    auto conn2 = pool.acquire();
    ASSERT_NE(nullptr, conn2);
}

// Tests pool statistics
TEST_F(ConnectionPoolTest, PoolStatistics) {
    ConnectionPool pool(2, 5, connection_factory_);
    
    auto stats1 = pool.get_statistics();
    EXPECT_EQ(2, stats1.total_connections);
    EXPECT_EQ(0, stats1.active_connections);
    EXPECT_EQ(2, stats1.idle_connections);
    
    // Acquire connections
    auto conn1 = pool.acquire();
    auto conn2 = pool.acquire();
    
    auto stats2 = pool.get_statistics();
    EXPECT_EQ(2, stats2.total_connections);
    EXPECT_EQ(2, stats2.active_connections);
    EXPECT_EQ(0, stats2.idle_connections);
    
    // Release one connection
    pool.release(std::move(conn1));
    
    auto stats3 = pool.get_statistics();
    EXPECT_EQ(2, stats3.total_connections);
    EXPECT_EQ(1, stats3.active_connections);
    EXPECT_EQ(1, stats3.idle_connections);
}

// Tests pool growth when needed
TEST_F(ConnectionPoolTest, PoolGrowth) {
    ConnectionPool pool(1, 3, connection_factory_);
    
    auto stats1 = pool.get_statistics();
    EXPECT_EQ(1, stats1.total_connections);
    
    // Acquire more connections than minimum
    auto conn1 = pool.acquire();
    auto conn2 = pool.acquire();
    
    auto stats2 = pool.get_statistics();
    EXPECT_GE(stats2.total_connections, 2);
    EXPECT_LE(stats2.total_connections, 3);
}

// Tests pool timeout
TEST_F(ConnectionPoolTest, AcquireTimeout) {
    ConnectionPool pool(1, 1, connection_factory_);
    
    // Acquire the only connection
    auto conn1 = pool.acquire();
    
    // Try to acquire another with timeout
    std::thread acquire_thread([&pool]() {
        EXPECT_THROW(pool.acquire(100), DatabaseException);
    });
    
    acquire_thread.join();
}

// Tests connection validation
TEST_F(ConnectionPoolTest, ConnectionValidation) {
    // Factory that creates connections that become invalid
    auto factory = []() {
        auto conn = std::make_unique<NiceMock<MockDatabaseConnection>>();
        static int count = 0;
        // First connection will become invalid
        if (count++ == 0) {
            ON_CALL(*conn, is_connected()).WillByDefault(Return(false));
        } else {
            ON_CALL(*conn, is_connected()).WillByDefault(Return(true));
        }
        return conn;
    };
    
    ConnectionPool pool(2, 5, factory);
    
    // Validate connections - should remove invalid ones
    size_t removed = pool.validate_connections();
    EXPECT_GE(removed, 0);
    
    // Pool should maintain minimum connections
    auto stats = pool.get_statistics();
    EXPECT_GE(stats.total_connections, 2);
}

// Tests clearing idle connections
TEST_F(ConnectionPoolTest, ClearIdleConnections) {
    ConnectionPool pool(2, 5, connection_factory_);
    
    auto stats1 = pool.get_statistics();
    EXPECT_EQ(2, stats1.idle_connections);
    
    pool.clear_idle_connections();
    
    auto stats2 = pool.get_statistics();
    EXPECT_EQ(0, stats2.idle_connections);
}

// Tests result set base implementation
TEST(ResultSetBaseTest, ToRecord) {
    class TestResultSet : public ResultSetBase {
    public:
        bool next() override { return false; }
        std::any get_value(size_t index) const override {
            switch(index) {
                case 0: return 123;
                case 1: return std::string("test");
                case 2: return 45.67;
                default: return std::any{};
            }
        }
        std::any get_value(const std::string&) const override { return std::any{}; }
        bool is_null(size_t index) const override { return index >= 3; }
        bool is_null(const std::string&) const override { return false; }
        size_t column_count() const override { return 4; }
        std::string column_name(size_t index) const override {
            switch(index) {
                case 0: return "id";
                case 1: return "name";
                case 2: return "value";
                case 3: return "null_col";
                default: return "";
            }
        }
        std::string column_type(size_t) const override { return "unknown"; }
    };
    
    TestResultSet result_set;
    Record record = result_set.to_record();
    
    EXPECT_EQ(123, std::any_cast<int>(record.getField("id")));
    EXPECT_EQ("test", std::any_cast<std::string>(record.getField("name")));
    EXPECT_DOUBLE_EQ(45.67, std::any_cast<double>(record.getField("value")));
    EXPECT_FALSE(record.hasField("null_col"));
}

// Tests database connection factory
TEST(DatabaseConnectionFactoryTest, RegisterAndCreate) {
    // Register a test connection type
    DatabaseConnectionFactory::instance().register_type("test_db", []() {
        return std::make_unique<NiceMock<MockDatabaseConnection>>();
    });
    
    // Create connection
    auto connection = DatabaseConnectionFactory::instance().create("test_db");
    ASSERT_NE(nullptr, connection);
    
    // Test invalid type
    EXPECT_THROW(DatabaseConnectionFactory::instance().create("invalid_db"), DatabaseException);
}