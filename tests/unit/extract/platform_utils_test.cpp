/**
 * @file platform_utils_test.cpp
 * @brief Unit tests for platform-specific utility functions
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <filesystem>
#include <fstream>
#include <thread>

#ifdef _WIN32
#include "extract/platform/windows_utils.h"
#else
#include "extract/platform/unix_utils.h"
#include <sys/mman.h>  // For MADV_* constants
#endif

using namespace omop::extract::platform;
namespace fs = std::filesystem;

// Test fixture for platform utilities
class PlatformUtilsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary directory for tests
        test_dir_ = fs::temp_directory_path() / "omop_platform_test";
        fs::create_directories(test_dir_);
    }

    void TearDown() override {
        // Clean up test directory
        fs::remove_all(test_dir_);
    }

    // Helper to create test file
    std::string createTestFile(const std::string& name, const std::string& content) {
        fs::path filepath = test_dir_ / name;
        std::ofstream file(filepath);
        file << content;
        file.close();
        return filepath.string();
    }

    fs::path test_dir_;
};

#ifdef _WIN32
// Windows-specific tests

// Tests string conversion utilities
TEST(WindowsUtilsTest, StringConversion) {
    // Test UTF-8 to wide string conversion
    std::string utf8_str = "Test String éàü";
    std::wstring wide_str = utf8_to_wide(utf8_str);
    EXPECT_FALSE(wide_str.empty());
    
    // Test wide to UTF-8 conversion
    std::string converted_back = wide_to_utf8(wide_str);
    EXPECT_EQ(utf8_str, converted_back);
    
    // Test empty string
    EXPECT_TRUE(utf8_to_wide("").empty());
    EXPECT_TRUE(wide_to_utf8(L"").empty());
}

// Tests Windows error message retrieval
TEST(WindowsUtilsTest, ErrorMessage) {
    // Test with specific error code
    std::string error_msg = get_windows_error_message(ERROR_FILE_NOT_FOUND);
    EXPECT_FALSE(error_msg.empty());
    EXPECT_NE(error_msg.find("not found"), std::string::npos);
    
    // Test with current last error (0 means use GetLastError())
    SetLastError(ERROR_ACCESS_DENIED);
    error_msg = get_windows_error_message(0);
    EXPECT_FALSE(error_msg.empty());
}

// Tests file handle wrapper
TEST_F(PlatformUtilsTest, WindowsFileHandle) {
    std::string test_file = createTestFile("test.txt", "test content");
    std::wstring wide_path = utf8_to_wide(test_file);
    
    {
        WindowsFileHandle handle(CreateFileW(
            wide_path.c_str(),
            GENERIC_READ,
            FILE_SHARE_READ,
            nullptr,
            OPEN_EXISTING,
            FILE_ATTRIBUTE_NORMAL,
            nullptr));
        
        EXPECT_TRUE(handle.is_valid());
        EXPECT_NE(INVALID_HANDLE_VALUE, handle.get());
    }
    // Handle should be closed after scope
    
    // Test move semantics
    WindowsFileHandle handle1(CreateFileW(
        wide_path.c_str(),
        GENERIC_READ,
        FILE_SHARE_READ,
        nullptr,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL,
        nullptr));
    
    WindowsFileHandle handle2(std::move(handle1));
    EXPECT_FALSE(handle1.is_valid());
    EXPECT_TRUE(handle2.is_valid());
}

// Tests file size retrieval
TEST_F(PlatformUtilsTest, GetFileSize) {
    std::string content = "This is a test file with known size.";
    std::string test_file = createTestFile("size_test.txt", content);
    
    size_t size = get_file_size(test_file);
    EXPECT_EQ(content.length(), size);
    
    // Test non-existent file
    EXPECT_THROW(get_file_size("nonexistent.txt"), std::exception);
}

// Tests network path detection
TEST(WindowsUtilsTest, NetworkPath) {
    // UNC paths
    EXPECT_TRUE(is_network_path("\\\\server\\share\\file.txt"));
    EXPECT_TRUE(is_network_path("\\\\192.168.1.1\\folder"));
    
    // Local paths
    EXPECT_FALSE(is_network_path("C:\\Windows\\System32"));
    EXPECT_FALSE(is_network_path("D:\\Data\\file.txt"));
    
    // Relative paths
    EXPECT_FALSE(is_network_path("relative\\path.txt"));
}

// Tests available drives enumeration
TEST(WindowsUtilsTest, AvailableDrives) {
    std::vector<char> drives = get_available_drives();
    
    // Should have at least C: drive on Windows
    EXPECT_FALSE(drives.empty());
    bool has_c_drive = std::find(drives.begin(), drives.end(), 'C') != drives.end();
    EXPECT_TRUE(has_c_drive);
}

// Tests temporary directory
TEST(WindowsUtilsTest, TempDirectory) {
    std::string temp_dir = get_temp_directory();
    EXPECT_FALSE(temp_dir.empty());
    EXPECT_TRUE(fs::exists(temp_dir));
    EXPECT_TRUE(fs::is_directory(temp_dir));
}

// Tests temporary file creation
TEST(WindowsUtilsTest, CreateTempFile) {
    std::string temp_file = create_temp_file("test_", ".tmp");
    EXPECT_FALSE(temp_file.empty());
    EXPECT_TRUE(fs::exists(temp_file));
    
    // Clean up
    fs::remove(temp_file);
    
    // Test with default parameters
    temp_file = create_temp_file();
    EXPECT_FALSE(temp_file.empty());
    EXPECT_TRUE(temp_file.find("omop_") != std::string::npos);
    EXPECT_TRUE(temp_file.find(".tmp") != std::string::npos);
    
    // Clean up
    fs::remove(temp_file);
}

// Tests file attributes
TEST_F(PlatformUtilsTest, FileAttributes) {
    std::string test_file = createTestFile("attr_test.txt", "test");
    
    // Get current attributes
    DWORD attrs = get_file_attributes(test_file);
    EXPECT_NE(INVALID_FILE_ATTRIBUTES, attrs);
    
    // Set hidden attribute
    EXPECT_TRUE(set_file_attributes(test_file, attrs | FILE_ATTRIBUTE_HIDDEN));
    
    // Verify attribute was set
    DWORD new_attrs = get_file_attributes(test_file);
    EXPECT_TRUE(new_attrs & FILE_ATTRIBUTE_HIDDEN);
}

// Tests high-resolution timer
TEST(WindowsUtilsTest, HighResTimer) {
    WindowsHighResTimer timer;
    
    // Sleep for a known duration
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    double elapsed_ms = timer.elapsed_milliseconds();
    EXPECT_GE(elapsed_ms, 90.0);  // Allow some tolerance
    EXPECT_LE(elapsed_ms, 150.0);
    
    double elapsed_s = timer.elapsed_seconds();
    EXPECT_GE(elapsed_s, 0.09);
    EXPECT_LE(elapsed_s, 0.15);
    
    // Test reset
    timer.reset();
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    elapsed_ms = timer.elapsed_milliseconds();
    EXPECT_GE(elapsed_ms, 40.0);
    EXPECT_LE(elapsed_ms, 80.0);
}

// Tests memory information
TEST(WindowsUtilsTest, MemoryInfo) {
    MemoryInfo mem_info = get_memory_info();
    
    // Should have non-zero memory values
    EXPECT_GT(mem_info.total_physical, 0);
    EXPECT_GT(mem_info.available_physical, 0);
    EXPECT_LE(mem_info.available_physical, mem_info.total_physical);
    
    EXPECT_GT(mem_info.total_virtual, 0);
    EXPECT_GT(mem_info.available_virtual, 0);
}

// Tests process priority
TEST(WindowsUtilsTest, ProcessPriority) {
    // Set normal priority
    EXPECT_TRUE(set_process_priority(NORMAL_PRIORITY_CLASS));
    
    // Can't really test other priorities without admin rights
}

// Tests file mapping
TEST_F(PlatformUtilsTest, FileMapping) {
    std::string content = "Memory mapped file content";
    std::string test_file = createTestFile("mapped.txt", content);
    
    auto [file_handle, mapping_handle] = create_file_mapping(test_file);
    EXPECT_TRUE(file_handle.is_valid());
    EXPECT_TRUE(mapping_handle.is_valid());
    
    // Map view of file
    void* view = map_view_of_file(mapping_handle.get());
    EXPECT_NE(nullptr, view);
    
    // Read content
    std::string mapped_content(static_cast<char*>(view), content.length());
    EXPECT_EQ(content, mapped_content);
    
    // Unmap
    EXPECT_TRUE(unmap_view_of_file(view));
}

#else
// Unix/Linux-specific tests

// Tests file descriptor wrapper
TEST_F(PlatformUtilsTest, UnixFileDescriptor) {
    std::string test_file = createTestFile("test.txt", "test content");
    
    {
        UnixFileDescriptor fd(open(test_file.c_str(), O_RDONLY));
        EXPECT_TRUE(fd.is_valid());
        EXPECT_GE(fd.get(), 0);
    }
    // File descriptor should be closed after scope
    
    // Test move semantics
    UnixFileDescriptor fd1(open(test_file.c_str(), O_RDONLY));
    UnixFileDescriptor fd2(std::move(fd1));
    EXPECT_FALSE(fd1.is_valid());
    EXPECT_TRUE(fd2.is_valid());
}

// Tests memory mapped file
TEST_F(PlatformUtilsTest, MemoryMappedFile) {
    std::string content = "Memory mapped file content for testing";
    std::string test_file = createTestFile("mapped.txt", content);
    
    MemoryMappedFile mmf;
    EXPECT_TRUE(mmf.map_file(test_file));
    EXPECT_TRUE(mmf.is_mapped());
    EXPECT_NE(nullptr, mmf.data());
    EXPECT_EQ(content.length(), mmf.size());
    
    // Read content
    std::string mapped_content(static_cast<char*>(mmf.data()), mmf.size());
    EXPECT_EQ(content, mapped_content);
    
    // Test unmapping
    mmf.unmap();
    EXPECT_FALSE(mmf.is_mapped());
    EXPECT_EQ(nullptr, mmf.data());
    
    // Test move semantics
    MemoryMappedFile mmf1;
    mmf1.map_file(test_file);
    
    MemoryMappedFile mmf2(std::move(mmf1));
    EXPECT_FALSE(mmf1.is_mapped());
    EXPECT_TRUE(mmf2.is_mapped());
}

// Tests system error message
TEST(UnixUtilsTest, SystemErrorMessage) {
    // Test with specific error code
    std::string error_msg = get_system_error_message(ENOENT);
    EXPECT_FALSE(error_msg.empty());
    
    // Test with errno
    errno = EACCES;
    error_msg = get_system_error_message();
    EXPECT_FALSE(error_msg.empty());
}

// Tests file size retrieval
TEST_F(PlatformUtilsTest, GetFileSize) {
    std::string content = "This is a test file with known size.";
    std::string test_file = createTestFile("size_test.txt", content);
    
    size_t size = get_file_size(test_file);
    EXPECT_EQ(content.length(), size);
    
    // Test non-existent file
    EXPECT_THROW(get_file_size("/nonexistent/file.txt"), std::exception);
}

// Tests file modification time
TEST_F(PlatformUtilsTest, GetFileMTime) {
    std::string test_file = createTestFile("mtime_test.txt", "test");
    
    time_t mtime = get_file_mtime(test_file);
    EXPECT_GT(mtime, 0);
    
    // Modification time should be recent
    time_t now = time(nullptr);
    EXPECT_LE(mtime, now);
    EXPECT_GE(mtime, now - 60); // Within last minute
}

// Tests symbolic link detection
TEST_F(PlatformUtilsTest, SymbolicLinks) {
    std::string target_file = createTestFile("target.txt", "target content");
    fs::path link_path = test_dir_ / "symlink.txt";
    
    // Create symbolic link (requires appropriate permissions)
    try {
        fs::create_symlink(target_file, link_path);
        
        EXPECT_TRUE(is_symbolic_link(link_path.string()));
        EXPECT_FALSE(is_symbolic_link(target_file));
        
        // Resolve symbolic link
        std::string resolved = resolve_symbolic_link(link_path.string());
        EXPECT_FALSE(resolved.empty());
        
    } catch (const fs::filesystem_error&) {
        // Skip test if no permission to create symlinks
        GTEST_SKIP() << "No permission to create symbolic links";
    }
}

// Tests real path resolution
TEST_F(PlatformUtilsTest, GetRealPath) {
    std::string test_file = createTestFile("real_test.txt", "test");
    
    std::string real_path = get_real_path(test_file);
    EXPECT_FALSE(real_path.empty());
    EXPECT_TRUE(fs::exists(real_path));
    
    // Test with relative path
    fs::current_path(test_dir_);
    std::string relative_real = get_real_path("real_test.txt");
    EXPECT_EQ(real_path, relative_real);
}

// Tests network path detection
TEST(UnixUtilsTest, NetworkPath) {
    // Common network mount points
    EXPECT_TRUE(is_network_path("/mnt/nfs/share"));
    EXPECT_TRUE(is_network_path("/media/network"));
    EXPECT_TRUE(is_network_path("/net/server"));
    
    // Local paths
    EXPECT_FALSE(is_network_path("/home/<USER>"));
    EXPECT_FALSE(is_network_path("/tmp"));
    EXPECT_FALSE(is_network_path("/var/log"));
}

// Tests mounted filesystems
TEST(UnixUtilsTest, MountedFilesystems) {
    std::vector<std::string> filesystems = get_mounted_filesystems();
    
    // Should have at least root filesystem
    EXPECT_FALSE(filesystems.empty());
    bool has_root = std::find(filesystems.begin(), filesystems.end(), "/") != filesystems.end();
    EXPECT_TRUE(has_root);
}

// Tests temporary directory
TEST(UnixUtilsTest, TempDirectory) {
    std::string temp_dir = get_temp_directory();
    EXPECT_FALSE(temp_dir.empty());
    EXPECT_TRUE(fs::exists(temp_dir));
    EXPECT_TRUE(fs::is_directory(temp_dir));
    EXPECT_EQ('/', temp_dir.back());
}

// Tests temporary file creation
TEST(UnixUtilsTest, CreateTempFile) {
    std::string temp_file = create_temp_file("test_", ".tmp");
    EXPECT_FALSE(temp_file.empty());
    EXPECT_TRUE(fs::exists(temp_file));
    
    // Clean up
    fs::remove(temp_file);
    
    // Test with default parameters
    temp_file = create_temp_file();
    EXPECT_FALSE(temp_file.empty());
    EXPECT_TRUE(temp_file.find("omop_") != std::string::npos);
    EXPECT_TRUE(temp_file.find(".tmp") != std::string::npos);
    
    // Clean up
    fs::remove(temp_file);
}

// Tests file permissions
TEST_F(PlatformUtilsTest, FilePermissions) {
    std::string test_file = createTestFile("perm_test.txt", "test");
    
    // Set specific permissions (rw-r--r--)
    EXPECT_TRUE(set_file_permissions(test_file, 0644));
    
    // Get permissions
    mode_t perms = get_file_permissions(test_file);
    EXPECT_EQ(0644, perms);
    
    // Change to executable (rwxr-xr-x)
    EXPECT_TRUE(set_file_permissions(test_file, 0755));
    perms = get_file_permissions(test_file);
    EXPECT_EQ(0755, perms);
}

// Tests high-resolution timer
TEST(UnixUtilsTest, HighResTimer) {
    UnixHighResTimer timer;
    
    // Sleep for a known duration
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    double elapsed_ms = timer.elapsed_milliseconds();
    EXPECT_GE(elapsed_ms, 90.0);  // Allow some tolerance
    EXPECT_LE(elapsed_ms, 150.0);
    
    double elapsed_s = timer.elapsed_seconds();
    EXPECT_GE(elapsed_s, 0.09);
    EXPECT_LE(elapsed_s, 0.15);
    
    // Test reset
    timer.reset();
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    elapsed_ms = timer.elapsed_milliseconds();
    EXPECT_GE(elapsed_ms, 40.0);
    EXPECT_LE(elapsed_ms, 80.0);
}

// Tests memory information
TEST(UnixUtilsTest, MemoryInfo) {
    MemoryInfo mem_info = get_memory_info();
    
    // Should have non-zero memory values
    EXPECT_GT(mem_info.total_physical, 0);
    EXPECT_GT(mem_info.available_physical, 0);
    EXPECT_LE(mem_info.available_physical, mem_info.total_physical);
    
    // Swap might be zero on some systems
    if (mem_info.total_swap > 0) {
        EXPECT_LE(mem_info.available_swap, mem_info.total_swap);
    }
}

// Tests process priority (nice value)
TEST(UnixUtilsTest, ProcessPriority) {
    // Get current priority
    int original_priority = get_process_priority();
    
    // Try to set a slightly higher nice value (lower priority)
    // This should work without root privileges
    int new_priority = std::min(original_priority + 5, 19);
    
    if (set_process_priority(new_priority)) {
        int current = get_process_priority();
        EXPECT_EQ(new_priority, current);
        
        // Restore original
        set_process_priority(original_priority);
    }
}

// Tests CPU count
TEST(UnixUtilsTest, CpuCount) {
    size_t cpu_count = get_cpu_count();
    EXPECT_GE(cpu_count, 1);
    
    // Should match system configuration
    long sys_count = sysconf(_SC_NPROCESSORS_ONLN);
    if (sys_count > 0) {
        EXPECT_EQ(static_cast<size_t>(sys_count), cpu_count);
    }
}

// Tests memory locking (requires appropriate permissions)
TEST(UnixUtilsTest, MemoryLocking) {
    const size_t size = 4096; // One page
    void* buffer = malloc(size);
    ASSERT_NE(nullptr, buffer);
    
    // Try to lock memory (might fail without permissions)
    bool locked = lock_memory(buffer, size);
    if (locked) {
        // If we could lock, we should be able to unlock
        EXPECT_TRUE(unlock_memory(buffer, size));
    } else {
        // Expected to fail without CAP_IPC_LOCK or appropriate limits
        GTEST_SKIP() << "Memory locking requires elevated privileges";
    }
    
    free(buffer);
}

// Tests memory usage advice
TEST(UnixUtilsTest, MemoryAdvice) {
    const size_t size = 4096 * 10; // 10 pages
    void* buffer = malloc(size);
    ASSERT_NE(nullptr, buffer);
    
    // Should be able to give advice about memory usage
    EXPECT_TRUE(advise_memory_usage(buffer, size, MADV_SEQUENTIAL));
    EXPECT_TRUE(advise_memory_usage(buffer, size, MADV_RANDOM));
    EXPECT_TRUE(advise_memory_usage(buffer, size, MADV_NORMAL));
    
    free(buffer);
}

#endif // Platform-specific tests

// Common tests that work on both platforms
TEST_F(PlatformUtilsTest, CommonFileOperations) {
    // Test that file operations work consistently
    std::string test_file = createTestFile("common_test.txt", "Common content");
    
    EXPECT_TRUE(fs::exists(test_file));
    EXPECT_GT(fs::file_size(test_file), 0);
}