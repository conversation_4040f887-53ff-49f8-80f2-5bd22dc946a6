/**
 * @file test_interfaces.cpp
 * @brief Unit tests for interfaces components
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "core/interfaces.h"
#include "core/record.h"
#include "common/exceptions.h"
#include <thread>
#include <chrono>
#include <any>
#include <optional>
#include <unordered_map>
#include <string>
#include <vector>

namespace omop::core {

using namespace testing;

// Type aliases to simplify MOCK_METHOD syntax
using ConfigMap = std::unordered_map<std::string, std::any>;
using StatsMap = std::unordered_map<std::string, std::any>;
using OptionalRecord = std::optional<Record>;

// Mock implementations for testing
class MockExtractor : public IExtractor {
public:
    MOCK_METHOD(void, initialize, (const ConfigMap&, ProcessingContext&), (override));
    MOCK_METHOD(RecordBatch, extract_batch, (size_t, ProcessingContext&), (override));
    MOCK_METHOD(bool, has_more_data, (), (const, override));
    MOCK_METHOD(std::string, get_type, (), (const, override));
    MOCK_METHOD(void, finalize, (ProcessingContext&), (override));
    MOCK_METHOD(StatsMap, get_statistics, (), (const, override));
};

class MockTransformer : public ITransformer {
public:
    MOCK_METHOD(void, initialize, (const ConfigMap&, ProcessingContext&), (override));
    MOCK_METHOD(OptionalRecord, transform, (const Record&, ProcessingContext&), (override));
    MOCK_METHOD(RecordBatch, transform_batch, (const RecordBatch&, ProcessingContext&), (override));
    MOCK_METHOD(std::string, get_type, (), (const, override));
    MOCK_METHOD(ValidationResult, validate, (const Record&), (const, override));
    MOCK_METHOD(StatsMap, get_statistics, (), (const, override));
};

class MockLoader : public ILoader {
public:
    MOCK_METHOD(void, initialize, (const ConfigMap&, ProcessingContext&), (override));
    MOCK_METHOD(bool, load, (const Record&, ProcessingContext&), (override));
    MOCK_METHOD(size_t, load_batch, (const RecordBatch&, ProcessingContext&), (override));
    MOCK_METHOD(void, commit, (ProcessingContext&), (override));
    MOCK_METHOD(void, rollback, (ProcessingContext&), (override));
    MOCK_METHOD(std::string, get_type, (), (const, override));
    MOCK_METHOD(void, finalize, (ProcessingContext&), (override));
    MOCK_METHOD(StatsMap, get_statistics, (), (const, override));
};

// Simple test to verify the test infrastructure works
TEST(InterfacesTest, BasicTest) {
    EXPECT_TRUE(true);
}

// ProcessingContext tests
class ProcessingContextTest : public ::testing::Test {
protected:
    ProcessingContext context;
};

// Test default constructor initializes context properly
TEST_F(ProcessingContextTest, DefaultConstruction) {
    EXPECT_EQ(context.current_stage(), ProcessingContext::Stage::Extract);
    EXPECT_EQ(context.job_id(), "");
    EXPECT_EQ(context.processed_count(), 0);
    EXPECT_EQ(context.error_count(), 0);
}

// Test setting and getting processing stage
TEST_F(ProcessingContextTest, SetAndGetStage) {
    context.set_stage(ProcessingContext::Stage::Transform);
    EXPECT_EQ(context.current_stage(), ProcessingContext::Stage::Transform);
    
    context.set_stage(ProcessingContext::Stage::Load);
    EXPECT_EQ(context.current_stage(), ProcessingContext::Stage::Load);
}

// Test setting and getting job ID
TEST_F(ProcessingContextTest, SetAndGetJobId) {
    std::string job_id = "test-job-123";
    context.set_job_id(job_id);
    EXPECT_EQ(context.job_id(), job_id);
}

// Test incrementing processed record count
TEST_F(ProcessingContextTest, IncrementProcessedCount) {
    EXPECT_EQ(context.processed_count(), 0);
    
    context.increment_processed();
    EXPECT_EQ(context.processed_count(), 1);
    
    context.increment_processed(10);
    EXPECT_EQ(context.processed_count(), 11);
}

// Test incrementing error count
TEST_F(ProcessingContextTest, IncrementErrorCount) {
    EXPECT_EQ(context.error_count(), 0);
    
    context.increment_errors();
    EXPECT_EQ(context.error_count(), 1);
    
    context.increment_errors(5);
    EXPECT_EQ(context.error_count(), 6);
}

// Test elapsed time calculation
TEST_F(ProcessingContextTest, ElapsedTime) {
    auto start_duration = context.elapsed_time();
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    auto end_duration = context.elapsed_time();
    
    EXPECT_GT(end_duration.count(), start_duration.count());
    EXPECT_GE(end_duration.count() - start_duration.count(), 0.1);
}

// Test setting and getting context data
TEST_F(ProcessingContextTest, SetAndGetContextData) {
    context.set_data("key1", 42);
    context.set_data("key2", std::string("value"));
    context.set_data("key3", 3.14);
    
    auto data1 = context.get_data("key1");
    ASSERT_TRUE(data1.has_value());
    EXPECT_EQ(std::any_cast<int>(data1.value()), 42);
    
    auto data2 = context.get_data("key2");
    ASSERT_TRUE(data2.has_value());
    EXPECT_EQ(std::any_cast<std::string>(data2.value()), "value");
    
    auto data3 = context.get_data("key3");
    ASSERT_TRUE(data3.has_value());
    EXPECT_DOUBLE_EQ(std::any_cast<double>(data3.value()), 3.14);
    
    auto data4 = context.get_data("nonexistent");
    EXPECT_FALSE(data4.has_value());
}

// Test context data thread safety
TEST_F(ProcessingContextTest, ThreadSafetyContextData) {
    const int num_threads = 10;
    const int operations_per_thread = 100;
    std::vector<std::thread> threads;

    // Multiple threads writing different keys
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([this, i, operations_per_thread]() {
            for (int j = 0; j < operations_per_thread; ++j) {
                std::string key = "thread_" + std::to_string(i) + "_" + std::to_string(j);
                context.set_data(key, i * operations_per_thread + j);
            }
        });
    }

    for (auto& t : threads) {
        t.join();
    }

    // Verify all data was written
    for (int i = 0; i < num_threads; ++i) {
        for (int j = 0; j < operations_per_thread; ++j) {
            std::string key = "thread_" + std::to_string(i) + "_" + std::to_string(j);
            auto data = context.get_data(key);
            ASSERT_TRUE(data.has_value());
            EXPECT_EQ(std::any_cast<int>(data.value()), i * operations_per_thread + j);
        }
    }
}

// Test logging functionality
TEST_F(ProcessingContextTest, Logging) {
    context.set_job_id("test-job");
    context.set_stage(ProcessingContext::Stage::Transform);

    // These should not throw
    EXPECT_NO_THROW(context.log("debug", "Debug message"));
    EXPECT_NO_THROW(context.log("info", "Info message"));
    EXPECT_NO_THROW(context.log("warn", "Warning message"));
    EXPECT_NO_THROW(context.log("warning", "Warning message"));
    EXPECT_NO_THROW(context.log("error", "Error message"));
    EXPECT_NO_THROW(context.log("critical", "Critical message"));
    EXPECT_NO_THROW(context.log("unknown", "Unknown level defaults to info"));
}

// ValidationResult tests
class ValidationResultTest : public ::testing::Test {
protected:
    ValidationResult result;
};

// Test default constructor creates valid result
TEST_F(ValidationResultTest, DefaultConstruction) {
    EXPECT_TRUE(result.is_valid());
    EXPECT_EQ(result.error_count(), 0);
    EXPECT_TRUE(result.errors().empty());
    EXPECT_EQ(result.error_messages(), "");
}

// Test adding errors invalidates result
TEST_F(ValidationResultTest, AddError) {
    result.add_error({"field1", "error message 1", "rule1"});
    
    EXPECT_FALSE(result.is_valid());
    EXPECT_EQ(result.error_count(), 1);
    EXPECT_EQ(result.errors().size(), 1);
    
    const auto& error = result.errors()[0];
    EXPECT_EQ(error.field_name, "field1");
    EXPECT_EQ(error.error_message, "error message 1");
    EXPECT_EQ(error.rule_name, "rule1");
}

// Test adding multiple errors
TEST_F(ValidationResultTest, AddMultipleErrors) {
    result.add_error("field1", "error1", "rule1");
    result.add_error("field2", "error2", "rule2");
    result.add_error("field3", "error3", "rule3");
    
    EXPECT_FALSE(result.is_valid());
    EXPECT_EQ(result.error_count(), 3);
}

// Test error message formatting
TEST_F(ValidationResultTest, ErrorMessageFormatting) {
    result.add_error("field1", "Value must be positive", "positive_number");
    result.add_error("field2", "Required field missing", "required");
    
    std::string messages = result.error_messages();
    EXPECT_TRUE(messages.find("field1") != std::string::npos);
    EXPECT_TRUE(messages.find("Value must be positive") != std::string::npos);
    EXPECT_TRUE(messages.find("positive_number") != std::string::npos);
    EXPECT_TRUE(messages.find("field2") != std::string::npos);
    EXPECT_TRUE(messages.find("Required field missing") != std::string::npos);
    EXPECT_TRUE(messages.find("required") != std::string::npos);
}

// Test merging validation results
TEST_F(ValidationResultTest, MergeResults) {
    ValidationResult other;
    other.add_error("field1", "error1", "rule1");
    other.add_error("field2", "error2", "rule2");
    
    result.add_error("field3", "error3", "rule3");
    result.merge(other);
    
    EXPECT_FALSE(result.is_valid());
    EXPECT_EQ(result.error_count(), 3);
}

// Test merging with valid result
TEST_F(ValidationResultTest, MergeWithValidResult) {
    result.add_error("field1", "error1", "rule1");
    
    ValidationResult valid_result;
    result.merge(valid_result);
    
    EXPECT_FALSE(result.is_valid());
    EXPECT_EQ(result.error_count(), 1);
}

// Interface concept tests
class ConceptTest : public ::testing::Test {
protected:
    Record record;
};

// Test RecordLike concept with Record class
TEST_F(ConceptTest, RecordSatisfiesRecordLikeConcept) {
    // This test verifies that Record satisfies the RecordLike concept
    static_assert(RecordLike<Record>);
    
    // Test concept requirements
    record.setField("test", 42);
    auto value = record.getField("test");
    EXPECT_EQ(std::any_cast<int>(value), 42);
    
    auto names = record.getFieldNames();
    EXPECT_EQ(names.size(), 1);
    EXPECT_EQ(names[0], "test");
}

// Mock record that doesn't satisfy RecordLike
struct NotRecordLike {
    void someMethod() {}
};

// Verify NotRecordLike doesn't satisfy concept
TEST_F(ConceptTest, NotRecordLikeFailsConcept) {
    static_assert(!RecordLike<NotRecordLike>);
}

// Test IExtractor interface
class ExtractorTest : public ::testing::Test {
protected:
    MockExtractor extractor;
    ProcessingContext context;
};

TEST_F(ExtractorTest, Initialize) {
    ConfigMap config;
    config["batch_size"] = 100;

    EXPECT_CALL(extractor, initialize(_, _)).Times(1);
    extractor.initialize(config, context);
}

TEST_F(ExtractorTest, ExtractBatch) {
    RecordBatch batch;
    EXPECT_CALL(extractor, extract_batch(100, _)).WillOnce(Return(batch));
    auto result = extractor.extract_batch(100, context);
    EXPECT_EQ(result.size(), batch.size());
}

TEST_F(ExtractorTest, HasMoreData) {
    EXPECT_CALL(extractor, has_more_data()).WillOnce(Return(true));
    EXPECT_TRUE(extractor.has_more_data());
}

TEST_F(ExtractorTest, GetType) {
    EXPECT_CALL(extractor, get_type()).WillOnce(Return("test_extractor"));
    EXPECT_EQ(extractor.get_type(), "test_extractor");
}

TEST_F(ExtractorTest, Finalize) {
    EXPECT_CALL(extractor, finalize(_)).Times(1);
    extractor.finalize(context);
}

TEST_F(ExtractorTest, GetStatistics) {
    StatsMap stats;
    stats["records_extracted"] = 1000;
    EXPECT_CALL(extractor, get_statistics()).WillOnce(Return(stats));
    auto result = extractor.get_statistics();
    EXPECT_EQ(std::any_cast<int>(result["records_extracted"]), 1000);
}

// Test ITransformer interface
class TransformerTest : public ::testing::Test {
protected:
    MockTransformer transformer;
    ProcessingContext context;
    Record record;
};

TEST_F(TransformerTest, Initialize) {
    ConfigMap config;
    config["transformation_rules"] = std::string("test_rules");

    EXPECT_CALL(transformer, initialize(_, _)).Times(1);
    transformer.initialize(config, context);
}

TEST_F(TransformerTest, Transform) {
    EXPECT_CALL(transformer, transform(_, _)).WillOnce(Return(OptionalRecord(record)));
    auto result = transformer.transform(record, context);
    EXPECT_TRUE(result.has_value());
}

TEST_F(TransformerTest, TransformBatch) {
    RecordBatch batch;
    EXPECT_CALL(transformer, transform_batch(_, _)).WillOnce(Return(batch));
    auto result = transformer.transform_batch(batch, context);
    EXPECT_EQ(result.size(), batch.size());
}

TEST_F(TransformerTest, GetType) {
    EXPECT_CALL(transformer, get_type()).WillOnce(Return("test_transformer"));
    EXPECT_EQ(transformer.get_type(), "test_transformer");
}

TEST_F(TransformerTest, Validate) {
    ValidationResult valid_result;
    EXPECT_CALL(transformer, validate(_)).WillOnce(Return(valid_result));
    auto result = transformer.validate(record);
    EXPECT_TRUE(result.is_valid());
}

TEST_F(TransformerTest, GetStatistics) {
    StatsMap stats;
    stats["records_transformed"] = 950;
    stats["records_failed"] = 50;
    EXPECT_CALL(transformer, get_statistics()).WillOnce(Return(stats));
    auto result = transformer.get_statistics();
    EXPECT_EQ(std::any_cast<int>(result["records_transformed"]), 950);
}

// Test ILoader interface
class LoaderTest : public ::testing::Test {
protected:
    MockLoader loader;
    ProcessingContext context;
    Record record;
};

TEST_F(LoaderTest, Initialize) {
    ConfigMap config;
    config["connection_string"] = std::string("test_connection");

    EXPECT_CALL(loader, initialize(_, _)).Times(1);
    loader.initialize(config, context);
}

TEST_F(LoaderTest, Load) {
    EXPECT_CALL(loader, load(_, _)).WillOnce(Return(true));
    EXPECT_TRUE(loader.load(record, context));
}

TEST_F(LoaderTest, LoadBatch) {
    RecordBatch batch;
    batch.addRecord(record);
    EXPECT_CALL(loader, load_batch(_, _)).WillOnce(Return(batch.size()));
    size_t loaded = loader.load_batch(batch, context);
    EXPECT_EQ(loaded, batch.size());
}

TEST_F(LoaderTest, GetType) {
    EXPECT_CALL(loader, get_type()).WillOnce(Return("test_loader"));
    EXPECT_EQ(loader.get_type(), "test_loader");
}

TEST_F(LoaderTest, CommitAndRollback) {
    EXPECT_CALL(loader, commit(_)).Times(1);
    EXPECT_CALL(loader, rollback(_)).Times(1);

    loader.commit(context);
    loader.rollback(context);
}

TEST_F(LoaderTest, Finalize) {
    EXPECT_CALL(loader, finalize(_)).Times(1);
    loader.finalize(context);
}

TEST_F(LoaderTest, GetStatistics) {
    StatsMap stats;
    stats["records_loaded"] = 900;
    stats["records_failed"] = 100;
    EXPECT_CALL(loader, get_statistics()).WillOnce(Return(stats));
    auto result = loader.get_statistics();
    EXPECT_EQ(std::any_cast<int>(result["records_loaded"]), 900);
}

// ComponentFactory tests
class ComponentFactoryTest : public ::testing::Test {
protected:
    ComponentFactory<IExtractor> factory;
};

// Test registering and creating components
TEST_F(ComponentFactoryTest, RegisterAndCreate) {
    factory.register_creator("mock", []() {
        return std::make_unique<MockExtractor>();
    });

    EXPECT_TRUE(factory.is_registered("mock"));
    EXPECT_EQ(factory.registered_count(), 1);

    auto extractor = factory.create("mock");
    EXPECT_NE(extractor, nullptr);
}

// Test creating unregistered component throws
TEST_F(ComponentFactoryTest, CreateUnregisteredThrows) {
    EXPECT_THROW(factory.create("unknown"), common::ConfigurationException);
}

// Test getting registered types
TEST_F(ComponentFactoryTest, GetRegisteredTypes) {
    factory.register_creator("type1", []() { return std::make_unique<MockExtractor>(); });
    factory.register_creator("type2", []() { return std::make_unique<MockExtractor>(); });
    factory.register_creator("type3", []() { return std::make_unique<MockExtractor>(); });

    auto types = factory.get_registered_types();
    EXPECT_EQ(types.size(), 3);
    EXPECT_TRUE(std::find(types.begin(), types.end(), "type1") != types.end());
    EXPECT_TRUE(std::find(types.begin(), types.end(), "type2") != types.end());
    EXPECT_TRUE(std::find(types.begin(), types.end(), "type3") != types.end());
}

// Test checking if type is registered
TEST_F(ComponentFactoryTest, IsRegistered) {
    EXPECT_FALSE(factory.is_registered("test"));

    factory.register_creator("test", []() {
        return std::make_unique<MockExtractor>();
    });

    EXPECT_TRUE(factory.is_registered("test"));
}

// Test registered count
TEST_F(ComponentFactoryTest, RegisteredCount) {
    EXPECT_EQ(factory.registered_count(), 0);

    factory.register_creator("type1", []() { return std::make_unique<MockExtractor>(); });
    EXPECT_EQ(factory.registered_count(), 1);

    factory.register_creator("type2", []() { return std::make_unique<MockExtractor>(); });
    EXPECT_EQ(factory.registered_count(), 2);
}

// Test overwriting registered type
TEST_F(ComponentFactoryTest, OverwriteRegisteredType) {
    int creation_count = 0;

    factory.register_creator("test", [&creation_count]() {
        creation_count = 1;
        return std::make_unique<MockExtractor>();
    });

    factory.register_creator("test", [&creation_count]() {
        creation_count = 2;
        return std::make_unique<MockExtractor>();
    });

    auto extractor = factory.create("test");
    EXPECT_EQ(creation_count, 2); // Second creator was used
}

} // namespace omop::core
