/**
 * @file test_record.cpp
 * @brief Unit tests for Record and RecordBatch classes
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include "core/record.h"
#include <chrono>
#include <thread>

using namespace omop::core;

// Record tests
class RecordTest : public ::testing::Test {
protected:
    Record record;

    void SetUp() override {
        // Set up test data
        record.setField("int_field", 42);
        record.setField("string_field", std::string("test"));
        record.setField("double_field", 3.14);
        record.setField("bool_field", true);
    }
};

// Test default constructor
TEST_F(RecordTest, DefaultConstructor) {
    Record r;
    EXPECT_TRUE(r.isEmpty());
    EXPECT_EQ(r.getFieldCount(), 0);
    EXPECT_TRUE(r.getFieldNames().empty());
}

// Test constructor with initial data
TEST_F(RecordTest, ConstructorWithData) {
    std::unordered_map<std::string, std::any> data;
    data["field1"] = 100;
    data["field2"] = std::string("value");

    Record r(data);
    EXPECT_FALSE(r.isEmpty());
    EXPECT_EQ(r.getFieldCount(), 2);
    EXPECT_EQ(std::any_cast<int>(r.getField("field1")), 100);
    EXPECT_EQ(std::any_cast<std::string>(r.getField("field2")), "value");
}

// Test constructor with data and metadata
TEST_F(RecordTest, ConstructorWithDataAndMetadata) {
    std::unordered_map<std::string, std::any> data;
    data["field1"] = 100;

    Record::RecordMetadata metadata;
    metadata.source_table = "source_table";
    metadata.target_table = "target_table";
    metadata.source_row_number = 10;
    metadata.record_id = "rec-123";

    Record r(data, metadata);
    EXPECT_EQ(r.getMetadata().source_table, "source_table");
    EXPECT_EQ(r.getMetadata().target_table, "target_table");
    EXPECT_EQ(r.getMetadata().source_row_number, 10);
    EXPECT_EQ(r.getMetadata().record_id, "rec-123");
}

// Test setting and getting fields
TEST_F(RecordTest, SetAndGetField) {
    record.setField("new_field", 999);
    EXPECT_EQ(std::any_cast<int>(record.getField("new_field")), 999);

    // Overwrite existing field
    record.setField("int_field", 100);
    EXPECT_EQ(std::any_cast<int>(record.getField("int_field")), 100);
}

// Test getting field that doesn't exist throws
TEST_F(RecordTest, GetNonExistentFieldThrows) {
    EXPECT_THROW(record.getField("nonexistent"), std::out_of_range);
}

// Test getFieldAs with type conversion
TEST_F(RecordTest, GetFieldAs) {
    EXPECT_EQ(record.getFieldAs<int>("int_field"), 42);
    EXPECT_EQ(record.getFieldAs<std::string>("string_field"), "test");
    EXPECT_DOUBLE_EQ(record.getFieldAs<double>("double_field"), 3.14);
    EXPECT_EQ(record.getFieldAs<bool>("bool_field"), true);
}

// Test getFieldAs with wrong type throws
TEST_F(RecordTest, GetFieldAsWrongTypeThrows) {
    EXPECT_THROW(record.getFieldAs<int>("string_field"), std::bad_any_cast);
}

// Test getFieldOptional
TEST_F(RecordTest, GetFieldOptional) {
    auto value = record.getFieldOptional("int_field");
    ASSERT_TRUE(value.has_value());
    EXPECT_EQ(std::any_cast<int>(value.value()), 42);

    auto none = record.getFieldOptional("nonexistent");
    EXPECT_FALSE(none.has_value());
}

// Test hasField
TEST_F(RecordTest, HasField) {
    EXPECT_TRUE(record.hasField("int_field"));
    EXPECT_TRUE(record.hasField("string_field"));
    EXPECT_FALSE(record.hasField("nonexistent"));
}

// Test isFieldNull
TEST_F(RecordTest, IsFieldNull) {
    record.setField("null_field", std::any{});
    EXPECT_TRUE(record.isFieldNull("null_field"));
    EXPECT_FALSE(record.isFieldNull("int_field"));
    EXPECT_TRUE(record.isFieldNull("nonexistent"));
}

// Test removeField
TEST_F(RecordTest, RemoveField) {
    EXPECT_TRUE(record.hasField("int_field"));
    EXPECT_TRUE(record.removeField("int_field"));
    EXPECT_FALSE(record.hasField("int_field"));
    EXPECT_FALSE(record.removeField("int_field")); // Already removed
}

// Test clear
TEST_F(RecordTest, Clear) {
    EXPECT_FALSE(record.isEmpty());
    record.clear();
    EXPECT_TRUE(record.isEmpty());
    EXPECT_EQ(record.getFieldCount(), 0);
}

// Test getFieldNames
TEST_F(RecordTest, GetFieldNames) {
    auto names = record.getFieldNames();
    EXPECT_EQ(names.size(), 4);
    EXPECT_TRUE(std::find(names.begin(), names.end(), "int_field") != names.end());
    EXPECT_TRUE(std::find(names.begin(), names.end(), "string_field") != names.end());
    EXPECT_TRUE(std::find(names.begin(), names.end(), "double_field") != names.end());
    EXPECT_TRUE(std::find(names.begin(), names.end(), "bool_field") != names.end());
}

// Test getFields and getFieldsMutable
TEST_F(RecordTest, GetFields) {
    const auto& fields = record.getFields();
    EXPECT_EQ(fields.size(), 4);

    auto& mutable_fields = record.getFieldsMutable();
    mutable_fields["new_field"] = 123;
    EXPECT_TRUE(record.hasField("new_field"));
}

// Test field metadata
TEST_F(RecordTest, FieldMetadata) {
    Record::FieldMetadata metadata;
    metadata.name = "test_field";
    metadata.data_type = "integer";
    metadata.is_nullable = false;
    metadata.source_column = "src_col";
    metadata.description = "Test field";

    record.setFieldMetadata("int_field", metadata);

    auto retrieved = record.getFieldMetadata("int_field");
    ASSERT_TRUE(retrieved.has_value());
    EXPECT_EQ(retrieved->name, "test_field");
    EXPECT_EQ(retrieved->data_type, "integer");
    EXPECT_FALSE(retrieved->is_nullable);
    EXPECT_EQ(retrieved->source_column, "src_col");
    EXPECT_EQ(retrieved->description, "Test field");

    auto none = record.getFieldMetadata("nonexistent");
    EXPECT_FALSE(none.has_value());
}

// Test record metadata
TEST_F(RecordTest, RecordMetadata) {
    Record::RecordMetadata metadata;
    metadata.source_table = "patients";
    metadata.target_table = "person";
    metadata.source_row_number = 100;
    metadata.record_id = "rec-456";
    metadata.custom["key1"] = "value1";

    record.setMetadata(metadata);

    const auto& retrieved = record.getMetadata();
    EXPECT_EQ(retrieved.source_table, "patients");
    EXPECT_EQ(retrieved.target_table, "person");
    EXPECT_EQ(retrieved.source_row_number, 100);
    EXPECT_EQ(retrieved.record_id, "rec-456");
    EXPECT_EQ(retrieved.custom.at("key1"), "value1");
}

// Test merge records
TEST_F(RecordTest, MergeRecords) {
    Record other;
    other.setField("int_field", 100); // Existing field
    other.setField("new_field", std::string("new"));

    record.merge(other, true); // Overwrite = true
    EXPECT_EQ(std::any_cast<int>(record.getField("int_field")), 100);
    EXPECT_EQ(std::any_cast<std::string>(record.getField("new_field")), "new");

    Record another;
    another.setField("int_field", 200);
    another.setField("another_field", 300);

    record.merge(another, false); // Overwrite = false
    EXPECT_EQ(std::any_cast<int>(record.getField("int_field")), 100); // Not overwritten
    EXPECT_EQ(std::any_cast<int>(record.getField("another_field")), 300);
}

// Test selectFields
TEST_F(RecordTest, SelectFields) {
    std::vector<std::string> fields_to_select = {"int_field", "string_field"};
    Record selected = record.selectFields(fields_to_select);

    EXPECT_EQ(selected.getFieldCount(), 2);
    EXPECT_TRUE(selected.hasField("int_field"));
    EXPECT_TRUE(selected.hasField("string_field"));
    EXPECT_FALSE(selected.hasField("double_field"));
    EXPECT_FALSE(selected.hasField("bool_field"));
}

// Test renameField
TEST_F(RecordTest, RenameField) {
    EXPECT_TRUE(record.renameField("int_field", "renamed_field"));
    EXPECT_FALSE(record.hasField("int_field"));
    EXPECT_TRUE(record.hasField("renamed_field"));
    EXPECT_EQ(std::any_cast<int>(record.getField("renamed_field")), 42);

    // Can't rename to existing field
    EXPECT_FALSE(record.renameField("string_field", "renamed_field"));

    // Can't rename non-existent field
    EXPECT_FALSE(record.renameField("nonexistent", "new_name"));
}

// Test JSON serialization and deserialization
TEST_F(RecordTest, JsonSerialization) {
    // Add metadata for complete test
    Record::RecordMetadata metadata;
    metadata.source_table = "test_source";
    metadata.target_table = "test_target";
    metadata.source_row_number = 50;
    metadata.record_id = "test-123";
    metadata.custom["custom_key"] = "custom_value";
    record.setMetadata(metadata);

    // Test compact JSON
    std::string json = record.toJson(false);
    EXPECT_FALSE(json.empty());

    // Test pretty JSON
    std::string pretty_json = record.toJson(true);
    EXPECT_FALSE(pretty_json.empty());
    EXPECT_GT(pretty_json.length(), json.length());

    // Test deserialization
    Record deserialized = Record::fromJson(json);
    EXPECT_EQ(deserialized.getFieldCount(), record.getFieldCount());
    EXPECT_EQ(std::any_cast<int64_t>(deserialized.getField("int_field")), 42);
    EXPECT_EQ(std::any_cast<std::string>(deserialized.getField("string_field")), "test");
    EXPECT_DOUBLE_EQ(std::any_cast<double>(deserialized.getField("double_field")), 3.14);
    EXPECT_EQ(std::any_cast<bool>(deserialized.getField("bool_field")), true);

    EXPECT_EQ(deserialized.getMetadata().source_table, "test_source");
    EXPECT_EQ(deserialized.getMetadata().target_table, "test_target");
}

// Test toString
TEST_F(RecordTest, ToString) {
    record.getMetadataMutable().record_id = "test-id";
    record.getMetadataMutable().source_table = "source";
    record.getMetadataMutable().target_table = "target";

    std::string str = record.toString();
    EXPECT_TRUE(str.find("test-id") != std::string::npos);
    EXPECT_TRUE(str.find("fields=4") != std::string::npos);
    EXPECT_TRUE(str.find("source") != std::string::npos);
    EXPECT_TRUE(str.find("target") != std::string::npos);
}

// Test equality operators
TEST_F(RecordTest, EqualityOperators) {
    Record r1;
    r1.setField("field1", 42);
    r1.setField("field2", std::string("test"));

    Record r2;
    r2.setField("field1", 42);
    r2.setField("field2", std::string("test"));

    EXPECT_EQ(r1, r2);
    EXPECT_FALSE(r1 != r2);

    r2.setField("field1", 43);
    EXPECT_NE(r1, r2);
    EXPECT_TRUE(r1 != r2);

    Record r3;
    r3.setField("field1", 42);
    EXPECT_NE(r1, r3); // Different number of fields
}

// Test handling different data types
TEST_F(RecordTest, DifferentDataTypes) {
    Record r;

    // Test various integer types
    r.setField("int32", int32_t(123));
    r.setField("int64", int64_t(456));
    r.setField("float", float(1.23f));
    r.setField("char_ptr", "test string");

    auto time_point = std::chrono::system_clock::now();
    r.setField("time", time_point);

    // Verify types are preserved
    EXPECT_EQ(std::any_cast<int32_t>(r.getField("int32")), 123);
    EXPECT_EQ(std::any_cast<int64_t>(r.getField("int64")), 456);
    EXPECT_FLOAT_EQ(std::any_cast<float>(r.getField("float")), 1.23f);

    // JSON serialization should handle these types
    std::string json = r.toJson();
    EXPECT_FALSE(json.empty());
}

// RecordBatch tests
class RecordBatchTest : public ::testing::Test {
protected:
    RecordBatch batch;

    void SetUp() override {
        for (int i = 0; i < 5; ++i) {
            Record r;
            r.setField("id", i);
            r.setField("value", i * 10);
            batch.addRecord(r);
        }
    }
};

// Test default constructor
TEST_F(RecordBatchTest, DefaultConstructor) {
    RecordBatch b;
    EXPECT_TRUE(b.isEmpty());
    EXPECT_TRUE(b.empty());
    EXPECT_EQ(b.size(), 0);
}

// Test constructor with capacity
TEST_F(RecordBatchTest, ConstructorWithCapacity) {
    RecordBatch b(100);
    EXPECT_TRUE(b.isEmpty());
    // Capacity is reserved but size is still 0
    EXPECT_EQ(b.size(), 0);
}

// Test adding records
TEST_F(RecordBatchTest, AddRecords) {
    RecordBatch b;

    Record r1;
    r1.setField("field", 1);
    b.addRecord(r1);

    Record r2;
    r2.setField("field", 2);
    b.addRecord(std::move(r2)); // Move semantics

    EXPECT_EQ(b.size(), 2);
    EXPECT_FALSE(b.isEmpty());
}

// Test getting records
TEST_F(RecordBatchTest, GetRecords) {
    EXPECT_EQ(batch.size(), 5);

    const Record& r = batch.getRecord(2);
    EXPECT_EQ(std::any_cast<int>(r.getField("id")), 2);
    EXPECT_EQ(std::any_cast<int>(r.getField("value")), 20);

    Record& mr = batch.getRecordMutable(3);
    mr.setField("value", 35);
    EXPECT_EQ(std::any_cast<int>(batch.getRecord(3).getField("value")), 35);
}

// Test getting record with invalid index throws
TEST_F(RecordBatchTest, GetRecordInvalidIndexThrows) {
    EXPECT_THROW(batch.getRecord(10), std::out_of_range);
    EXPECT_THROW(batch.getRecordMutable(10), std::out_of_range);
}

// Test getRecords and getRecordsMutable
TEST_F(RecordBatchTest, GetAllRecords) {
    const auto& records = batch.getRecords();
    EXPECT_EQ(records.size(), 5);

    auto& mutable_records = batch.getRecordsMutable();
    Record new_record;
    new_record.setField("id", 99);
    mutable_records.push_back(new_record);

    EXPECT_EQ(batch.size(), 6);
}

// Test clear
TEST_F(RecordBatchTest, Clear) {
    EXPECT_FALSE(batch.isEmpty());
    batch.clear();
    EXPECT_TRUE(batch.isEmpty());
    EXPECT_EQ(batch.size(), 0);
}

// Test reserve
TEST_F(RecordBatchTest, Reserve) {
    RecordBatch b;
    b.reserve(1000);
    // Reserve doesn't change size
    EXPECT_EQ(b.size(), 0);

    // But adding many records should be efficient
    for (int i = 0; i < 100; ++i) {
        Record r;
        r.setField("id", i);
        b.addRecord(r);
    }
    EXPECT_EQ(b.size(), 100);
}

// Test iterator support
TEST_F(RecordBatchTest, IteratorSupport) {
    int count = 0;
    for (auto& record : batch) {
        EXPECT_EQ(std::any_cast<int>(record.getField("id")), count);
        count++;
    }
    EXPECT_EQ(count, 5);

    // Const iterator
    const RecordBatch& const_batch = batch;
    count = 0;
    for (const auto& record : const_batch) {
        EXPECT_EQ(std::any_cast<int>(record.getField("id")), count);
        count++;
    }
}

// Test batch operations
TEST_F(RecordBatchTest, BatchOperations) {
    // Modify all records in batch
    for (auto& record : batch) {
        int id = std::any_cast<int>(record.getField("id"));
        record.setField("doubled", id * 2);
    }

    // Verify modifications
    for (size_t i = 0; i < batch.size(); ++i) {
        const auto& record = batch.getRecord(i);
        int id = std::any_cast<int>(record.getField("id"));
        int doubled = std::any_cast<int>(record.getField("doubled"));
        EXPECT_EQ(doubled, id * 2);
    }
}
